using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Antlr4.Runtime.Tree;
using Antlr4.Runtime;

namespace VbDeobf.AST
{
    /// <summary>
    /// Visitor that converts ANTLR CST to custom AST
    /// </summary>
    public class CSTToASTVisitor : VBAParserBaseVisitor<IASTNode>
    {
        private string? _fileName;

        public CSTToASTVisitor(string? fileName = null)
        {
            _fileName = fileName;
        }

        /// <summary>
        /// Create source location from parse tree context
        /// </summary>
        private SourceLocation CreateSourceLocation(IParseTree context)
        {
            if (context is ParserRuleContext ruleContext)
            {
                var start = ruleContext.Start;
                var stop = ruleContext.Stop ?? start;
                return SourceLocation.FromTokens(start, stop, _fileName);
            }
            return new SourceLocation(0, 0, 0, 0, _fileName);
        }

        /// <summary>
        /// Visit start rule - entry point
        /// </summary>
        public override IASTNode VisitStartRule(VBAParser.StartRuleContext context)
        {
            return Visit(context.module());
        }

        /// <summary>
        /// Visit module
        /// </summary>
        public override IASTNode VisitModule(VBAParser.ModuleContext context)
        {
            var declarations = new ModuleDeclarationsNode(CreateSourceLocation(context));
            var body = new ModuleBodyNode(CreateSourceLocation(context));
            var module = new ModuleNode(declarations, body, CreateSourceLocation(context));

            // Process module header if present
            if (context.moduleHeader() != null)
            {
                var header = (ModuleHeaderNode)Visit(context.moduleHeader());
                module.SetHeader(header);
            }

            // Process module declarations
            if (context.moduleDeclarations() != null)
            {
                var declsNode = (ModuleDeclarationsNode)Visit(context.moduleDeclarations());
                module.Declarations = declsNode;
            }

            // Process module body
            if (context.moduleBody() != null)
            {
                var bodyNode = (ModuleBodyNode)Visit(context.moduleBody());
                module.Body = bodyNode;
            }

            return module;
        }

        /// <summary>
        /// Visit module header
        /// </summary>
        public override IASTNode VisitModuleHeader(VBAParser.ModuleHeaderContext context)
        {
            var version = context.numberLiteral()?.GetText() ?? "1.0";
            var isClass = context.CLASS() != null;
            return new ModuleHeaderNode(version, isClass, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit module declarations
        /// </summary>
        public override IASTNode VisitModuleDeclarations(VBAParser.ModuleDeclarationsContext context)
        {
            var declarations = new ModuleDeclarationsNode(CreateSourceLocation(context));

            foreach (var element in context.moduleDeclarationsElement())
            {
                var node = Visit(element);
                if (node is DeclarationNode decl)
                {
                    declarations.AddDeclaration(decl);
                }
                else if (node is ModuleOptionNode option)
                {
                    declarations.AddOption(option);
                }
                else if (node is DeclareStatementNode declare)
                {
                    declarations.AddDeclareStatement(declare);
                }
            }

            return declarations;
        }

        /// <summary>
        /// Visit module body
        /// </summary>
        public override IASTNode VisitModuleBody(VBAParser.ModuleBodyContext context)
        {
            var body = new ModuleBodyNode(CreateSourceLocation(context));

            foreach (var element in context.moduleBodyElement())
            {
                var node = Visit(element);
                if (node is DeclarationNode procedure)
                {
                    body.AddProcedure(procedure);
                }
            }

            return body;
        }

        /// <summary>
        /// Visit function statement
        /// </summary>
        public override IASTNode VisitFunctionStmt(VBAParser.FunctionStmtContext context)
        {
            var name = context.functionName().GetText();
            var parameters = context.argList() != null ?
                (ParameterListNode)Visit(context.argList()) :
                new ParameterListNode(CreateSourceLocation(context));

            var body = context.block() != null ?
                (BlockNode)Visit(context.block()) :
                new BlockNode(CreateSourceLocation(context));

            TypeNode? returnType = null;
            if (context.asTypeClause() != null)
            {
                returnType = (TypeNode)Visit(context.asTypeClause());
            }

            var function = new FunctionDeclarationNode(name, parameters, body, returnType, CreateSourceLocation(context));

            // Handle visibility
            if (context.visibility() != null)
            {
                function.Visibility = GetVisibilityModifier(context.visibility().GetText());
            }

            // Handle static
            function.IsStatic = context.STATIC() != null;

            return function;
        }

        /// <summary>
        /// Visit subroutine statement
        /// </summary>
        public override IASTNode VisitSubStmt(VBAParser.SubStmtContext context)
        {
            var name = context.subroutineName().GetText();
            var parameters = context.argList() != null ?
                (ParameterListNode)Visit(context.argList()) :
                new ParameterListNode(CreateSourceLocation(context));

            var body = context.block() != null ?
                (BlockNode)Visit(context.block()) :
                new BlockNode(CreateSourceLocation(context));

            var subroutine = new SubroutineDeclarationNode(name, parameters, body, CreateSourceLocation(context));

            // Handle visibility
            if (context.visibility() != null)
            {
                subroutine.Visibility = GetVisibilityModifier(context.visibility().GetText());
            }

            // Handle static
            subroutine.IsStatic = context.STATIC() != null;

            return subroutine;
        }

        /// <summary>
        /// Visit property statement
        /// </summary>
        public override IASTNode VisitPropertyGetStmt(VBAParser.PropertyGetStmtContext context)
        {
            var name = context.functionName().GetText();
            var parameters = context.argList() != null ?
                (ParameterListNode)Visit(context.argList()) :
                new ParameterListNode(CreateSourceLocation(context));

            var body = context.block() != null ?
                (BlockNode)Visit(context.block()) :
                new BlockNode(CreateSourceLocation(context));

            TypeNode? returnType = null;
            if (context.asTypeClause() != null)
            {
                returnType = (TypeNode)Visit(context.asTypeClause());
            }

            var property = new PropertyDeclarationNode(name, PropertyType.Get, parameters, body, returnType, CreateSourceLocation(context));

            // Handle visibility
            if (context.visibility() != null)
            {
                property.Visibility = GetVisibilityModifier(context.visibility().GetText());
            }

            return property;
        }

        /// <summary>
        /// Visit argument list
        /// </summary>
        public override IASTNode VisitArgList(VBAParser.ArgListContext context)
        {
            var paramList = new ParameterListNode(CreateSourceLocation(context));

            foreach (var arg in context.arg())
            {
                var param = (ParameterNode)Visit(arg);
                paramList.AddParameter(param);
            }

            return paramList;
        }

        /// <summary>
        /// Visit argument
        /// </summary>
        public override IASTNode VisitArg(VBAParser.ArgContext context)
        {
            var name = context.unrestrictedIdentifier().GetText();

            TypeNode? type = null;
            if (context.asTypeClause() != null)
            {
                type = (TypeNode)Visit(context.asTypeClause());
            }

            ExpressionNode? defaultValue = null;
            if (context.argDefaultValue() != null)
            {
                defaultValue = (ExpressionNode)Visit(context.argDefaultValue().expression());
            }

            var param = new ParameterNode(name, type, defaultValue, CreateSourceLocation(context));

            // Handle modifiers
            if (context.OPTIONAL() != null)
            {
                param.IsOptional = true;
            }

            if (context.BYVAL() != null)
            {
                param.Modifier = ParameterModifier.ByVal;
            }
            else if (context.BYREF() != null)
            {
                param.Modifier = ParameterModifier.ByRef;
            }

            if (context.PARAMARRAY() != null)
            {
                param.IsParamArray = true;
            }

            return param;
        }

        /// <summary>
        /// Visit block
        /// </summary>
        public override IASTNode VisitBlock(VBAParser.BlockContext context)
        {
            var block = new BlockNode(CreateSourceLocation(context));

            foreach (var stmt in context.blockStmt())
            {
                var statement = Visit(stmt);
                if (statement is StatementNode statementNode)
                {
                    block.AddStatement(statementNode);
                }
            }

            return block;
        }

        /// <summary>
        /// Get visibility modifier from text
        /// </summary>
        private VisibilityModifier GetVisibilityModifier(string text)
        {
            return text.ToUpperInvariant() switch
            {
                "PUBLIC" => VisibilityModifier.Public,
                "PRIVATE" => VisibilityModifier.Private,
                "FRIEND" => VisibilityModifier.Friend,
                "GLOBAL" => VisibilityModifier.Global,
                _ => VisibilityModifier.Public
            };
        }

        /// <summary>
        /// Visit parenthesized expression
        /// </summary>
        public override IASTNode VisitParenthesizedExpr(VBAParser.ParenthesizedExprContext context)
        {
            var expr = (ExpressionNode)Visit(context.expression());
            return new ParenthesizedExpressionNode(expr, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit new expression
        /// </summary>
        public override IASTNode VisitNewExpr(VBAParser.NewExprContext context)
        {
            var type = (ExpressionNode)Visit(context.expression());
            return new NewExpressionNode(type, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit typeof expression
        /// </summary>
        public override IASTNode VisitTypeofexpr(VBAParser.TypeofexprContext context)
        {
            var expr = (ExpressionNode)Visit(context.expression());
            return new TypeOfExpressionNode(expr, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit lExpr (left-hand side expressions)
        /// </summary>
        public override IASTNode VisitLExpr(VBAParser.LExprContext context)
        {
            return Visit(context.lExpression());
        }

        /// <summary>
        /// Visit add operation
        /// </summary>
        public override IASTNode VisitAddOp(VBAParser.AddOpContext context)
        {
            var left = (ExpressionNode)Visit(context.expression(0));
            var right = (ExpressionNode)Visit(context.expression(1));
            var op = context.PLUS() != null ? BinaryOperator.Add : BinaryOperator.Subtract;
            return new BinaryExpressionNode(left, op, right, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit multiply operation
        /// </summary>
        public override IASTNode VisitMultOp(VBAParser.MultOpContext context)
        {
            var left = (ExpressionNode)Visit(context.expression(0));
            var right = (ExpressionNode)Visit(context.expression(1));
            var op = context.MULT() != null ? BinaryOperator.Multiply : BinaryOperator.Divide;
            return new BinaryExpressionNode(left, op, right, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit concatenation operation
        /// </summary>
        public override IASTNode VisitConcatOp(VBAParser.ConcatOpContext context)
        {
            var left = (ExpressionNode)Visit(context.expression(0));
            var right = (ExpressionNode)Visit(context.expression(1));
            return new BinaryExpressionNode(left, BinaryOperator.Concatenate, right, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit relational operation
        /// </summary>
        public override IASTNode VisitRelationalOp(VBAParser.RelationalOpContext context)
        {
            var left = (ExpressionNode)Visit(context.expression(0));
            var right = (ExpressionNode)Visit(context.expression(1));
            var op = GetRelationalOperator(context);
            return new BinaryExpressionNode(left, op, right, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit logical AND operation
        /// </summary>
        public override IASTNode VisitLogicalAndOp(VBAParser.LogicalAndOpContext context)
        {
            var left = (ExpressionNode)Visit(context.expression(0));
            var right = (ExpressionNode)Visit(context.expression(1));
            return new BinaryExpressionNode(left, BinaryOperator.And, right, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit logical OR operation
        /// </summary>
        public override IASTNode VisitLogicalOrOp(VBAParser.LogicalOrOpContext context)
        {
            var left = (ExpressionNode)Visit(context.expression(0));
            var right = (ExpressionNode)Visit(context.expression(1));
            return new BinaryExpressionNode(left, BinaryOperator.Or, right, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit unary minus operation
        /// </summary>
        public override IASTNode VisitUnaryMinusOp(VBAParser.UnaryMinusOpContext context)
        {
            var operand = (ExpressionNode)Visit(context.expression());
            return new UnaryExpressionNode(UnaryOperator.Minus, operand, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit logical NOT operation
        /// </summary>
        public override IASTNode VisitLogicalNotOp(VBAParser.LogicalNotOpContext context)
        {
            var operand = (ExpressionNode)Visit(context.expression());
            return new UnaryExpressionNode(UnaryOperator.Not, operand, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit literal expression
        /// </summary>
        public override IASTNode VisitLiteralExpression(VBAParser.LiteralExpressionContext context)
        {
            if (context.STRINGLITERAL() != null)
            {
                var value = context.STRINGLITERAL().GetText();
                // Remove quotes
                value = value.Substring(1, value.Length - 2);
                return new StringLiteralNode(value, CreateSourceLocation(context));
            }
            else if (context.numberLiteral() != null)
            {
                return VisitNumberLiteral(context.numberLiteral());
            }
            else if (context.DATELITERAL() != null)
            {
                var dateText = context.DATELITERAL().GetText();
                // Remove # symbols and parse date
                dateText = dateText.Substring(1, dateText.Length - 2);
                if (DateTime.TryParse(dateText, out var date))
                {
                    return new DateLiteralNode(date, CreateSourceLocation(context));
                }
                return new StringLiteralNode(dateText, CreateSourceLocation(context));
            }
            else if (context.literalIdentifier() != null)
            {
                return VisitLiteralIdentifier(context.literalIdentifier());
            }

            return new StringLiteralNode("", CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit number literal
        /// </summary>
        public override IASTNode VisitNumberLiteral(VBAParser.NumberLiteralContext context)
        {
            var text = context.GetText();

            // Try to parse as different number types
            if (int.TryParse(text, out var intValue))
            {
                return new NumberLiteralNode(intValue, NumberType.Integer, CreateSourceLocation(context));
            }
            else if (long.TryParse(text, out var longValue))
            {
                return new NumberLiteralNode(longValue, NumberType.Long, CreateSourceLocation(context));
            }
            else if (double.TryParse(text, NumberStyles.Float, CultureInfo.InvariantCulture, out var doubleValue))
            {
                return new NumberLiteralNode(doubleValue, NumberType.Double, CreateSourceLocation(context));
            }

            return new NumberLiteralNode(0, NumberType.Integer, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit literal identifier (True, False, Nothing, etc.)
        /// </summary>
        public override IASTNode VisitLiteralIdentifier(VBAParser.LiteralIdentifierContext context)
        {
            var text = context.GetText().ToUpperInvariant();

            return text switch
            {
                "TRUE" => new BooleanLiteralNode(true, CreateSourceLocation(context)),
                "FALSE" => new BooleanLiteralNode(false, CreateSourceLocation(context)),
                "NOTHING" => new NothingLiteralNode(CreateSourceLocation(context)),
                "EMPTY" => new EmptyLiteralNode(CreateSourceLocation(context)),
                "NULL" => new NullLiteralNode(CreateSourceLocation(context)),
                _ => new IdentifierExpressionNode(context.GetText(), CreateSourceLocation(context))
            };
        }

        /// <summary>
        /// Visit simple name expression
        /// </summary>
        public override IASTNode VisitSimpleNameExpr(VBAParser.SimpleNameExprContext context)
        {
            return new IdentifierExpressionNode(context.identifier().GetText(), CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit member access expression
        /// </summary>
        public override IASTNode VisitMemberAccessExpr(VBAParser.MemberAccessExprContext context)
        {
            var obj = (ExpressionNode)Visit(context.lExpression());
            var memberName = context.unrestrictedIdentifier().GetText();
            return new MemberAccessExpressionNode(obj, memberName, false, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit index expression
        /// </summary>
        public override IASTNode VisitIndexExpr(VBAParser.IndexExprContext context)
        {
            var obj = (ExpressionNode)Visit(context.lExpression());
            var args = context.argumentList() != null ?
                (ArgumentListNode)Visit(context.argumentList()) :
                new ArgumentListNode(CreateSourceLocation(context));
            return new IndexExpressionNode(obj, args, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit dictionary access expression
        /// </summary>
        public override IASTNode VisitDictionaryAccessExpr(VBAParser.DictionaryAccessExprContext context)
        {
            var obj = (ExpressionNode)Visit(context.lExpression());
            var memberName = context.unrestrictedIdentifier().GetText();
            return new MemberAccessExpressionNode(obj, memberName, true, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit instance expression (Me)
        /// </summary>
        public override IASTNode VisitInstanceExpr(VBAParser.InstanceExprContext context)
        {
            return new IdentifierExpressionNode("Me", CreateSourceLocation(context));
        }

        /// <summary>
        /// Get relational operator from context
        /// </summary>
        private BinaryOperator GetRelationalOperator(VBAParser.RelationalOpContext context)
        {
            if (context.EQ() != null) return BinaryOperator.Equal;
            if (context.NEQ() != null) return BinaryOperator.NotEqual;
            if (context.LT() != null) return BinaryOperator.LessThan;
            if (context.LEQ() != null) return BinaryOperator.LessThanOrEqual;
            if (context.GT() != null) return BinaryOperator.GreaterThan;
            if (context.GEQ() != null) return BinaryOperator.GreaterThanOrEqual;
            if (context.LIKE() != null) return BinaryOperator.Like;
            if (context.IS() != null) return BinaryOperator.Is;

            return BinaryOperator.Equal; // Default
        }

        /// <summary>
        /// Visit assignment statement (Let/Set)
        /// </summary>
        public override IASTNode VisitLetStmt(VBAParser.LetStmtContext context)
        {
            var target = (ExpressionNode)Visit(context.lExpression());
            var value = (ExpressionNode)Visit(context.expression());
            var isSetAssignment = context.LET() == null; // If no LET keyword, it might be a Set

            return new AssignmentStatementNode(target, value, isSetAssignment, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit Set statement
        /// </summary>
        public override IASTNode VisitSetStmt(VBAParser.SetStmtContext context)
        {
            var target = (ExpressionNode)Visit(context.lExpression());
            var value = (ExpressionNode)Visit(context.expression());

            return new AssignmentStatementNode(target, value, true, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit call statement
        /// </summary>
        public override IASTNode VisitCallStmt(VBAParser.CallStmtContext context)
        {
            var hasCallKeyword = context.CALL() != null;
            var expression = (ExpressionNode)Visit(context.lExpression());

            // If there are arguments, create a call expression
            if (context.argumentList() != null)
            {
                var args = (ArgumentListNode)Visit(context.argumentList());
                expression = new CallExpressionNode(expression, args, CreateSourceLocation(context));
            }

            return new CallStatementNode(expression, hasCallKeyword, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit If statement
        /// </summary>
        public override IASTNode VisitIfStmt(VBAParser.IfStmtContext context)
        {
            var condition = (ExpressionNode)Visit(context.booleanExpression());
            var thenBlock = context.block() != null ?
                (BlockNode)Visit(context.block()) :
                new BlockNode(CreateSourceLocation(context));

            var ifStmt = new IfStatementNode(condition, thenBlock, false, CreateSourceLocation(context));

            // Handle ElseIf blocks
            foreach (var elseIfCtx in context.elseIfBlock())
            {
                var elseIfCondition = (ExpressionNode)Visit(elseIfCtx.booleanExpression());
                var elseIfBlock = elseIfCtx.block() != null ?
                    (BlockNode)Visit(elseIfCtx.block()) :
                    new BlockNode(CreateSourceLocation(elseIfCtx));

                var elseIfStmt = new ElseIfStatementNode(elseIfCondition, elseIfBlock, CreateSourceLocation(elseIfCtx));
                ifStmt.AddElseIf(elseIfStmt);
            }

            // Handle Else block
            if (context.elseBlock() != null)
            {
                var elseBlock = context.elseBlock().block() != null ?
                    (BlockNode)Visit(context.elseBlock().block()) :
                    new BlockNode(CreateSourceLocation(context.elseBlock()));

                var elseStmt = new ElseStatementNode(elseBlock, CreateSourceLocation(context.elseBlock()));
                ifStmt.SetElse(elseStmt);
            }

            return ifStmt;
        }

        /// <summary>
        /// Visit For statement
        /// </summary>
        public override IASTNode VisitForNextStmt(VBAParser.ForNextStmtContext context)
        {
            var variable = (ExpressionNode)Visit(context.expression(0));
            var startValue = (ExpressionNode)Visit(context.expression(1));
            var endValue = (ExpressionNode)Visit(context.expression(2));

            ExpressionNode? stepValue = null;
            if (context.stepStmt() != null)
            {
                stepValue = (ExpressionNode)Visit(context.stepStmt().expression());
            }

            var body = context.unterminatedBlock() != null ?
                (BlockNode)Visit(context.unterminatedBlock()) :
                new BlockNode(CreateSourceLocation(context));

            return new ForStatementNode(variable, startValue, endValue, body, stepValue, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit For Each statement
        /// </summary>
        public override IASTNode VisitForEachStmt(VBAParser.ForEachStmtContext context)
        {
            var variable = (ExpressionNode)Visit(context.expression(0));
            var collection = (ExpressionNode)Visit(context.expression(1));

            var body = context.unterminatedBlock() != null ?
                (BlockNode)Visit(context.unterminatedBlock()) :
                new BlockNode(CreateSourceLocation(context));

            return new ForEachStatementNode(variable, collection, body, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit While statement
        /// </summary>
        public override IASTNode VisitWhileWendStmt(VBAParser.WhileWendStmtContext context)
        {
            var condition = (ExpressionNode)Visit(context.expression());
            var body = context.block() != null ?
                (BlockNode)Visit(context.block()) :
                new BlockNode(CreateSourceLocation(context));

            return new WhileStatementNode(condition, body, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit Do Loop statement
        /// </summary>
        public override IASTNode VisitDoLoopStmt(VBAParser.DoLoopStmtContext context)
        {
            var body = context.block() != null ?
                (BlockNode)Visit(context.block()) :
                new BlockNode(CreateSourceLocation(context));

            ExpressionNode? condition = null;
            var loopType = DoLoopType.DoLoop;
            var isConditionAtStart = true;

            // Check for While/Until conditions
            if (context.WHILE() != null)
            {
                loopType = DoLoopType.DoWhile;
                if (context.expression() != null)
                {
                    condition = (ExpressionNode)Visit(context.expression());
                }
            }
            else if (context.UNTIL() != null)
            {
                loopType = DoLoopType.DoUntil;
                if (context.expression() != null)
                {
                    condition = (ExpressionNode)Visit(context.expression());
                }
            }

            return new DoLoopStatementNode(body, loopType, condition, isConditionAtStart, CreateSourceLocation(context));
        }

        /// <summary>
        /// Visit argument list
        /// </summary>
        public override IASTNode VisitArgumentList(VBAParser.ArgumentListContext context)
        {
            var argList = new ArgumentListNode(CreateSourceLocation(context));

            foreach (var arg in context.argument())
            {
                var argNode = (ArgumentNode)Visit(arg);
                argList.AddArgument(argNode);
            }

            return argList;
        }

        /// <summary>
        /// Visit argument
        /// </summary>
        public override IASTNode VisitArgument(VBAParser.ArgumentContext context)
        {
            if (context.positionalArgument() != null)
            {
                var expr = (ExpressionNode)Visit(context.positionalArgument().argumentExpression());
                return new ArgumentNode(expr, null, CreateSourceLocation(context));
            }
            else if (context.namedArgument() != null)
            {
                var name = context.namedArgument().unrestrictedIdentifier().GetText();
                var expr = (ExpressionNode)Visit(context.namedArgument().argumentExpression());
                return new ArgumentNode(expr, name, CreateSourceLocation(context));
            }
            else if (context.missingArgument() != null)
            {
                var emptyExpr = new EmptyLiteralNode(CreateSourceLocation(context));
                var arg = new ArgumentNode(emptyExpr, null, CreateSourceLocation(context));
                arg.IsMissing = true;
                return arg;
            }

            return new ArgumentNode(new EmptyLiteralNode(CreateSourceLocation(context)), null, CreateSourceLocation(context));
        }

        /// <summary>
        /// Default visit - returns null for unhandled nodes
        /// </summary>
        protected override IASTNode DefaultResult => null!;
    }
}
