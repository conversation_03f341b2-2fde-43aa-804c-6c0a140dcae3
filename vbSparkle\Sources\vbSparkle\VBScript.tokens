ACCESS=1
ADDRESSOF=2
ALIAS=3
AND=4
ATTRIBUTE=5
APPACTIVATE=6
APPEND=7
AS=8
BEEP=9
BEGIN=10
BEGINPROPERTY=11
BINARY=12
BOOLEAN=13
BYVAL=14
BYREF=15
BYTE=16
CALL=17
CASE=18
CHDIR=19
CHDRIVE=20
CLASS=21
CLOSE=22
COLLECTION=23
CONST=24
DATE=25
PTRSAFE=26
DECLARE=27
DEFBOOL=28
DEFBYTE=29
DEFDATE=30
DEFDBL=31
DEFDEC=32
DEFCUR=33
DEFINT=34
DEFLNG=35
DEFOBJ=36
DEFSNG=37
DEFSTR=38
DEFVAR=39
DELETESETTING=40
DIM=41
DO=42
DOUBLE=43
EACH=44
ELSE=45
ELSEIF=46
END_ENUM=47
END_CLASS=48
END_FUNCTION=49
END_IF=50
END_PROPERTY=51
END_SELECT=52
END_SUB=53
END_TYPE=54
END_WITH=55
END=56
ENDPROPERTY=57
ENUM=58
EQV=59
ERASE=60
ERROR=61
EVENT=62
EXIT_DO=63
EXIT_FOR=64
EXIT_FUNCTION=65
EXIT_PROPERTY=66
EXIT_SUB=67
FALSE=68
FILECOPY=69
FRIEND=70
FOR=71
FUNCTION=72
GET=73
GLOBAL=74
GOSUB=75
GOTO=76
IF=77
IMP=78
IMPLEMENTS=79
IN=80
INPUT=81
IS=82
INTEGER=83
KILL=84
LOAD=85
LOCK=86
LONG=87
LOOP=88
LEN=89
LET=90
LIB=91
LIKE=92
LINE_INPUT=93
LOCK_READ=94
LOCK_WRITE=95
LOCK_READ_WRITE=96
LSET=97
MACRO_IF=98
MACRO_ELSEIF=99
MACRO_ELSE=100
MACRO_END_IF=101
ME=102
MID=103
MKDIR=104
MOD=105
NAME=106
NEXT=107
NEW=108
NOT=109
NOTHING=110
NULL=111
OBJECT=112
ON=113
ON_ERROR=114
ON_LOCAL_ERROR=115
OPEN=116
OPTIONAL=117
OPTION_BASE=118
OPTION_EXPLICIT=119
OPTION_COMPARE=120
OPTION_PRIVATE_MODULE=121
OR=122
OUTPUT=123
PARAMARRAY=124
PRESERVE=125
PRINT=126
PRIVATE=127
PROPERTY_GET=128
PROPERTY_LET=129
PROPERTY_SET=130
PUBLIC=131
PUT=132
RANDOM=133
RANDOMIZE=134
RAISEEVENT=135
READ=136
READ_WRITE=137
REDIM=138
REM=139
RESET=140
RESUME=141
RETURN=142
RMDIR=143
RSET=144
SAVEPICTURE=145
SAVESETTING=146
SEEK=147
SELECT=148
SENDKEYS=149
SET=150
SETATTR=151
SHARED=152
SINGLE=153
SPC=154
STATIC=155
DEFAULT=156
STEP=157
STOP=158
STRING=159
SUB=160
TAB=161
TEXT=162
THEN=163
TIME=164
TO=165
TRUE=166
TYPE=167
TYPEOF=168
UNLOAD=169
UNLOCK=170
UNTIL=171
VARIANT=172
VERSION=173
WEND=174
WHILE=175
WIDTH=176
WITH=177
WITHEVENTS=178
WRITE=179
XOR=180
AMPERSAND=181
ASSIGN=182
AT=183
COMMA=184
DIV=185
DOLLAR=186
DOT=187
EQ=188
EXCLAMATIONMARK=189
GEQ=190
GT=191
HASH=192
LEQ=193
LBRACE=194
LPAREN=195
LT=196
MINUS=197
MINUS_EQ=198
MULT=199
NEQ=200
PERCENT=201
PLUS=202
PLUS_EQ=203
POW=204
RBRACE=205
RPAREN=206
SEMICOLON=207
L_SQUARE_BRACKET=208
R_SQUARE_BRACKET=209
STRINGLITERAL=210
DATELITERAL=211
HEXLITERAL=212
INTEGERLITERAL=213
DOUBLELITERAL=214
FILENUMBER=215
OCTALLITERAL=216
GUID=217
IDENTIFIER=218
NEWLINE=219
INLINE_SEP=220
INLN=221
COMMENT=222
WS=223
LINE_CONTINUATION=224
'&'=181
':='=182
'@'=183
','=184
'$'=186
'.'=187
'='=188
'!'=189
'>='=190
'>'=191
'#'=192
'<='=193
'{'=194
'('=195
'<'=196
'-'=197
'-='=198
'*'=199
'<>'=200
'%'=201
'+'=202
'+='=203
'^'=204
'}'=205
')'=206
';'=207
'['=208
']'=209
':'=221
