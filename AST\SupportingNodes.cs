using System.Collections.Generic;

namespace VbDeobf.AST
{
    /// <summary>
    /// Parameter node
    /// </summary>
    public class ParameterNode : ASTNodeBase
    {
        public override ASTNodeType NodeType => ASTNodeType.Parameter;
        public string Name { get; set; }
        public TypeNode? Type { get; set; }
        public ExpressionNode? DefaultValue { get; set; }
        public ParameterModifier Modifier { get; set; } = ParameterModifier.None;
        public bool IsOptional { get; set; }
        public bool IsParamArray { get; set; }
        
        public ParameterNode(string name, TypeNode? type = null, ExpressionNode? defaultValue = null, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            Name = name;
            Type = type;
            DefaultValue = defaultValue;
            if (type != null) AddChild(type);
            if (defaultValue != null) AddChild(defaultValue);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitParameter(this);
    }
    
    /// <summary>
    /// Parameter list node
    /// </summary>
    public class ParameterListNode : ASTNodeBase
    {
        public override ASTNodeType NodeType => ASTNodeType.ParameterList;
        public List<ParameterNode> Parameters { get; set; } = new List<ParameterNode>();
        
        public ParameterListNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }
        
        public void AddParameter(ParameterNode parameter)
        {
            Parameters.Add(parameter);
            AddChild(parameter);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitParameterList(this);
    }
    
    /// <summary>
    /// Argument node
    /// </summary>
    public class ArgumentNode : ASTNodeBase
    {
        public override ASTNodeType NodeType => ASTNodeType.Argument;
        public ExpressionNode Expression { get; set; }
        public string? Name { get; set; } // For named arguments
        public bool IsByVal { get; set; }
        public bool IsMissing { get; set; }
        
        public ArgumentNode(ExpressionNode expression, string? name = null, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            Expression = expression;
            Name = name;
            AddChild(expression);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitArgument(this);
    }
    
    /// <summary>
    /// Argument list node
    /// </summary>
    public class ArgumentListNode : ASTNodeBase
    {
        public override ASTNodeType NodeType => ASTNodeType.ArgumentList;
        public List<ArgumentNode> Arguments { get; set; } = new List<ArgumentNode>();
        
        public ArgumentListNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }
        
        public void AddArgument(ArgumentNode argument)
        {
            Arguments.Add(argument);
            AddChild(argument);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitArgumentList(this);
    }
    
    /// <summary>
    /// Base type node
    /// </summary>
    public abstract class TypeNode : ASTNodeBase
    {
        protected TypeNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }
    }
    
    /// <summary>
    /// Built-in type node
    /// </summary>
    public class BuiltInTypeNode : TypeNode
    {
        public override ASTNodeType NodeType => ASTNodeType.BuiltInType;
        public VBAType Type { get; set; }
        
        public BuiltInTypeNode(VBAType type, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            Type = type;
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitBuiltInType(this);
    }
    
    /// <summary>
    /// User-defined type node
    /// </summary>
    public class UserDefinedTypeNode : TypeNode
    {
        public override ASTNodeType NodeType => ASTNodeType.UserDefinedType;
        public string TypeName { get; set; }
        
        public UserDefinedTypeNode(string typeName, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            TypeName = typeName;
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitUserDefinedType(this);
    }
    
    /// <summary>
    /// Array type node
    /// </summary>
    public class ArrayTypeNode : TypeNode
    {
        public override ASTNodeType NodeType => ASTNodeType.ArrayType;
        public TypeNode? ElementType { get; set; }
        public List<ArrayDimensionNode> Dimensions { get; set; } = new List<ArrayDimensionNode>();
        
        public ArrayTypeNode(TypeNode? elementType, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            ElementType = elementType;
            if (elementType != null) AddChild(elementType);
        }
        
        public void AddDimension(ArrayDimensionNode dimension)
        {
            Dimensions.Add(dimension);
            AddChild(dimension);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitArrayType(this);
    }
    
    /// <summary>
    /// Attribute node
    /// </summary>
    public class AttributeNode : ASTNodeBase
    {
        public override ASTNodeType NodeType => ASTNodeType.Attribute;
        public string Name { get; set; }
        public List<ExpressionNode> Arguments { get; set; } = new List<ExpressionNode>();
        
        public AttributeNode(string name, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            Name = name;
        }
        
        public void AddArgument(ExpressionNode argument)
        {
            Arguments.Add(argument);
            AddChild(argument);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitAttribute(this);
    }
    
    /// <summary>
    /// Attribute list node
    /// </summary>
    public class AttributeListNode : ASTNodeBase
    {
        public override ASTNodeType NodeType => ASTNodeType.AttributeList;
        public List<AttributeNode> Attributes { get; set; } = new List<AttributeNode>();
        
        public AttributeListNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }
        
        public void AddAttribute(AttributeNode attribute)
        {
            Attributes.Add(attribute);
            AddChild(attribute);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitAttributeList(this);
    }
    
    /// <summary>
    /// Visibility node
    /// </summary>
    public class VisibilityNode : ASTNodeBase
    {
        public override ASTNodeType NodeType => ASTNodeType.Visibility;
        public VisibilityModifier Visibility { get; set; }
        
        public VisibilityNode(VisibilityModifier visibility, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            Visibility = visibility;
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitVisibility(this);
    }
    
    /// <summary>
    /// Block node
    /// </summary>
    public class BlockNode : ASTNodeBase
    {
        public override ASTNodeType NodeType => ASTNodeType.Block;
        public List<StatementNode> Statements { get; set; } = new List<StatementNode>();
        
        public BlockNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }
        
        public void AddStatement(StatementNode statement)
        {
            Statements.Add(statement);
            AddChild(statement);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitBlock(this);
    }
    
    /// <summary>
    /// Identifier node
    /// </summary>
    public class IdentifierNode : ASTNodeBase
    {
        public override ASTNodeType NodeType => ASTNodeType.Identifier;
        public string Name { get; set; }
        
        public IdentifierNode(string name, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            Name = name;
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitIdentifier(this);
    }
    
    /// <summary>
    /// Comment node
    /// </summary>
    public class CommentNode : ASTNodeBase
    {
        public override ASTNodeType NodeType => ASTNodeType.Comment;
        public string Text { get; set; }
        public bool IsRem { get; set; } // true for REM comments, false for ' comments
        
        public CommentNode(string text, bool isRem = false, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            Text = text;
            IsRem = isRem;
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitComment(this);
    }
    
    /// <summary>
    /// Parameter modifiers
    /// </summary>
    public enum ParameterModifier
    {
        None,
        ByVal,
        ByRef
    }
}
