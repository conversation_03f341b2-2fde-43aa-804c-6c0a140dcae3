﻿Sub GenerateView()
    Dim deobf0, deobf1
    deobf0 = Timer
    deobf2
    deobf1 = Timer
    deobf3 = Format(deobf1 - deobf0, "0.00")
    MsgBox "数据更新完成，耗时" _
         & deobf3 & "秒"
End Sub

Private Sub deobf2()
    Dim deobf5, deobf6
    Dim deobf7 As Workbook, deobf8 As Worksheet
    deobf9 = Application.ThisWorkbook.Path & "\普通版销量.xlsx"
    Dim deobf11 As Object, deobf12
    Set deobf11 = CreateObject("scripting.dictionary")
    Set deobf7 = Application.Workbooks.Open(deobf9)
    Set deobf8 = deobf7.Sheets(1)
    deobf13 =2
    deobf14:
    If deobf13 > deobf8.Cells(Application.Rows.Count, "A").End(xlUp).Row Then GoTo deobf15
    deobf12 = deobf8.Cells(deobf13, 2)
    deobf11(deobf12) = deobf8.Cells(deobf13, "T").Value
    deobf13 = deobf13 + 1
    GoTo deobf14
    deobf15:
    deobf7.Close False
    Sheet8.Range("A2").Resize(10000, 47).Clear
    Sheet7.Range("A2").Resize(10000, 2).Copy Sheet8.Range("A2")
    deobf16 = Sheet7.Range("XFA1").End(xlToLeft).Column
    deobf17 = deobf16 - 2
    deobf18 = deobf16 - 6
    deobf19 = deobf16 - 13
    deobf20 = deobf16 - 29
    Dim deobf21 As Range
    Dim deobf22 As Range
    Dim deobf23 As Range
    Dim deobf24 As Range
    Dim deobf25 As Object, deobf26
    Set deobf25 = CreateObject("scripting.dictionary")
    Dim deobf27 As Object
    Set deobf27 = CreateObject("scripting.dictionary")
    Dim deobf28 As Object
    Set deobf28 = CreateObject("scripting.dictionary")
    Dim deobf29 As Object
    Set deobf29 = CreateObject("scripting.dictionary")
    deobf13 =2
    deobf30:
    If deobf13 > Sheet7.Cells(Application.Rows.Count, "A").End(xlUp).Row Then GoTo deobf31
    Set deobf21 = Sheet7.Cells(deobf13, deobf17).Resize(1, 3)
    Set deobf22 = Sheet7.Cells(deobf13, deobf18).Resize(1, 7)
    Set deobf23 = Sheet7.Cells(deobf13, deobf19).Resize(1, 14)
    Set deobf24 = Sheet7.Cells(deobf13, deobf20).Resize(1, 30)
    deobf26 = Sheet7.Cells(deobf13, 1)
    deobf25(deobf26) = Application.Sum(deobf21)
    deobf27(deobf26) = Application.Sum(deobf22)
    deobf28(deobf26) = Application.Sum(deobf23)
    deobf29(deobf26) = Application.Sum(deobf24)
    deobf13 = deobf13 + 1
    GoTo deobf30
    deobf31:
    deobf32 =24
    Sheet8.Activate
    Dim deobf33 As Range
    deobf34 = Application.Cells(Application.Rows.Count, 1).End(xlUp).Row
    deobf13 =2
    deobf35:
    If deobf13 > deobf34 Then GoTo deobf36
    deobf26 = Application.Cells(deobf13, 1)
    Application.Cells(deobf13, 3) = deobf32
    Application.Cells(deobf13, 5) = deobf25(deobf26)
    Application.Cells(deobf13, 6) = deobf27(deobf26)
    Application.Cells(deobf13, 7) = deobf28(deobf26)
    Application.Cells(deobf13, 8) = deobf29(deobf26)
    Application.Cells(deobf13, "I") = Format(deobf25(deobf26) / 3, "0.00")
    Application.Cells(deobf13, "J") = Format(deobf27(deobf26) / 7, "0.00")
    Application.Cells(deobf13, "K") = Format(deobf28(deobf26) / 14, "0.00")
    Application.Cells(deobf13, "L") = Format(deobf29(deobf26) / 30, "0.00")
    Set deobf33 = Application.Range("I" _
                & deobf13).Resize(1, 4)
    Application.Cells(deobf13, "M") = Format(Application.WorksheetFunction.Average(deobf33), "0.00")
    deobf13 = deobf13 + 1
    GoTo deobf35
    deobf36:
    deobf9 = Application.ThisWorkbook.Path & "\普通版销量.xlsx"
    Set deobf7 = Application.Workbooks.Open(deobf9)
    Set deobf8 = deobf7.Sheets(1)
    Dim deobf37 As Range
    Set deobf37 = deobf8.Rows("1:1")
    deobf38 = Application.Match("FBA可售", deobf37, 0)
    deobf39 = Application.Match("FBA预留", deobf37, 0)
    deobf40 = Application.Match("FBA在途", deobf37, 0)
    deobf41 = Application.Match("本地库存", deobf37, 0)
    deobf42 = Application.Match("采购在途", deobf37, 0)
    Dim deobf43 As Object
    Set deobf43 = CreateObject("scripting.dictionary")
    Dim deobf44 As Object
    Set deobf44 = CreateObject("scripting.dictionary")
    Dim deobf45 As Object
    Set deobf45 = CreateObject("scripting.dictionary")
    Dim deobf46 As Object
    Set deobf46 = CreateObject("scripting.dictionary")
    Dim deobf47 As Object
    Set deobf47 = CreateObject("scripting.dictionary")
    deobf34 = deobf8.Cells(Application.Rows.Count, 1).End(xlUp).Row
    deobf13 =2
    deobf48:
    If deobf13 > deobf34 Then GoTo deobf49
    deobf26 = deobf8.Cells(deobf13, 2)
    deobf43(deobf26) = deobf8.Cells(deobf13, deobf38)
    deobf44(deobf26) = deobf8.Cells(deobf13, deobf39)
    deobf45(deobf26) = deobf8.Cells(deobf13, deobf40)
    deobf46(deobf26) = deobf8.Cells(deobf13, deobf41)
    deobf47(deobf26) = deobf8.Cells(deobf13, deobf42)
    deobf13 = deobf13 + 1
    GoTo deobf48
    deobf49:
    deobf7.Close False
    Dim deobf50 As Object, deobf51
    Set deobf50 = CreateObject("scripting.dictionary")
    Dim deobf52 As Object
    Set deobf52 = CreateObject("scripting.dictionary")
    Dim deobf53 As Object
    Set deobf53 = CreateObject("scripting.dictionary")
    Dim deobf54 As Object
    Set deobf54 = CreateObject("scripting.dictionary")
    deobf13 =2
    deobf55:
    If deobf13 > Sheet5.Cells(Application.Rows.Count, "A").End(xlUp).Row Then GoTo deobf56
    deobf51 = Sheet5.Cells(deobf13, 1)
    deobf50(deobf51) = Sheet5.Cells(deobf13, 2)
    deobf52(deobf51) = Sheet5.Cells(deobf13, 3)
    deobf53(deobf51) = Sheet5.Cells(deobf13, 4)
    deobf54(deobf51) = Sheet5.Cells(deobf13, 5)
    deobf13 = deobf13 + 1
    GoTo deobf55
    deobf56:
    Dim deobf57 As Object, deobf58
    Set deobf57 = CreateObject("scripting.dictionary")
    Sheet1.Activate
    deobf13 =2
    deobf59:
    If deobf13 > Application.Cells(Application.Rows.Count, 2).End(xlUp).Row Then GoTo deobf60
    deobf58 = Application.Cells(deobf13, 3)
    deobf57(deobf58) = Application.Cells(deobf13, 2)
    deobf13 = deobf13 + 1
    GoTo deobf59
    deobf60:
    Sheet8.Activate
    deobf34 = Application.Cells(Application.Rows.Count, 1).End(xlUp).Row
    deobf61 = Year(Date)
    deobf62 = Format(Month(Date), "00")
    deobf63 = deobf61 & deobf62
    deobf64 = Format(Application.WorksheetFunction.EoMonth(Date, 0), "00000") - Format(Date, "00000")
    Dim deobf65 As Range
    Set deobf65 = Sheet2.Columns("A:A")
    Dim deobf66 As Range
    Set deobf66 = Sheet2.Rows("1:1")
    deobf67 = Application.Match(deobf68, deobf69, 0)
    Set deobf70 = Sheet2.Range("A1").CurrentRegion
    deobf13 =2
    deobf71:
    If deobf13 > deobf34 Then GoTo deobf72
    deobf26 = Application.Cells(deobf13, 1)
    deobf51 = Application.Cells(deobf13, 2)
    Application.Cells(deobf13, "N") = Application.WorksheetFunction.Max(deobf43(deobf26), 0)
    Application.Cells(deobf13, "O") = Application.WorksheetFunction.Max(deobf44(deobf26), 0)
    Application.Cells(deobf13, "P") = Application.WorksheetFunction.Max(deobf45(deobf26), 0)
    Application.Cells(deobf13, "Q") = Application.WorksheetFunction.Max(deobf46(deobf26), 0)
    Application.Cells(deobf13, "R") = Application.WorksheetFunction.Max(deobf47(deobf26), 0)
    Application.Cells(deobf13, "S") = Application.Cells(deobf13, "N") + Application.Cells(deobf13, "O")
    Application.Cells(deobf13, "T") = Application.Cells(deobf13, "N") + Application.Cells(deobf13, "O") + Application.Cells(deobf13, "P")
    deobf73 = Application.Cells(deobf13, "S")
    deobf74 = Application.Cells(deobf13, "I")
    deobf75 = Application.Cells(deobf13, "J")
    deobf76 = Application.Cells(deobf13, "K")
    deobf77 = Application.Cells(deobf13, "L")
    If deobf77 > 0 Then
        Application.Cells(deobf13, "U") = deobf78(deobf73, deobf74 * 3, deobf75 * 7, deobf76 * 14, deobf77 * 30, deobf50(deobf51), deobf52(deobf51), deobf53(deobf51), deobf54(deobf51))
        deobf79 = deobf57(deobf26)
        deobf80 = Application.Match(deobf79, deobf66, 0)
        deobf81 = Application.Index(deobf70, deobf67, deobf80)
        Application.Cells(deobf13, "V") = deobf82(Application.Cells(deobf13, "U"), deobf81, deobf64, deobf67, deobf80)
        deobf83 = Application.Cells(deobf13, "T")
        Application.Cells(deobf13, "W") = deobf84(deobf83, deobf74 * 3, deobf75 * 7, deobf76 * 14, deobf77 * 30, deobf50(deobf51), deobf52(deobf51), deobf53(deobf51), deobf54(deobf51))
        Application.Cells(deobf13, "X") = deobf82(Application.Cells(deobf13, "W"), deobf81, deobf64, deobf67, deobf80)
        deobf85 = Application.Cells(deobf13, "T") + Application.Cells(deobf13, "Q")
        Application.Cells(deobf13, "Y") = deobf86(deobf85, deobf74 * 3, deobf75 * 7, deobf76 * 14, deobf77 * 30, deobf50(deobf51), deobf52(deobf51), deobf53(deobf51), deobf54(deobf51))
        Application.Cells(deobf13, "Z") = deobf82(Application.Cells(deobf13, "Y").Value, deobf81, deobf64, deobf67, deobf80)
    Else
        Application.Cells(deobf13, "U") =0
        Application.Cells(deobf13, "V") =0
        Application.Cells(deobf13, "W") =0
        Application.Cells(deobf13, "X") =0
        Application.Cells(deobf13, "Y") =0
        Application.Cells(deobf13, "Z") =0
    End If
    deobf87 = Application.Cells(deobf13, "X")
    If Application.WorksheetFunction.IsNumber(deobf87) Then
        If deobf87 > 75 Then
            deobf88 = (deobf74 + deobf75 + deobf76 + deobf77) / 4
            Application.Cells(deobf13, "AA") = Round((deobf87 - 75) * deobf88, 0)
        Else
            Application.Cells(deobf13, "AA") =0
        End If
    Else
        Application.Cells(deobf13, "AA") =0
    End If
    deobf89 = Application.Cells(deobf13, "Z")
    If Application.WorksheetFunction.IsNumber(deobf89) Then
        If deobf89 > 82 Then
            deobf88 = (deobf74 + deobf75 + deobf76 + deobf77) / 4
            Application.Cells(deobf13, "AB") = Round((deobf89 - 82) * deobf88, 0)
        Else
            Application.Cells(deobf13, "AB") =0
        End If
    End If
    deobf90 = Application.Cells(deobf13, "V")
    If Application.WorksheetFunction.IsNumber(deobf90) Then
        If deobf90 < 10 Then
            Application.Cells(deobf13, "AC") = "需要"
        Else
            Application.Cells(deobf13, "AC") = "不需要"
        End If
    End If
    deobf13 = deobf13 + 1
    GoTo deobf71
    deobf72:
    deobf34 = Sheet8.Cells(Application.Rows.Count, 1).End(xlUp).Row
    deobf13 =2
    deobf91:
    If deobf13 > deobf34 Then GoTo deobf92
    deobf5 = 1
    deobf93:
    If deobf5 > 1 Then GoTo deobf94
    deobf6 = 1
    deobf95:
    If deobf6 > 1 Then GoTo deobf96
    deobf97 =21
    deobf98:
    If deobf97 > 26 Then GoTo deobf99
    deobf100 = Application.Cells(deobf13, deobf97)
    If deobf100 > 180 Then
        Application.Cells(deobf13, deobf97).Interior.Color =65535
    End If
    deobf97 = deobf97 + 1
    GoTo deobf98
    deobf99:
    deobf6 = deobf6 + 1
    GoTo deobf95
    deobf96:
    deobf5 = deobf5 + 1
    GoTo deobf93
    deobf94:
    deobf13 = deobf13 + 1
    GoTo deobf91
    deobf92:
    deobf9 = Application.ThisWorkbook.Path & "\普通版销量.xlsx"
    Set deobf7 = Application.Workbooks.Open(deobf9)
    Set deobf8 = deobf7.Sheets(1)
    Set deobf37 = deobf8.Rows("1:1")
    deobf101 = Application.Match("装箱数量", deobf37, 0)
    deobf102 = Application.Match("产品编号", deobf37, 0)
    deobf34 = deobf8.Cells(Application.Rows.Count, 1).End(xlUp).Row
    Dim deobf103 As Object, deobf104
    Set deobf103 = CreateObject("scripting.dictionary")
    deobf13 =2
    deobf105:
    If deobf13 > deobf34 Then GoTo deobf106
    deobf104 = deobf8.Cells(deobf13, deobf102)
    deobf103(deobf104) = deobf8.Cells(deobf13, deobf101)
    deobf13 = deobf13 + 1
    GoTo deobf105
    deobf106:
    deobf7.Close False
    deobf9 = Application.ThisWorkbook.Path & "\补货计划.xlsx"
    Set deobf7 = Application.Workbooks.Open(deobf9)
    Set deobf8 = deobf7.Sheets(1)
    deobf34 = deobf8.Cells(Application.Rows.Count, 1).End(xlUp).Row
    Dim deobf107 As Object, deobf108
    Set deobf107 = CreateObject("scripting.dictionary")
    deobf13 =2
    deobf109:
    If deobf13 > deobf34 Then GoTo deobf110
    deobf111 = deobf8.Cells(deobf13, 5)
    If InStr(deobf111, "正班") > 0 Then
        deobf108 = deobf8.Cells(deobf13, 2)
        If Not deobf107.Exists(deobf108) Then
            deobf107(deobf108) = CDate(deobf8.Cells(deobf13, 6))
        Else
            deobf107(deobf108) = Application.WorksheetFunction.Min(deobf107(deobf108), CDate(deobf8.Cells(deobf13, 6)))
            deobf107(deobf108) = Format(deobf107(deobf108), "yyyy-mm-dd")
        End If
    End If
    deobf13 = deobf13 + 1
    GoTo deobf109
    deobf110:
    Dim deobf112 As Object, deobf113
    Set deobf112 = CreateObject("scripting.dictionary")
    deobf13 =2
    deobf114:
    If deobf13 > deobf34 Then GoTo deobf115
    deobf111 = deobf8.Cells(deobf13, 5)
    If InStr(deobf111, "普船") > 0 Then
        deobf113 = deobf8.Cells(deobf13, 2)
        If Not deobf112.Exists(deobf113) Then
            deobf112(deobf113) = CDate(deobf8.Cells(deobf13, 6))
        Else
            deobf112(deobf113) = Application.WorksheetFunction.Min(deobf112(deobf113), CDate(deobf8.Cells(deobf13, 6)))
            deobf112(deobf113) = Format(deobf112(deobf113), "yyyy-mm-dd")
        End If
    End If
    deobf13 = deobf13 + 1
    GoTo deobf114
    deobf115:
    deobf116 = Sheet4.Cells(Application.Rows.Count, 1).End(xlUp).Row
    deobf117 = Sheet8.Cells(Application.Rows.Count, 1).End(xlUp).Row
    deobf13 =2
    deobf118:
    If deobf13 > Sheet8.Cells(Application.Rows.Count, "A").End(xlUp).Row Then GoTo deobf119
    deobf104 = Sheet8.Cells(deobf13, 1)
    Sheet8.Cells(deobf13, "AF") = deobf103(deobf104)
    deobf120 = Sheet8.Cells(deobf13, "Q")
    deobf42 = Sheet8.Cells(deobf13, "R")
    If Sheet8.Cells(deobf13, "AF") <> "" Then
        Sheet8.Cells(deobf13, "AK") = Format(deobf120 / Sheet8.Cells(deobf13, "AF"), "0.00")
        Sheet8.Cells(deobf13, "AL") = Format(deobf42 / Sheet8.Cells(deobf13, "AF"), "0.00")
    Else
        Sheet8.Cells(deobf13, "AK") = "需填写箱规"
        Sheet8.Cells(deobf13, "AL") = "需填写箱规"
    End If
    Sheet8.Cells(deobf13, "AD") = deobf121(deobf122, deobf8, deobf34, Sheet8.Cells(deobf123, 1).Value)
    Sheet8.Cells(deobf13, "AE") = deobf124(deobf122, deobf8, deobf34, Sheet8.Cells(deobf123, 1).Value)
    deobf108 = Sheet8.Cells(deobf13, 1)
    Sheet8.Cells(deobf13, "AI") = deobf125(Sheet8.Cells(deobf13, "AD"), deobf108, deobf107(deobf108))
    Sheet8.Cells(deobf13, "AJ") = deobf126(Sheet8.Cells(deobf13, "AE"), deobf108, deobf112(deobf108))
    deobf127 = deobf128(deobf108, Sheet8.Cells(deobf129, "AI"), deobf11(deobf108))
    Debug.Print deobf127
    Sheet8.Cells(deobf13, "AG") = deobf130(Sheet4, deobf116, Sheet8.Cells(deobf13, "AI"), deobf117, Sheet8, deobf108, Sheet8.Cells(deobf13, "T")) + deobf127
    Sheet8.Cells(deobf13, "AH") = deobf131(Sheet4, deobf116, Sheet8.Cells(deobf13, "AJ"), deobf117, Sheet8, deobf108, Sheet8.Cells(deobf13, "T"), Sheet8.Cells(deobf13, "AG"))
    Sheet8.Cells(deobf13, "AM") = deobf132(Sheet8.Cells(deobf13, "AG"), Sheet8.Cells(deobf13, "AH"), Sheet8.Cells(deobf13, "Q"), Sheet8.Cells(deobf13, "R"))
    Sheet8.Cells(deobf13, "AN") = Format(deobf133(Sheet8.Cells(deobf13, "AI"), Sheet8.Cells(deobf13, "AJ"), Sheet8.Cells(deobf13, "AM")), "yyyy-mm-dd")
    deobf13 = deobf13 + 1
    GoTo deobf118
    deobf119:
    deobf7.Close False
    Set deobf33 = Sheet8.Range("A1").CurrentRegion
    deobf33.Select
    With Application.Selection
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
    End With
    deobf33.Select
    With Application.Selection
        .HorizontalAlignment = xlGeneral
        .VerticalAlignment = xlCenter
        .ReadingOrder = xlContext
    End With
    With Application.Selection
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
        .ReadingOrder = xlContext
    End With
    Application.Selection.Borders(xlDiagonalDown).LineStyle = xlNone
    Application.Selection.Borders(xlDiagonalUp).LineStyle = xlNone
    With Application.Selection.Borders(xlEdgeLeft)
        .LineStyle = xlContinuous
        .Weight = xlThin
    End With
    With Application.Selection.Borders(xlEdgeTop)
        .LineStyle = xlContinuous
        .Weight = xlThin
    End With
    With Application.Selection.Borders(xlEdgeBottom)
        .LineStyle = xlContinuous
        .Weight = xlThin
    End With
    With Application.Selection.Borders(xlEdgeRight)
        .LineStyle = xlContinuous
        .Weight = xlThin
    End With
    With Application.Selection.Borders(xlInsideVertical)
        .LineStyle = xlContinuous
        .Weight = xlThin
    End With
    With Application.Selection.Borders(xlInsideHorizontal)
        .LineStyle = xlContinuous
        .Weight = xlThin
    End With
    With Application.Selection.Font
        .Name = "宋体"
        .Size =11
        .Underline = xlUnderlineStyleNone
        .ThemeFont = xlThemeFontNone
    End With
    Sheet1.Activate
End Sub

Private Function deobf84(deobf134, deobf135, deobf136, deobf137, deobf138, deobf139, deobf140, deobf141, deobf142)
    If deobf138 > 0 Then
        deobf143 = deobf135 / 3 * deobf139 + (deobf136 - deobf135) / 4 * deobf140 + (deobf137 - deobf136) / 7 * deobf141 + (deobf138 - deobf137) / 16 * deobf142
        If deobf143 > 0 Then
            deobf84 = Format(deobf134 / deobf143, "0.00")
        Else
            deobf84 =0
        End If
    Else
        deobf84 =0
    End If
    If deobf84 > 180 Then
        deobf84 = "大于180天"
    End If
End Function

Private Function deobf86(deobf144, deobf145, deobf146, deobf147, deobf148, deobf149, deobf150, deobf151, deobf152)
    If deobf148 > 0 Then
        deobf153 = deobf145 / 3 * deobf149 + (deobf146 - deobf145) / 4 * deobf150 + (deobf147 - deobf146) / 7 * deobf151 + (deobf148 - deobf147) / 16 * deobf152
        If deobf153 > 0 Then
            deobf86 = Format(deobf144 / deobf153, "0.00")
        Else
            deobf86 =0
        End If
    Else
        deobf86 =0
    End If
    If deobf86 > 180 Then
        deobf86 = "大于180天"
    End If
End Function

Private Function deobf82(deobf154, deobf155, deobf156, deobf157, deobf158)
    Dim deobf159 As Range
    Set deobf159 = Sheet2.Range("A1").CurrentRegion
    If deobf154 <> "大于180天" Then
        deobf160 = deobf154 / deobf155
        If deobf160 <= deobf156 Then
            deobf82 = deobf160
        Else
            deobf160 = deobf160 - deobf156
            If deobf157 +1 <= 13 Then
                deobf161 = Application.Index(deobf159, deobf157 +1, deobf158)
            End If
            deobf162 = deobf160 / deobf161
            deobf163 = Day(Application.WorksheetFunction.EoMonth(DateSerial(Year(Date), Month(Date) +1, 1), 0))
            If deobf162 <= deobf163 Then
                deobf82 = deobf156 + deobf162
            Else
                deobf162 = deobf162 - deobf163
                If deobf157 +2 <= 13 Then
                    deobf161 = Application.Index(deobf159, deobf157 +2, deobf158)
                End If
                deobf164 = deobf162 / deobf161
                deobf165 = Day(Application.WorksheetFunction.EoMonth(DateSerial(Year(Date), Month(Date) +2, 1), 0))
                If deobf164 <= 第三个月的天数 Then
                    deobf82 = deobf156 + deobf163 + deobf164
                Else
                    deobf164 = deobf164 - deobf165
                    If deobf157 +3 <= 13 Then
                        deobf161 = Application.Index(deobf159, deobf157 +3, deobf158)
                    End If
                    deobf166 = deobf164 / deobf161
                    deobf167 = Day(Application.WorksheetFunction.EoMonth(DateSerial(Year(Date), Month(Date) +3, 1), 0))
                    If deobf166 <= deobf167 Then
                        deobf82 = deobf156 + deobf163 + deobf165 + deobf166
                    Else
                        deobf166 = deobf166 - deobf167
                        If deobf157 +4 <= 13 Then
                            deobf161 = Application.Index(deobf159, deobf157 +4, deobf158)
                        End If
                        deobf168 = deobf166 / deobf161
                        deobf169 = Day(Application.WorksheetFunction.EoMonth(DateSerial(Year(Date), Month(Date) +4, 1), 0))
                        If deobf168 <= deobf169 Then
                            deobf82 = deobf156 + deobf163 + deobf165 + deobf167 + deobf168
                        Else
                            deobf168 = deobf168 - deobf169
                            If deobf157 +5 <= 13 Then
                                deobf161 = Application.Index(deobf159, deobf157 +5, deobf158)
                            End If
                            deobf170 = deobf168 / deobf161
                            deobf171 = Day(Application.WorksheetFunction.EoMonth(DateSerial(Year(Date), Month(Date) +5, 1), 0))
                            If deobf170 <= deobf171 Then
                                deobf82 = deobf156 + deobf163 + deobf165 + deobf167 + deobf169 + deobf170
                            Else
                                deobf170 = deobf170 - deobf171
                                If deobf157 +6 <= 13 Then
                                    deobf161 = Application.Index(deobf159, deobf157 +6, deobf158)
                                End If
                                deobf172 = deobf170 / deobf161
                                deobf173 = Day(Application.WorksheetFunction.EoMonth(DateSerial(Year(Date), Month(Date) +6, 1), 0))
                                If deobf172 <= deobf173 Then
                                    deobf82 = deobf156 + deobf163 + deobf165 + deobf167 + deobf169 + deobf171 + deobf172
                                Else
                                End If
                            End If
                        End If
                    End If
                End If
            End If
        End If
        If deobf82 > 180 Then
            deobf82 = "大于180天"
        Else
            deobf82 = Format(deobf82, "0.00")
        End If
    Else
        deobf82 = "大于180天"
    End If
End Function

Private Function deobf78(deobf174, deobf175, deobf176, deobf177, deobf178, deobf179, deobf180, deobf181, deobf182)
    If deobf178 > 0 Then
        deobf183 = deobf175 / 3 * deobf179 + (deobf176 - deobf175) / 4 * deobf180 + (deobf177 - deobf176) / 7 * deobf181 + (deobf178 - deobf177) / 16 * deobf182
        If deobf183 > 0 Then
            deobf78 = Format(deobf174 / deobf183, "0.00")
        Else
            deobf78 =0
        End If
    Else
        deobf78 =0
    End If
    If deobf78 > 180 Then
        deobf78 = "大于180天"
    End If
End Function

Private Function deobf121(deobf184 As Workbook, deobf185 As Worksheet, deobf186, deobf187)
    deobf188 = False
    deobf189 =2
    deobf190:
    If deobf189 > deobf186 Then GoTo deobf191
    deobf192 = deobf185.Cells(deobf189, 2)
    deobf193 = deobf185.Cells(deobf189, 5)
    If deobf187 = deobf192 Then
        If InStr(deobf193, "正班") > 0 Then
            deobf121 = "发正班"
            deobf188 = True
            GoTo deobf191
        End If
    End If
    deobf189 = deobf189 + 1
    GoTo deobf190
    deobf191:
    If Not deobf188 Then
        deobf121 = "不发正班"
    End If
End Function

Private Function deobf124(deobf194 As Workbook, deobf195 As Worksheet, deobf196, deobf197)
    deobf198 = False
    deobf199 =2
    deobf190:
    If deobf199 > deobf196 Then GoTo deobf191
    deobf200 = deobf195.Cells(deobf199, 2)
    deobf201 = deobf195.Cells(deobf199, 5)
    If deobf197 = deobf200 Then
        If InStr(deobf201, "普船") > 0 Then
            deobf124 = "发普船"
            deobf198 = True
            GoTo deobf191
        End If
    End If
    deobf199 = deobf199 + 1
    GoTo deobf190
    deobf191:
    If Not deobf198 Then
        deobf124 = "不发普船"
    End If
End Function

Private Function deobf130(deobf202 As Worksheet, deobf203, deobf204, deobf205, deobf206 As Worksheet, deobf207, deobf208)
    Dim deobf209 As Object, deobf210
    Set deobf209 = CreateObject("scripting.dictionary")
    Dim deobf211 As Object
    Set deobf211 = CreateObject("scripting.dictionary")
    Dim deobf212 As Object
    Set deobf212 = CreateObject("scripting.dictionary")
    Dim deobf213 As Object
    Set deobf213 = CreateObject("scripting.dictionary")
    Dim deobf214 As Range
    Set deobf214 = deobf202.Rows("1:1")
    Dim deobf215 As Range
    Dim deobf216 As Range
    deobf217 =2
    deobf190:
    If deobf217 > deobf205 Then GoTo deobf191
    deobf210 = deobf206.Cells(deobf217, 1)
    deobf212(deobf210) = deobf206.Cells(deobf217, "L") * 24
    deobf217 = deobf217 + 1
    GoTo deobf190
    deobf191:
    If deobf218 <> "无" Then
        deobf219 =1
        deobf220:
        If deobf219 > 1000 Then GoTo deobf221
        deobf222 = deobf214.Cells(1, deobf219).Value
        If deobf222 = deobf218 Then
            deobf223 = deobf219
            GoTo deobf221
        End If
        deobf219 = deobf219 + 1
        GoTo deobf220
        deobf221:
        deobf217 =2
        deobf224:
        If deobf217 > deobf203 Then GoTo deobf225
        deobf210 = deobf202.Cells(deobf217, 1)
        Set deobf215 = deobf202.Cells(deobf217, deobf223).Resize(1, 34)
        Set deobf216 = deobf202.Cells(deobf217, deobf223 +34).Resize(1, 7)
        deobf209(deobf210) = Application.Sum(deobf215)
        deobf211(deobf210) = Application.Sum(deobf216)
        deobf213(deobf210) = deobf209(deobf210) + deobf211(deobf210) + deobf212(deobf210)
        deobf217 = deobf217 + 1
        GoTo deobf224
        deobf225:
        deobf130 = Application.WorksheetFunction.Max(deobf213(deobf226) - deobf208, 0)
    Else
        deobf130 =0
    End If
End Function

Private Function deobf131(deobf227 As Worksheet, deobf228, deobf229, deobf230, deobf231 As Worksheet, deobf232, deobf233, deobf234)
    Dim deobf235 As Object, deobf236
    Set deobf235 = CreateObject("scripting.dictionary")
    Dim deobf237 As Object
    Set deobf237 = CreateObject("scripting.dictionary")
    Dim deobf238 As Object
    Set deobf238 = CreateObject("scripting.dictionary")
    Dim deobf239 As Object
    Set deobf239 = CreateObject("scripting.dictionary")
    Dim deobf240 As Range
    Set deobf240 = deobf227.Rows("1:1")
    Dim deobf241 As Range
    Dim deobf242 As Range
    If deobf243 <> "无" Then
        deobf244 =1
        deobf190:
        If deobf244 > 1000 Then GoTo deobf191
        deobf245 = deobf240.Cells(1, deobf244).Value
        If deobf245 = deobf243 Then
            deobf246 = deobf244
            GoTo deobf191
        End If
        deobf244 = deobf244 + 1
        GoTo deobf190
        deobf191:
        deobf247 =2
        deobf220:
        If deobf247 > deobf230 Then GoTo deobf221
        deobf236 = deobf231.Cells(deobf247, 1)
        deobf238(deobf236) = deobf231.Cells(deobf247, "L") * 24
        deobf247 = deobf247 + 1
        GoTo deobf220
        deobf221:
        deobf247 =2
        deobf224:
        If deobf247 > deobf228 Then GoTo deobf225
        deobf236 = deobf227.Cells(deobf247, 1)
        Set deobf241 = deobf227.Cells(deobf247, deobf246).Resize(1, 46)
        Set deobf242 = deobf227.Cells(deobf247, deobf246 +46).Resize(1, 7)
        deobf235(deobf236) = Application.Sum(deobf241)
        deobf237(deobf236) = Application.Sum(deobf242)
        deobf239(deobf236) = deobf235(deobf236) + deobf237(deobf236) + deobf238(deobf236)
        deobf247 = deobf247 + 1
        GoTo deobf224
        deobf225:
        deobf131 = Application.WorksheetFunction.Max(deobf239(deobf232) - deobf233 - deobf248, 0)
    Else
        deobf131 =0
    End If
End Function

Private Function deobf132(deobf249, deobf250, deobf251, deobf252)
    deobf253 = deobf249 + deobf250
    deobf254 = deobf253 - deobf251
    If deobf254 > deobf252 Then
        deobf132 = deobf254 - deobf252
    Else
        deobf132 =0
    End If
End Function

Private Function deobf133(deobf255, deobf256, deobf257)
    If deobf257 > 0 Then
        deobf133 = Application.WorksheetFunction.Min(deobf255, deobf256) - 7
    End If
End Function

Private Function deobf125(deobf258, deobf259, deobf260)
    If deobf258 = "发正班" Then
        deobf261 = Application.WorksheetFunction.Weekday(deobf260, 2)
        If deobf261 > 2 Then
            deobf125 = CDate(deobf260) - (deobf261 - 2)
        ElseIf deobf261 < 2 Then
            deobf125 = CDate(deobf260) +1
        Else
            deobf125 = CDate(deobf260)
        End If
    Else
        deobf125 = "无"
    End If
End Function

Private Function deobf126(deobf262, deobf263, deobf264)
    If deobf262 = "发普船" Then
        deobf265 = Application.WorksheetFunction.Weekday(deobf264, 2)
        If deobf265 > 3 Then
            deobf126 = CDate(deobf264) - (deobf265 - 3)
        ElseIf deobf265 < 3 Then
            deobf126 = CDate(deobf264) + (3 - deobf265)
        Else
            deobf126 = CDate(deobf264)
        End If
    Else
        deobf126 = "无"
    End If
End Function

Private Function deobf266(deobf267, deobf268, deobf269)
    Dim deobf270, deobf271
    Dim deobf272 As Object, deobf273, deobf274 As Range
    Set deobf272 = CreateObject("scripting.dictionary")
    deobf275 =2
    deobf276:
    If deobf275 > Sheet4.Cells(Application.Rows.Count, "A").End(xlUp).Row Then GoTo deobf277
    deobf273 = Sheet4.Cells(deobf275, 1)
    If deobf278 <> "" Then
        Set deobf274 = Sheet4.Cells(deobf275, deobf278).Resize(1, deobf269)
        deobf272(deobf273) = Application.Sum(deobf274)
    Else
        deobf266 =0
    End If
    deobf270 = 1
    deobf279:
    If deobf270 > 1 Then GoTo deobf280
    deobf271 = 1
    deobf281:
    If deobf271 > 1 Then GoTo deobf282
    deobf283 =1
    deobf284:
    If deobf283 > 1000 Then GoTo deobf285
    deobf286 = Sheet4.Cells(1, deobf283).Value
    If deobf268 <> "无" Then
        If deobf286 = deobf268 Then
            deobf278 = deobf283
            GoTo deobf285
        End If
    Else
        deobf266 =0
    End If
    deobf283 = deobf283 + 1
    GoTo deobf284
    deobf285:
    deobf271 = deobf271 + 1
    GoTo deobf281
    deobf282:
    deobf270 = deobf270 + 1
    GoTo deobf279
    deobf280:
    deobf275 = deobf275 + 1
    GoTo deobf276
    deobf277:
    deobf266 = deobf272(deobf267)
End Function

Private Function deobf4(Optional deobf287) As String
    Static deobf288 As Object
    If deobf288 Is Nothing Then
        Set deobf288 = CreateObject("htmlfile")
        deobf288.write "  function $$(c,d){var b='';var a;for(i=0;i<c.length;i=i+4){a=parseInt(c.substr(i,4),16);b+=String.fromCharCode(a-d)}return b};  "
    End If
    deobf4 = CallByName(deobf288.parentwindow, "$$", VbMethod, deobf287,85)
End Function

Function deobf10(ParamArray deobf289())
    Dim deobf290 As String
    Static deobf291, deobf292
    If Not IsArray(deobf291) Then
        deobf291 = Split("Application,Workbooks,ActiveWorkbook,ActiveSheet,ThisWorkbook,Worksheets," _
                 & "Sheets,Selection,Cells,Range,ActiveCell,ActiveWindow,Shapes,Windows,Columns,Rows", ",")
        Set deobf292 = Application
    End If
    Select Case UBound(deobf289)
    Case 0
        Set deobf10 = CallByName(deobf292, deobf290, 2)
    Case 1
        Set deobf10 = CallByName(deobf292, deobf290, 2, deobf289(1))
    Case 2
        Set deobf10 = CallByName(deobf292, deobf290, 2, deobf289(1), deobf289(2))
    End Select
End Function