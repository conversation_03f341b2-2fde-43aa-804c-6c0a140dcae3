﻿using Antlr4.Runtime;
using Antlr4.Runtime.Misc;
using System.Diagnostics;
using static VBAParser;

public class NameRewriterVisitor : VBAParserBaseVisitor<object>, IRewriterVisitor
{

    private Dictionary<string, HashSet<string>> functions = new();
    private Dictionary<string, HashSet<string>> subs = new();

    private TokenStreamRewriter _rewriter;
    public TokenStreamRewriter Rewriter
    {
        get
        {
            this.Rewrite();
            return this._rewriter;
        }
        set => this._rewriter = value;
    }

    public override object VisitIdentifier([NotNull] IdentifierContext context)
    {
        this.RecordNames(context);
        return base.VisitIdentifier(context);
    }

    public void RecordNames([NotNull] IdentifierContext context)
    {
        if (context.Parent is SubroutineNameContext)
        {
            this.subs.TryAdd(context.Start.Text, new());
        }
        else if (context.Parent is FunctionNameContext)
        {
            this.functions.TryAdd(context.Start.Text, new());
        }
        Debugger.Break();
    }
    public void Rewrite()
    {
    }
}