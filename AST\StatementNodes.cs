using System.Collections.Generic;

namespace VbDeobf.AST
{
    /// <summary>
    /// Assignment statement node (Let, Set)
    /// </summary>
    public class AssignmentStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.AssignmentStatement;
        public ExpressionNode Target { get; set; }
        public ExpressionNode Value { get; set; }
        public bool IsSetAssignment { get; set; } // true for Set, false for Let

        public AssignmentStatementNode(ExpressionNode target, ExpressionNode value, bool isSetAssignment = false, SourceLocation? sourceLocation = null)
            : base(sourceLocation)
        {
            Target = target;
            Value = value;
            IsSetAssignment = isSetAssignment;
            AddChild(target);
            AddChild(value);
        }

        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitAssignmentStatement(this);
    }

    /// <summary>
    /// Call statement node
    /// </summary>
    public class CallStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.CallStatement;
        public ExpressionNode Expression { get; set; }
        public bool HasCallKeyword { get; set; }

        public CallStatementNode(ExpressionNode expression, bool hasCallKeyword = false, SourceLocation? sourceLocation = null)
            : base(sourceLocation)
        {
            Expression = expression;
            HasCallKeyword = hasCallKeyword;
            AddChild(expression);
        }

        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitCallStatement(this);
    }

    /// <summary>
    /// If statement node
    /// </summary>
    public class IfStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.IfStatement;
        public ExpressionNode Condition { get; set; }
        public BlockNode ThenBlock { get; set; }
        public List<ElseIfStatementNode> ElseIfBlocks { get; set; } = new List<ElseIfStatementNode>();
        public ElseStatementNode? ElseBlock { get; set; }
        public bool IsSingleLine { get; set; }

        public IfStatementNode(ExpressionNode condition, BlockNode thenBlock, bool isSingleLine = false, SourceLocation? sourceLocation = null)
            : base(sourceLocation)
        {
            Condition = condition;
            ThenBlock = thenBlock;
            IsSingleLine = isSingleLine;
            AddChild(condition);
            AddChild(thenBlock);
        }

        public void AddElseIf(ElseIfStatementNode elseIfBlock)
        {
            ElseIfBlocks.Add(elseIfBlock);
            AddChild(elseIfBlock);
        }

        public void SetElse(ElseStatementNode elseBlock)
        {
            ElseBlock = elseBlock;
            AddChild(elseBlock);
        }

        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitIfStatement(this);
    }

    /// <summary>
    /// ElseIf statement node
    /// </summary>
    public class ElseIfStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.ElseIfStatement;
        public ExpressionNode Condition { get; set; }
        public BlockNode Block { get; set; }

        public ElseIfStatementNode(ExpressionNode condition, BlockNode block, SourceLocation? sourceLocation = null)
            : base(sourceLocation)
        {
            Condition = condition;
            Block = block;
            AddChild(condition);
            AddChild(block);
        }

        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitElseIfStatement(this);
    }

    /// <summary>
    /// Else statement node
    /// </summary>
    public class ElseStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.ElseStatement;
        public BlockNode Block { get; set; }

        public ElseStatementNode(BlockNode block, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            Block = block;
            AddChild(block);
        }

        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitElseStatement(this);
    }

    /// <summary>
    /// For statement node
    /// </summary>
    public class ForStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.ForStatement;
        public ExpressionNode Variable { get; set; }
        public ExpressionNode StartValue { get; set; }
        public ExpressionNode EndValue { get; set; }
        public ExpressionNode? StepValue { get; set; }
        public BlockNode Body { get; set; }

        public ForStatementNode(ExpressionNode variable, ExpressionNode startValue, ExpressionNode endValue,
            BlockNode body, ExpressionNode? stepValue = null, SourceLocation? sourceLocation = null)
            : base(sourceLocation)
        {
            Variable = variable;
            StartValue = startValue;
            EndValue = endValue;
            StepValue = stepValue;
            Body = body;
            AddChild(variable);
            AddChild(startValue);
            AddChild(endValue);
            if (stepValue != null) AddChild(stepValue);
            AddChild(body);
        }

        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitForStatement(this);
    }

    /// <summary>
    /// For Each statement node
    /// </summary>
    public class ForEachStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.ForEachStatement;
        public ExpressionNode Variable { get; set; }
        public ExpressionNode Collection { get; set; }
        public BlockNode Body { get; set; }

        public ForEachStatementNode(ExpressionNode variable, ExpressionNode collection, BlockNode body, SourceLocation? sourceLocation = null)
            : base(sourceLocation)
        {
            Variable = variable;
            Collection = collection;
            Body = body;
            AddChild(variable);
            AddChild(collection);
            AddChild(body);
        }

        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitForEachStatement(this);
    }

    /// <summary>
    /// While statement node
    /// </summary>
    public class WhileStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.WhileStatement;
        public ExpressionNode Condition { get; set; }
        public BlockNode Body { get; set; }

        public WhileStatementNode(ExpressionNode condition, BlockNode body, SourceLocation? sourceLocation = null)
            : base(sourceLocation)
        {
            Condition = condition;
            Body = body;
            AddChild(condition);
            AddChild(body);
        }

        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitWhileStatement(this);
    }

    /// <summary>
    /// Do Loop statement node
    /// </summary>
    public class DoLoopStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.DoLoopStatement;
        public ExpressionNode? Condition { get; set; }
        public BlockNode Body { get; set; }
        public DoLoopType LoopType { get; set; }
        public bool IsConditionAtStart { get; set; }

        public DoLoopStatementNode(BlockNode body, DoLoopType loopType, ExpressionNode? condition = null,
            bool isConditionAtStart = true, SourceLocation? sourceLocation = null)
            : base(sourceLocation)
        {
            Body = body;
            LoopType = loopType;
            Condition = condition;
            IsConditionAtStart = isConditionAtStart;
            AddChild(body);
            if (condition != null) AddChild(condition);
        }

        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitDoLoopStatement(this);
    }

    /// <summary>
    /// Select Case statement node
    /// </summary>
    public class SelectCaseStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.SelectCaseStatement;
        public ExpressionNode Expression { get; set; }
        public List<CaseStatementNode> Cases { get; set; } = new List<CaseStatementNode>();
        public BlockNode? ElseCase { get; set; }

        public SelectCaseStatementNode(ExpressionNode expression, SourceLocation? sourceLocation = null)
            : base(sourceLocation)
        {
            Expression = expression;
            AddChild(expression);
        }

        public void AddCase(CaseStatementNode caseNode)
        {
            Cases.Add(caseNode);
            AddChild(caseNode);
        }

        public void SetElseCase(BlockNode elseCase)
        {
            ElseCase = elseCase;
            AddChild(elseCase);
        }

        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitSelectCaseStatement(this);
    }

    /// <summary>
    /// Case statement node
    /// </summary>
    public class CaseStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.CaseStatement;
        public List<ExpressionNode> Values { get; set; } = new List<ExpressionNode>();
        public BlockNode Body { get; set; }
        public bool IsElseCase { get; set; }

        public CaseStatementNode(BlockNode body, bool isElseCase = false, SourceLocation? sourceLocation = null)
            : base(sourceLocation)
        {
            Body = body;
            IsElseCase = isElseCase;
            AddChild(body);
        }

        public void AddValue(ExpressionNode value)
        {
            Values.Add(value);
            AddChild(value);
        }

        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitCaseStatement(this);
    }

    /// <summary>
    /// With statement node
    /// </summary>
    public class WithStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.WithStatement;
        public ExpressionNode Expression { get; set; }
        public BlockNode Body { get; set; }

        public WithStatementNode(ExpressionNode expression, BlockNode body, SourceLocation? sourceLocation = null)
            : base(sourceLocation)
        {
            Expression = expression;
            Body = body;
            AddChild(expression);
            AddChild(body);
        }

        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitWithStatement(this);
    }

    /// <summary>
    /// Exit statement node
    /// </summary>
    public class ExitStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.ExitStatement;
        public ExitType ExitType { get; set; }

        public ExitStatementNode(ExitType exitType, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            ExitType = exitType;
        }

        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitExitStatement(this);
    }

    /// <summary>
    /// Return statement node
    /// </summary>
    public class ReturnStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.ReturnStatement;

        public ReturnStatementNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }

        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitReturnStatement(this);
    }

    /// <summary>
    /// GoTo statement node
    /// </summary>
    public class GoToStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.GoToStatement;
        public string Label { get; set; }

        public GoToStatementNode(string label, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            Label = label;
        }

        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitGoToStatement(this);
    }

    /// <summary>
    /// GoSub statement node
    /// </summary>
    public class GoSubStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.GoSubStatement;
        public string Label { get; set; }

        public GoSubStatementNode(string label, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            Label = label;
        }

        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitGoSubStatement(this);
    }

    /// <summary>
    /// On Error statement node
    /// </summary>
    public class OnErrorStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.OnErrorStatement;
        public OnErrorType ErrorType { get; set; }
        public string? Label { get; set; }

        public OnErrorStatementNode(OnErrorType errorType, string? label = null, SourceLocation? sourceLocation = null)
            : base(sourceLocation)
        {
            ErrorType = errorType;
            Label = label;
        }

        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitOnErrorStatement(this);
    }

    /// <summary>
    /// RaiseEvent statement node
    /// </summary>
    public class RaiseEventStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.RaiseEventStatement;
        public string EventName { get; set; }
        public ArgumentListNode? Arguments { get; set; }

        public RaiseEventStatementNode(string eventName, ArgumentListNode? arguments = null, SourceLocation? sourceLocation = null)
            : base(sourceLocation)
        {
            EventName = eventName;
            Arguments = arguments;
            if (arguments != null) AddChild(arguments);
        }

        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitRaiseEventStatement(this);
    }

    /// <summary>
    /// Redim statement node
    /// </summary>
    public class RedimStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.RedimStatement;
        public List<ExpressionNode> Variables { get; set; } = new List<ExpressionNode>();
        public bool IsPreserve { get; set; }

        public RedimStatementNode(bool isPreserve = false, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            IsPreserve = isPreserve;
        }

        public void AddVariable(ExpressionNode variable)
        {
            Variables.Add(variable);
            AddChild(variable);
        }

        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitRedimStatement(this);
    }

    /// <summary>
    /// Erase statement node
    /// </summary>
    public class EraseStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.EraseStatement;
        public List<ExpressionNode> Variables { get; set; } = new List<ExpressionNode>();

        public EraseStatementNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }

        public void AddVariable(ExpressionNode variable)
        {
            Variables.Add(variable);
            AddChild(variable);
        }

        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitEraseStatement(this);
    }

    /// <summary>
    /// Types of Do Loop statements
    /// </summary>
    public enum DoLoopType
    {
        DoLoop,      // Do ... Loop (infinite)
        DoWhile,     // Do While ... Loop or Do ... Loop While
        DoUntil      // Do Until ... Loop or Do ... Loop Until
    }

    /// <summary>
    /// Types of Exit statements
    /// </summary>
    public enum ExitType
    {
        ExitDo,
        ExitFor,
        ExitFunction,
        ExitProperty,
        ExitSub
    }

    /// <summary>
    /// Types of On Error statements
    /// </summary>
    public enum OnErrorType
    {
        GoTo,
        Resume,
        ResumeNext
    }
}
