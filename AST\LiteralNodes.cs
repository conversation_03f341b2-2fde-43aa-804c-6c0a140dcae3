namespace VbDeobf.AST
{
    /// <summary>
    /// String literal node
    /// </summary>
    public class StringLiteralNode : ExpressionNode
    {
        public override ASTNodeType NodeType => ASTNodeType.StringLiteral;
        public string Value { get; set; }
        
        public StringLiteralNode(string value, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            Value = value;
            ExpressionType = VBAType.String;
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitStringLiteral(this);
    }
    
    /// <summary>
    /// Number literal node
    /// </summary>
    public class NumberLiteralNode : ExpressionNode
    {
        public override ASTNodeType NodeType => ASTNodeType.NumberLiteral;
        public object Value { get; set; } // Can be int, long, double, etc.
        public NumberType NumberType { get; set; }
        
        public NumberLiteralNode(object value, NumberType numberType, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            Value = value;
            NumberType = numberType;
            ExpressionType = GetVBAType(numberType);
        }
        
        private static VBAType GetVBAType(NumberType numberType)
        {
            return numberType switch
            {
                NumberType.Integer => VBAType.Integer,
                NumberType.Long => VBAType.Long,
                NumberType.LongLong => VBAType.LongLong,
                NumberType.Single => VBAType.Single,
                NumberType.Double => VBAType.Double,
                NumberType.Currency => VBAType.Currency,
                NumberType.Byte => VBAType.Byte,
                _ => VBAType.Variant
            };
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitNumberLiteral(this);
    }
    
    /// <summary>
    /// Boolean literal node
    /// </summary>
    public class BooleanLiteralNode : ExpressionNode
    {
        public override ASTNodeType NodeType => ASTNodeType.BooleanLiteral;
        public bool Value { get; set; }
        
        public BooleanLiteralNode(bool value, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            Value = value;
            ExpressionType = VBAType.Boolean;
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitBooleanLiteral(this);
    }
    
    /// <summary>
    /// Date literal node
    /// </summary>
    public class DateLiteralNode : ExpressionNode
    {
        public override ASTNodeType NodeType => ASTNodeType.DateLiteral;
        public DateTime Value { get; set; }
        
        public DateLiteralNode(DateTime value, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            Value = value;
            ExpressionType = VBAType.Date;
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitDateLiteral(this);
    }
    
    /// <summary>
    /// Nothing literal node
    /// </summary>
    public class NothingLiteralNode : ExpressionNode
    {
        public override ASTNodeType NodeType => ASTNodeType.NothingLiteral;
        
        public NothingLiteralNode(SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            ExpressionType = VBAType.Object;
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitNothingLiteral(this);
    }
    
    /// <summary>
    /// Empty literal node
    /// </summary>
    public class EmptyLiteralNode : ExpressionNode
    {
        public override ASTNodeType NodeType => ASTNodeType.EmptyLiteral;
        
        public EmptyLiteralNode(SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            ExpressionType = VBAType.Variant;
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitEmptyLiteral(this);
    }
    
    /// <summary>
    /// Null literal node
    /// </summary>
    public class NullLiteralNode : ExpressionNode
    {
        public override ASTNodeType NodeType => ASTNodeType.NullLiteral;
        
        public NullLiteralNode(SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            ExpressionType = VBAType.Variant;
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitNullLiteral(this);
    }
    
    /// <summary>
    /// Types of number literals
    /// </summary>
    public enum NumberType
    {
        Byte,
        Integer,
        Long,
        LongLong,
        Single,
        Double,
        Currency
    }
}
