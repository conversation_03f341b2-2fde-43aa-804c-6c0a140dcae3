apiVersion: v1
kind: Service
metadata:
  name: {{ template "vbsparkleweb.fullname" . }}
  labels:
    app: {{ template "vbsparkleweb.name" . }}
    chart: {{ template "vbsparkleweb.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app: {{ template "vbsparkleweb.name" . }}
    release: {{ .Release.Name }}
