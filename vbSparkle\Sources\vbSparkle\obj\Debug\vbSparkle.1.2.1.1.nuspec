﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>vbSparkle</id>
    <version>*******</version>
    <authors>Airbus CERT, Sylvain Bruyere</authors>
    <requireLicenseAcceptance>true</requireLicenseAcceptance>
    <license type="file">LICENSE.txt</license>
    <licenseUrl>https://aka.ms/deprecateLicenseUrl</licenseUrl>
    <projectUrl>https://github.com/sbruyere/vbSparkle</projectUrl>
    <description>vbSparkle is a source-to-source multi-platform Visual Basic deobfuscator based on partial-evaluation and is mainly dedicated to the analysis of malicious code written in VBScript and VBA (Office Macro).

It is written in native C# and provides a .Net Standard library, and works on Windows, Linux, MacOS, etc..

The parsing of Visual Basic Script and VBA is processed through the use of ANTLR grammar &amp; lexer parsers.</description>
    <copyright>Airbus CERT, Sylvain B<PERSON>e</copyright>
    <tags>vbscript, visualbasic, vba, deobfuscation, malware, reverse</tags>
    <repository type="git" url="https://github.com/sbruyere/vbSparkle" commit="d342d4cc136286c4c81c389ff78b1361833bd601" />
    <dependencies>
      <group targetFramework="net6.0">
        <dependency id="Antlr4.Runtime.Standard" version="4.13.1" exclude="Build,Analyzers" />
        <dependency id="MathNet.Symbolics" version="0.25.0" exclude="Build,Analyzers" />
        <dependency id="System.Text.Encoding.CodePages" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="vbeDecoder" version="1.0.2" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net8.0">
        <dependency id="Antlr4.Runtime.Standard" version="4.13.1" exclude="Build,Analyzers" />
        <dependency id="MathNet.Symbolics" version="0.25.0" exclude="Build,Analyzers" />
        <dependency id="System.Text.Encoding.CodePages" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="vbeDecoder" version="1.0.2" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.0">
        <dependency id="Antlr4.Runtime.Standard" version="4.13.1" exclude="Build,Analyzers" />
        <dependency id="MathNet.Symbolics" version="0.25.0" exclude="Build,Analyzers" />
        <dependency id="System.Text.Encoding.CodePages" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="vbeDecoder" version="1.0.2" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="C:\Projects\Tools\VbDeobf\Sources\vbSparkle\bin\Debug\net6.0\vbSparkle.dll" target="lib\net6.0\vbSparkle.dll" />
    <file src="C:\Projects\Tools\VbDeobf\Sources\vbSparkle\bin\Debug\net8.0\vbSparkle.dll" target="lib\net8.0\vbSparkle.dll" />
    <file src="C:\Projects\Tools\VbDeobf\Sources\vbSparkle\bin\Debug\netstandard2.0\vbSparkle.dll" target="lib\netstandard2.0\vbSparkle.dll" />
    <file src="C:\Projects\Tools\VbDeobf\LICENSE.txt" target="LICENSE.txt" />
  </files>
</package>