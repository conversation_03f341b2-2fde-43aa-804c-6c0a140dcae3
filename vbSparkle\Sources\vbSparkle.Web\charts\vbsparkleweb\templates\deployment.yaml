apiVersion: apps/v1beta2
kind: Deployment
metadata:
  name: {{ template "vbsparkleweb.fullname" . }}
  labels:
    app: {{ template "vbsparkleweb.name" . }}
    chart: {{ template "vbsparkleweb.chart" . }}
    draft: {{ default "draft-app" .Values.draft }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ template "vbsparkleweb.name" . }}
      release: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app: {{ template "vbsparkleweb.name" . }}
        draft: {{ default "draft-app" .Values.draft }}
        release: {{ .Release.Name }}
      annotations:
        buildID: {{ .Values.buildID }}
    spec:
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: 8000
              protocol: TCP
          {{- if .Values.probes.enabled }}
          livenessProbe:
            httpGet:
              path: /
              port: http
          readinessProbe:
            httpGet:
              path: /
              port: http
          {{- end }}
          env:
            {{- $root := . }}
            {{- range $ref, $values := .Values.secrets }}
            {{- range $key, $value := $values }}
            - name: {{ $ref }}_{{ $key }}
              valueFrom:
                secretKeyRef:
                  name: {{ template "vbsparkleweb.fullname" $root }}-{{ $ref | lower }}
                  key: {{ $key }}
            {{- end }}
            {{- end }}
          resources:
{{ toYaml .Values.resources | indent 12 }}
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
    {{- end }}
