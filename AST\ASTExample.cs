using System;
using System.IO;
using Antlr4.Runtime;
using Antlr4.Runtime.Tree;

namespace VbDeobf.AST
{
    /// <summary>
    /// Example usage of the VBA AST system
    /// </summary>
    public class ASTExample
    {
        /// <summary>
        /// Parse VBA code and convert to AST
        /// </summary>
        /// <param name="vbaCode">VBA source code</param>
        /// <param name="fileName">Optional file name for source location tracking</param>
        /// <returns>AST root node</returns>
        public static ModuleNode ParseVBACode(string vbaCode, string? fileName = null)
        {
            // Create ANTLR input stream
            var inputStream = new AntlrInputStream(vbaCode);

            // Create lexer
            var lexer = new VBALexer(inputStream);

            // Create token stream
            var tokenStream = new CommonTokenStream(lexer);

            // Create parser
            var parser = new VBAParser(tokenStream);

            // Parse starting from the module rule
            var parseTree = parser.startRule();

            // Convert CST to AST
            var visitor = new CSTToASTVisitor(fileName);
            var astNode = visitor.Visit(parseTree);

            if (astNode is ModuleNode module)
            {
                return module;
            }

            throw new InvalidOperationException("Failed to parse VBA code into a valid module AST");
        }

        /// <summary>
        /// Example of basic AST usage
        /// </summary>
        public static void BasicExample()
        {
            Console.WriteLine("=== VBA AST Basic Example ===");

            // Sample VBA code - simplified for debugging
            var vbaCode = @"
Public Function AddNumbers(x As Integer, y As Integer) As Integer
    Dim result As Integer
    result = x + y
    AddNumbers = result
End Function
";

            try
            {
                // Parse the VBA code
                var ast = ParseVBACode(vbaCode, "example.bas");

                // Display AST structure
                Console.WriteLine("AST Structure:");
                Console.WriteLine(ast.ToDebugString());

                // Get statistics
                var stats = ASTUtilities.GetStatistics(ast);
                Console.WriteLine("\nAST Statistics:");
                Console.WriteLine(stats.ToString());

                // Find all functions
                var functions = ASTUtilities.FindNodesOfType<FunctionDeclarationNode>(ast);
                Console.WriteLine("\nFunctions found:");
                foreach (var func in functions)
                {
                    Console.WriteLine($"  - {func.Name} ({func.Visibility})");
                }

                // Find all subroutines
                var subroutines = ASTUtilities.FindNodesOfType<SubroutineDeclarationNode>(ast);
                Console.WriteLine("\nSubroutines found:");
                foreach (var sub in subroutines)
                {
                    Console.WriteLine($"  - {sub.Name} ({sub.Visibility})");
                }

                // Find all variables
                var variables = ASTUtilities.GetAllVariables(ast);
                Console.WriteLine("\nVariables found:");
                foreach (var var in variables)
                {
                    Console.WriteLine($"  - {var.Name} (Type: {var.Type?.Accept(new VBACodeGenerator()) ?? "Variant"})");
                }

                // Generate code back from AST
                var codeGenerator = new VBACodeGenerator();
                var generatedCode = ast.Accept(codeGenerator);

                Console.WriteLine("\nGenerated VBA Code:");
                Console.WriteLine("==================");
                Console.WriteLine(generatedCode);

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Example of identifier renaming (basic obfuscation)
        /// </summary>
        public static void IdentifierRenamingExample()
        {
            Console.WriteLine("\n=== Identifier Renaming Example ===");

            var vbaCode = @"
Public Function Calculate(value As Integer) As Integer
    Dim temp As Integer
    temp = value * 2
    Calculate = temp
End Function
";

            try
            {
                var ast = ParseVBACode(vbaCode, "rename_example.bas");

                Console.WriteLine("Original code:");
                var codeGenerator = new VBACodeGenerator();
                Console.WriteLine(ast.Accept(codeGenerator));

                // Rename identifiers
                Console.WriteLine("\nRenaming identifiers...");
                var renamedCount = 0;
                renamedCount += ASTUtilities.ReplaceIdentifier(ast, "Calculate", "Func1");
                renamedCount += ASTUtilities.ReplaceIdentifier(ast, "value", "param1");
                renamedCount += ASTUtilities.ReplaceIdentifier(ast, "temp", "var1");

                Console.WriteLine($"Renamed {renamedCount} identifiers");

                Console.WriteLine("\nObfuscated code:");
                Console.WriteLine(ast.Accept(codeGenerator));

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of AST validation
        /// </summary>
        public static void ValidationExample()
        {
            Console.WriteLine("\n=== AST Validation Example ===");

            var vbaCode = @"
Public Sub TestSub()
    Dim x As Integer
    x = 42
End Sub
";

            try
            {
                var ast = ParseVBACode(vbaCode, "validation_example.bas");

                // Validate the AST
                var errors = ASTUtilities.ValidateAST(ast);

                if (errors.Count == 0)
                {
                    Console.WriteLine("AST validation passed - no errors found");
                }
                else
                {
                    Console.WriteLine($"AST validation failed - {errors.Count} errors found:");
                    foreach (var error in errors)
                    {
                        Console.WriteLine($"  - {error}");
                    }
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of finding specific patterns in the AST
        /// </summary>
        public static void PatternSearchExample()
        {
            Console.WriteLine("\n=== Pattern Search Example ===");

            var vbaCode = @"
Public Sub TestPatterns()
    Dim i As Integer
    
    For i = 1 To 10
        If i Mod 2 = 0 Then
            Debug.Print i
        End If
    Next i
    
    Dim j As Integer
    j = 0
    Do While j < 5
        j = j + 1
        Debug.Print j
    Loop
End Sub
";

            try
            {
                var ast = ParseVBACode(vbaCode, "pattern_example.bas");

                // Find all For loops
                var forLoops = ASTUtilities.FindNodesOfType<ForStatementNode>(ast);
                Console.WriteLine($"Found {forLoops.Count()} For loops");

                // Find all If statements
                var ifStatements = ASTUtilities.FindNodesOfType<IfStatementNode>(ast);
                Console.WriteLine($"Found {ifStatements.Count()} If statements");

                // Find all Do loops
                var doLoops = ASTUtilities.FindNodesOfType<DoLoopStatementNode>(ast);
                Console.WriteLine($"Found {doLoops.Count()} Do loops");

                // Find all binary expressions
                var binaryExpressions = ASTUtilities.FindNodesOfType<BinaryExpressionNode>(ast);
                Console.WriteLine($"Found {binaryExpressions.Count()} binary expressions");

                // Find all assignment statements
                var assignments = ASTUtilities.FindNodesOfType<AssignmentStatementNode>(ast);
                Console.WriteLine($"Found {assignments.Count()} assignment statements");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Run all examples
        /// </summary>
        public static void RunAllExamples()
        {
            BasicExample();
            IdentifierRenamingExample();
            ValidationExample();
            PatternSearchExample();
        }
    }
}
