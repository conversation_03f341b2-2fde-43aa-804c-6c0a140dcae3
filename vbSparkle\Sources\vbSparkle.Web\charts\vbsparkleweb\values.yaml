# Default values for vbsparkleweb.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
fullnameOverride: vbsparkleweb
replicaCount: 1
image:
  repository: vbsparkleweb
  tag: stable
  pullPolicy: IfNotPresent
imagePullSecrets: []
  # Optionally specify an array of imagePullSecrets.
  # Secrets must be manually created in the namespace.
  # ref: https://kubernetes.io/docs/concepts/containers/images/#specifying-imagepullsecrets-on-a-pod
  #
  # This uses credentials from secret "myRegistryKeySecretName".
  # - name: myRegistryKeySecretName
service:
  type: ClusterIP
  port: 80

probes:
  enabled: false

ingress:
  enabled: false
  annotations:
    # kubernetes.io/tls-acme: "true"
  path: /
  # hosts:
  #   - chart-example.local
  tls: []
    # - secretName: chart-example-tls
    #   hosts:
    #     - chart-example.local
secrets: {}
  # Optionally specify a set of secret objects whose values
  # will be injected as environment variables by default.
  # You should add this section to a file like secrets.yaml
  # that is explicitly NOT committed to source code control
  # and then include it as part of your helm install step.
  # ref: https://kubernetes.io/docs/concepts/configuration/secret/
  #
  # This creates a secret "mysecret" and injects "mypassword"
  # as the environment variable mysecret_mypassword=password.
  # mysecret:
  #   mypassword: password
resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #  cpu: 100m
  #  memory: 128Mi
  # requests:
  #  cpu: 100m
  #  memory: 128Mi
nodeSelector: {}

tolerations: []

affinity: {}