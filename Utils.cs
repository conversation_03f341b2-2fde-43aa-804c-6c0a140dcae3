using Antlr4.Runtime;
using Antlr4.Runtime.Tree;

interface IRewriterVisitor
{
    TokenStreamRewriter Rewriter { get; set; }
}

public static class Utils
{
    public static T? TestStatementType<T>(IParseTree node)
    {
        if (node.GetType() == typeof(T))
        {
            return (T) node;
        }

        if (node.ChildCount == 1)
        {
            return TestStatementType<T>(node.GetChild(0));
        }

        return default;
    }

    public static T? BfsType<T>(IParseTree node)
    {
        return BfsTypeAll<T>(node).FirstOrDefault();
    }

    public static int IndexOf(IParseTree parent, IParseTree node)
    {
        for (var i = 0; i < parent.ChildCount; i++)
        {
            if (parent.GetChild(i) == node)
            {
                return i;
            }
        }
        return -1;
    }

    public static IEnumerable<T> BfsTypeAll<T>(IParseTree node)
    {
        var queue = new Queue<IParseTree>();
        queue.Enqueue(node);
        while (queue.Count > 0)
        {
            var child = queue.Dequeue();
            if (child.GetType() == typeof(T))
            {
                yield return (T) child;
            }
            for (var i = 0; i < child.ChildCount; i++)
            {
                queue.Enqueue(child.GetChild(i));
            }
        }
        yield break;
    }
}