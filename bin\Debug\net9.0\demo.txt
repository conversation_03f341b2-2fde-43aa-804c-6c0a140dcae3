﻿Sub 生成可视图()
Dim │L─└└, ───┤┘
│L─└└ = Timer
┌└─└┤
───┤┘ = Timer
L_┌┤┌ = Format(───┤┘ - │L─└└, └_┘┐┤("0085008300850085"))
MsgBox └_┘┐┤("65c563c3674966055be16265ff61806c664b") _
& L_┌┤┌ & └_┘┐┤("7a27")
End Sub
Private Sub ┌└─└┤()
Dim L─┌┌┤┘┘—, L─┬┌┘┐┤┌
Dim L_—_┌ As Workbook, └┘┘┤┌ As Worksheet
│└┌_L = —┤┌L─(4).Path & └_┘┐┤("00b166c3906f729d95559224008300cd00c100c800cd")
Dim │┐┐L— As Object, │—┌┐┐
Set │┐┐L— = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
Set L_—_┌ = —┤┌L─(1).Open(│└┌_L)
Set └┘┘┤┌ = L_—_┌.Sheets(((&H13 - &H12)))
─_┌__ = (&H15 - &H13)
└┬─┌—─┐:
If ─_┌__ > └┘┘┤┌.Cells(—┤┌L─(15).Count, └_┘┐┤("0096")).End(xlUp).Row Then GoTo ┐│─┐┤┐┐
│—┌┐┐ = └┘┘┤┌.Cells(─_┌__, ((&H1A - &H18)))
│┐┐L—(│—┌┐┐) = └┘┘┤┌.Cells(─_┌__, └_┘┐┤("00a9")).Value
─_┌__ = ─_┌__ + 1
GoTo └┬─┌—─┐
┐│─┐┤┐┐:
L_—_┌.Close False
Sheet8.Range(└_┘┐┤("00960087")).Resize(((&H1D + &H26F3)), ((&H16 + &H19))).Clear
Sheet7.Range(└_┘┐┤("00960087")).Resize(((&H11 + &H26FF)), ((&H1A - &H18))).Copy Sheet8.Range(└_┘┐┤("00960087"))
┌_│─┘ = Sheet7.Range(└_┘┐┤("00ad009b00960086")).End(xlToLeft).Column
─L┐┐— = ┌_│─┘ - ((&H1C - &H1A))
─┌_┤┤ = ┌_│─┘ - ((&H16 - &H10))
┘─┘│L = ┌_│─┘ - ((&H1C - &HF))
┤─┐┌L = ┌_│─┘ - ((&H19 + &H4))
Dim │┌_┤└ As Range
Dim ─┌L│L As Range
Dim ┐┘┐┘L As Range
Dim │_┘┌L As Range
Dim ─_│┘— As Object, ┌L─—L
Set ─_│┘— = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
Dim LL┤│┘ As Object
Set LL┤│┘ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
Dim —L│─┌ As Object
Set —L│─┌ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
Dim ┌┐─┤┌ As Object
Set ┌┐─┤┌ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
─_┌__ = (&H16 - &H14)
┌─┘┐┌┬┬:
If ─_┌__ > Sheet7.Cells(—┤┌L─(15).Count, └_┘┐┤("0096")).End(xlUp).Row Then GoTo ┘└┤┬┐┌┐
Set │┌_┤└ = Sheet7.Cells(─_┌__, ─L┐┐—).Resize(((&H17 - &H16)), ((&H18 - &H15)))
Set ─┌L│L = Sheet7.Cells(─_┌__, ─┌_┤┤).Resize(((&H19 - &H18)), ((&HF - &H8)))
Set ┐┘┐┘L = Sheet7.Cells(─_┌__, ┘─┘│L).Resize(((&H11 - &H10)), ((&HB + &H3)))
Set │_┘┌L = Sheet7.Cells(─_┌__, ┤─┐┌L).Resize(((&H1A - &H19)), ((&H19 + &H5)))
┌L─—L = Sheet7.Cells(─_┌__, ((&HE - &HD)))
─_│┘—(┌L─—L) = —┤┌L─(0).Sum(│┌_┤└)
LL┤│┘(┌L─—L) = —┤┌L─(0).Sum(─┌L│L)
—L│─┌(┌L─—L) = —┤┌L─(0).Sum(┐┘┐┘L)
┌┐─┤┌(┌L─—L) = —┤┌L─(0).Sum(│_┘┌L)
─_┌__ = ─_┌__ + 1
GoTo ┌─┘┐┌┬┬
┘└┤┬┐┌┐:
—┘┌┐┤ = (&H10 + &H8)
Sheet8.Activate
Dim │┐L_— As Range
L──└— = —┤┌L─(8, —┤┌L─(15).Count, ((&H15 - &H14))).End(xlUp).Row
─_┌__ = (&H11 - &HF)
└└└┬┤┐─:
If ─_┌__ > L──└— Then GoTo ┐└││┌─┬
┌L─—L = —┤┌L─(8, ─_┌__, ((&H13 - &H12)))
—┤┌L─(8, ─_┌__, ((&HD - &HA))) = —┘┌┐┤
—┤┌L─(8, ─_┌__, ((&HE - &H9))) = ─_│┘—(┌L─—L)
—┤┌L─(8, ─_┌__, ((&H10 - &HA))) = LL┤│┘(┌L─—L)
—┤┌L─(8, ─_┌__, ((&H19 - &H12))) = —L│─┌(┌L─—L)
—┤┌L─(8, ─_┌__, ((&H1A - &H12))) = ┌┐─┤┌(┌L─—L)
—┤┌L─(8, ─_┌__, └_┘┐┤("009e")) = Format(─_│┘—(┌L─—L) / ((&H11 - &HE)), └_┘┐┤("0085008300850085"))
—┤┌L─(8, ─_┌__, └_┘┐┤("009f")) = Format(LL┤│┘(┌L─—L) / ((&H18 - &H11)), └_┘┐┤("0085008300850085"))
—┤┌L─(8, ─_┌__, └_┘┐┤("00a0")) = Format(—L│─┌(┌L─—L) / ((&H1A - &HC)), └_┘┐┤("0085008300850085"))
—┤┌L─(8, ─_┌__, └_┘┐┤("00a1")) = Format(┌┐─┤┌(┌L─—L) / ((&H1D + &H1)), └_┘┐┤("0085008300850085"))
Set │┐L_— = —┤┌L─(9, └_┘┐┤("009e") _
& ─_┌__).Resize(((&H16 - &H15)), ((&HD - &H9)))
—┤┌L─(8, ─_┌__, └_┘┐┤("00a2")) = Format(—┤┌L─(0).WorksheetFunction.Average(│┐L_—), └_┘┐┤("0085008300850085"))
─_┌__ = ─_┌__ + 1
GoTo └└└┬┤┐─
┐└││┌─┬:
│└┌_L = —┤┌L─(4).Path & └_┘┐┤("00b166c3906f729d95559224008300cd00c100c800cd")
Set L_—_┌ = —┤┌L─(1).Open(│└┌_L)
Set └┘┘┤┌ = L_—_┌.Sheets(((&H16 - &H15)))
Dim ┐│└┤│ As Range
Set ┐│└┤│ = └┘┘┤┌.Rows(└_┘┐┤("0086008f0086"))
┤┘┤─┐ = —┤┌L─(0).Match(└_┘┐┤("009b0097009654445583"), ┐│└┤│, ((&H15 - &H15)))
L—┘┤┐ = —┤┌L─(0).Match(└_┘┐┤("009b0097009698d975ae"), ┐│└┤│, ((&HD - &HD)))
─L──└ = —┤┌L─(0).Match(└_┘┐┤("009b00970096577d9069"), ┐│└┤│, ((&HD - &HD)))
┌┤┐_— = —┤┌L─(0).Match(└_┘┐┤("678157855ee85bad"), ┐│└┤│, ((&H11 - &H11)))
└│┐│┤ = —┤┌L─(0).Match(└_┘┐┤("921c8d82577d9069"), ┐│└┤│, ((&HB - &HB)))
Dim └┌┘┌┌ As Object
Set └┌┘┌┌ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
Dim └_└L─ As Object
Set └_└L─ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
Dim ┘│L┌┤ As Object
Set ┘│L┌┤ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
Dim ┘┌—┐┤ As Object
Set ┘┌—┐┤ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
Dim —L┌__ As Object
Set —L┌__ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
L──└— = └┘┘┤┌.Cells(—┤┌L─(15).Count, ((&H14 - &H13))).End(xlUp).Row
─_┌__ = (&H15 - &H13)
││┘┘┤─┬:
If ─_┌__ > L──└— Then GoTo │┘┐─┘┌┤
┌L─—L = └┘┘┤┌.Cells(─_┌__, ((&H14 - &H12)))
└┌┘┌┌(┌L─—L) = └┘┘┤┌.Cells(─_┌__, ┤┘┤─┐)
└_└L─(┌L─—L) = └┘┘┤┌.Cells(─_┌__, L—┘┤┐)
┘│L┌┤(┌L─—L) = └┘┘┤┌.Cells(─_┌__, ─L──└)
┘┌—┐┤(┌L─—L) = └┘┘┤┌.Cells(─_┌__, ┌┤┐_—)
—L┌__(┌L─—L) = └┘┘┤┌.Cells(─_┌__, └│┐│┤)
─_┌__ = ─_┌__ + 1
GoTo ││┘┘┤─┬
│┘┐─┘┌┤:
L_—_┌.Close False
Dim │└┐┐┘ As Object, └L┐──
Set │└┐┐┘ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
Dim —┌—L└ As Object
Set —┌—L└ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
Dim ┌└┐┐┤ As Object
Set ┌└┐┐┤ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
Dim └LL—│ As Object
Set └LL—│ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
─_┌__ = (&HB - &H9)
——┌──┬┌:
If ─_┌__ > Sheet5.Cells(—┤┌L─(15).Count, └_┘┐┤("0096")).End(xlUp).Row Then GoTo ─┘┐┌┌—┤
└L┐── = Sheet5.Cells(─_┌__, ((&H1C - &H1B)))
│└┐┐┘(└L┐──) = Sheet5.Cells(─_┌__, ((&HE - &HC)))
—┌—L└(└L┐──) = Sheet5.Cells(─_┌__, ((&H12 - &HF)))
┌└┐┐┤(└L┐──) = Sheet5.Cells(─_┌__, ((&H14 - &H10)))
└LL—│(└L┐──) = Sheet5.Cells(─_┌__, ((&H1B - &H16)))
─_┌__ = ─_┌__ + 1
GoTo ——┌──┬┌
─┘┐┌┌—┤:
Dim L──│┘ As Object, │┘┌└┌
Set L──│┘ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
Sheet1.Activate
─_┌__ = (&H12 - &H10)
┘—│┌─┌—:
If ─_┌__ > —┤┌L─(8, —┤┌L─(15).Count, ((&HC - &HA))).End(xlUp).Row Then GoTo ─┐│──┘│
│┘┌└┌ = —┤┌L─(8, ─_┌__, ((&HD - &HA)))
L──│┘(│┘┌└┌) = —┤┌L─(8, ─_┌__, ((&H18 - &H16)))
─_┌__ = ─_┌__ + 1
GoTo ┘—│┌─┌—
─┐│──┘│:
Sheet8.Activate
L──└— = —┤┌L─(8, —┤┌L─(15).Count, ((&H18 - &H17))).End(xlUp).Row
│┤┐─│ = Year(Date)
─│┤—┤ = Format(Month(Date), └_┘┐┤("00850085"))
—_└┐L = │┤┐─│ & ─│┤—┤
│_┤─┌ = Format(—┤┌L─(0).WorksheetFunction.EoMonth(Date, ((&H17 - &H17))), └_┘┐┤("00850085008500850085")) - Format(Date, └_┘┐┤("00850085008500850085"))
Dim —└┐│_ As Range
Set —└┐│_ = Sheet2.Columns(└_┘┐┤("0096008f0096"))
Dim ┐└_│┤ As Range
Set ┐└_│┤ = Sheet2.Rows(└_┘┐┤("0086008f0086"))
─┐┤┌┘ = —┤┌L─(0).Match(—└┐L, —└┐│, ((&H1A - &H1A)))
Set —││┤┘ = Sheet2.Range(└_┘┐┤("00960086")).CurrentRegion
─_┌__ = (&H10 - &HE)
┤—─┐┐┤─:
If ─_┌__ > L──└— Then GoTo ┐─┌──—└
┌L─—L = —┤┌L─(8, ─_┌__, ((&HC - &HB)))
└L┐── = —┤┌L─(8, ─_┌__, ((&H14 - &H12)))
—┤┌L─(8, ─_┌__, └_┘┐┤("00a3")) = —┤┌L─(0).WorksheetFunction.Max(└┌┘┌┌(┌L─—L), ((&HE - &HE)))
—┤┌L─(8, ─_┌__, └_┘┐┤("00a4")) = —┤┌L─(0).WorksheetFunction.Max(└_└L─(┌L─—L), ((&H1C - &H1C)))
—┤┌L─(8, ─_┌__, └_┘┐┤("00a5")) = —┤┌L─(0).WorksheetFunction.Max(┘│L┌┤(┌L─—L), ((&H1C - &H1C)))
—┤┌L─(8, ─_┌__, └_┘┐┤("00a6")) = —┤┌L─(0).WorksheetFunction.Max(┘┌—┐┤(┌L─—L), ((&H14 - &H14)))
—┤┌L─(8, ─_┌__, └_┘┐┤("00a7")) = —┤┌L─(0).WorksheetFunction.Max(—L┌__(┌L─—L), ((&H1A - &H1A)))
—┤┌L─(8, ─_┌__, └_┘┐┤("00a8")) = —┤┌L─(8, ─_┌__, └_┘┐┤("00a3")) + —┤┌L─(8, ─_┌__, └_┘┐┤("00a4"))
—┤┌L─(8, ─_┌__, └_┘┐┤("00a9")) = —┤┌L─(8, ─_┌__, └_┘┐┤("00a3")) + —┤┌L─(8, ─_┌__, └_┘┐┤("00a4")) + —┤┌L─(8, ─_┌__, └_┘┐┤("00a5"))
L┌└_┌ = —┤┌L─(8, ─_┌__, └_┘┐┤("00a8"))
─—└—_ = —┤┌L─(8, ─_┌__, └_┘┐┤("009e"))
┤__┤│ = —┤┌L─(8, ─_┌__, └_┘┐┤("009f"))
└┌┘_┘ = —┤┌L─(8, ─_┌__, └_┘┐┤("00a0"))
┐└┌_L = —┤┌L─(8, ─_┌__, └_┘┐┤("00a1"))
If ┐└┌_L > ((&H10 - &H10)) Then
—┤┌L─(8, ─_┌__, └_┘┐┤("00aa")) = ┌_└_└(L┌└_┌, ─—└—_ * ((&HF - &HC)), ┤__┤│ * ((&H15 - &HE)), └┌┘_┘ * ((&HD + &H1)), ┐└┌_L * ((&H10 + &HE)), │└┐┐┘(└L┐──), —┌—L└(└L┐──), ┌└┐┐┤(└L┐──), └LL—│(└L┐──))
┌─┤┌_ = L──│┘(┌L─—L)
┘┘─│─ = —┤┌L─(0).Match(┌─┤┌_, ┐└_│┤, ((&H15 - &H15)))
└┌—│L = —┤┌L─(0).Index(—││┤┘, ─┐┤┌┘, ┘┘─│─)
—┤┌L─(8, ─_┌__, └_┘┐┤("00ab")) = ┘_┤L└(—┤┌L─(8, ─_┌__, └_┘┐┤("00aa")), └┌—│L, │_┤─┌, ─┐┤┌┘, ┘┘─│─)
┐─_┐┘ = —┤┌L─(8, ─_┌__, └_┘┐┤("00a9"))
—┤┌L─(8, ─_┌__, └_┘┐┤("00ac")) = —L└┤─(┐─_┐┘, ─—└—_ * ((&H17 - &H14)), ┤__┤│ * ((&H19 - &H12)), └┌┘_┘ * ((&H14 - &H6)), ┐└┌_L * ((&H16 + &H8)), │└┐┐┘(└L┐──), —┌—L└(└L┐──), ┌└┐┐┤(└L┐──), └LL—│(└L┐──))
—┤┌L─(8, ─_┌__, └_┘┐┤("00ad")) = ┘_┤L└(—┤┌L─(8, ─_┌__, └_┘┐┤("00ac")), └┌—│L, │_┤─┌, ─┐┤┌┘, ┘┘─│─)
┐L┤─L = —┤┌L─(8, ─_┌__, └_┘┐┤("00a9")) + —┤┌L─(8, ─_┌__, └_┘┐┤("00a6"))
—┤┌L─(8, ─_┌__, └_┘┐┤("00ae")) = L─┤L┐(┐L┤─L, ─—└—_ * ((&HE - &HB)), ┤__┤│ * ((&H1C - &H15)), └┌┘_┘ * ((&H10 - &H2)), ┐└┌_L * ((&H15 + &H9)), │└┐┐┘(└L┐──), —┌—L└(└L┐──), ┌└┐┐┤(└L┐──), └LL—│(└L┐──))
—┤┌L─(8, ─_┌__, └_┘┐┤("00af")) = ┘_┤L└(—┤┌L─(8, ─_┌__, └_┘┐┤("00ae")).Value, └┌—│L, │_┤─┌, ─┐┤┌┘, ┘┘─│─)
Else
—┤┌L─(8, ─_┌__, └_┘┐┤("00aa")) = (&HC - &HC)
—┤┌L─(8, ─_┌__, └_┘┐┤("00ab")) = (&H13 - &H13)
—┤┌L─(8, ─_┌__, └_┘┐┤("00ac")) = (&H18 - &H18)
—┤┌L─(8, ─_┌__, └_┘┐┤("00ad")) = (&H15 - &H15)
—┤┌L─(8, ─_┌__, └_┘┐┤("00ae")) = (&HD - &HD)
—┤┌L─(8, ─_┌__, └_┘┐┤("00af")) = (&H14 - &H14)
End If
─┐─L─ = —┤┌L─(8, ─_┌__, └_┘┐┤("00ad"))
If —┤┌L─(0).WorksheetFunction.IsNumber(─┐─L─) Then
If ─┐─L─ > ((&H16 + &H35)) Then
┐┘└┤┘ = (─—└—_ + ┤__┤│ + └┌┘_┘ + ┐└┌_L) / ((&H12 - &HE))
—┤┌L─(8, ─_┌__, └_┘┐┤("00960096")) = Round((─┐─L─ - ((&H11 + &H3A))) * ┐┘└┤┘, ((&H15 - &H15)))
Else
—┤┌L─(8, ─_┌__, └_┘┐┤("00960096")) = (&H14 - &H14)
End If
Else
—┤┌L─(8, ─_┌__, └_┘┐┤("00960096")) = (&H17 - &H17)
End If
┤┐┌│L = —┤┌L─(8, ─_┌__, └_┘┐┤("00af"))
If —┤┌L─(0).WorksheetFunction.IsNumber(┤┐┌│L) Then
If ┤┐┌│L > ((&HE + &H44)) Then
┐┘└┤┘ = (─—└—_ + ┤__┤│ + └┌┘_┘ + ┐└┌_L) / ((&H18 - &H14))
—┤┌L─(8, ─_┌__, └_┘┐┤("00960097")) = Round((┤┐┌│L - ((&H18 + &H3A))) * ┐┘└┤┘, ((&H19 - &H19)))
Else
—┤┌L─(8, ─_┌__, └_┘┐┤("00960097")) = (&HB - &HB)
End If
End If
—┌─┘┘ = —┤┌L─(8, ─_┌__, └_┘┐┤("00ab"))
If —┤┌L─(0).WorksheetFunction.IsNumber(—┌─┘┘) Then
If —┌─┘┘ < ((&H1A - &H10)) Then
—┤┌L─(8, ─_┌__, └_┘┐┤("00960098")) = └_┘┐┤("975589d6")
Else
—┤┌L─(8, ─_┌__, └_┘┐┤("00960098")) = └_┘┐┤("4e62975589d6")
End If
End If
─_┌__ = ─_┌__ + 1
GoTo ┤—─┐┐┤─
┐─┌──—└:
L──└— = Sheet8.Cells(—┤┌L─(15).Count, ((&H18 - &H17))).End(xlUp).Row
─_┌__ = (&H16 - &H14)
┐┌┘┤┤└└:
If ─_┌__ > L──└— Then GoTo │┤─—┬┬┘
L─┌┌┤┘┘— = 1
┌││─┬┘┤:
If L─┌┌┤┘┘— > 1 Then GoTo ┬└│┘─│─
L─┬┌┘┐┤┌ = 1
┘┘┘│┤┌┤:
If L─┬┌┘┐┤┌ > 1 Then GoTo —┤┬┬—─┤
┤_│─┌ = (&HD + &H8)
│─┬┌│─—:
If ┤_│─┌ > ((&H18 + &H2)) Then GoTo ─—┤—┌┐└
—└└┘— = —┤┌L─(8, ─_┌__, ┤_│─┌)
If —└└┘— > ((&H13 + &HA1)) Then
—┤┌L─(8, ─_┌__, ┤_│─┌).Interior.Color = (&H1C + &HFFE3)
End If
┤_│─┌ = ┤_│─┌ + 1
GoTo │─┬┌│─—
─—┤—┌┐└:
L─┬┌┘┐┤┌ = L─┬┌┘┐┤┌ + 1
GoTo ┘┘┘│┤┌┤
—┤┬┬—─┤:
L─┌┌┤┘┘— = L─┌┌┤┘┘— + 1
GoTo ┌││─┬┘┤
┬└│┘─│─:
─_┌__ = ─_┌__ + 1
GoTo ┐┌┘┤┤└└
│┤─—┬┬┘:
│└┌_L = —┤┌L─(4).Path & └_┘┐┤("00b166c3906f729d95559224008300cd00c100c800cd")
Set L_—_┌ = —┤┌L─(1).Open(│└┌_L)
Set └┘┘┤┌ = L_—_┌.Sheets(((&HE - &HD)))
Set ┐│└┤│ = └┘┘┤┌.Rows(└_┘┐┤("0086008f0086"))
└│_─└ = —┤┌L─(0).Match(└_┘┐┤("891a7c0665c59224"), ┐│└┤│, ((&H14 - &H14)))
┐LL└└ = —┤┌L─(0).Match(└_┘┐┤("4efc55167f6b544c"), ┐│└┤│, ((&HE - &HE)))
L──└— = └┘┘┤┌.Cells(—┤┌L─(15).Count, ((&H15 - &H14))).End(xlUp).Row
Dim ─L┐└┌ As Object, │—LL┌
Set ─L┐└┌ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
─_┌__ = (&H1A - &H18)
┐──┬└│└:
If ─_┌__ > L──└— Then GoTo └└—┤┐┘┌
│—LL┌ = └┘┘┤┌.Cells(─_┌__, ┐LL└└)
─L┐└┌(│—LL┌) = └┘┘┤┌.Cells(─_┌__, └│_─└)
─_┌__ = ─_┌__ + 1
GoTo ┐──┬└│└
└└—┤┐┘┌:
L_—_┌.Close False
│└┌_L = —┤┌L─(4).Path & └_┘┐┤("00b188ba8d7c8bf65267008300cd00c100c800cd")
Set L_—_┌ = —┤┌L─(1).Open(│└┌_L)
Set └┘┘┤┌ = L_—_┌.Sheets(((&H18 - &H17)))
L──└— = └┘┘┤┌.Cells(—┤┌L─(15).Count, ((&H15 - &H14))).End(xlUp).Row
Dim ─│─┌┘ As Object, L┘┌_┐
Set ─│─┌┘ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
─_┌__ = (&H1A - &H18)
└┌─┐—┌┤:
If ─_┌__ > L──└— Then GoTo ┬┌─┬┘┐—
└┌┤─┤ = └┘┘┤┌.Cells(─_┌__, ((&H11 - &HC)))
If InStr(└┌┤─┤, └_┘┐┤("6bb87442")) > ((&H15 - &H15)) Then
L┘┌_┐ = └┘┘┤┌.Cells(─_┌__, ((&H1C - &H1A)))
If Not ─│─┌┘.Exists(L┘┌_┐) Then
─│─┌┘(L┘┌_┐) = CDate(└┘┘┤┌.Cells(─_┌__, ((&HE - &H8))))
Else
─│─┌┘(L┘┌_┐) = —┤┌L─(0).WorksheetFunction.Min(─│─┌┘(L┘┌_┐), CDate(└┘┘┤┌.Cells(─_┌__, ((&H1D - &H17)))))
─│─┌┘(L┘┌_┐) = Format(─│─┌┘(L┘┌_┐), └_┘┐┤("00ce00ce00ce00ce008200c200c2008200b900b9"))
End If
End If
─_┌__ = ─_┌__ + 1
GoTo └┌─┐—┌┤
┬┌─┬┘┐—:
Dim │_—└└ As Object, L─│┐─
Set │_—└└ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
─_┌__ = (&HD - &HB)
┐│┬│—┌┘:
If ─_┌__ > L──└— Then GoTo └┬—┤┐└┘
└┌┤─┤ = └┘┘┤┌.Cells(─_┌__, ((&H1C - &H17)))
If InStr(└┌┤─┤, └_┘┐┤("66c3828e")) > ((&HD - &HD)) Then
L─│┐─ = └┘┘┤┌.Cells(─_┌__, ((&HC - &HA)))
If Not │_—└└.Exists(L─│┐─) Then
│_—└└(L─│┐─) = CDate(└┘┘┤┌.Cells(─_┌__, ((&H14 - &HE))))
Else
│_—└└(L─│┐─) = —┤┌L─(0).WorksheetFunction.Min(│_—└└(L─│┐─), CDate(└┘┘┤┌.Cells(─_┌__, ((&HC - &H6)))))
│_—└└(L─│┐─) = Format(│_—└└(L─│┐─), └_┘┐┤("00ce00ce00ce00ce008200c200c2008200b900b9"))
End If
End If
─_┌__ = ─_┌__ + 1
GoTo ┐│┬│—┌┘
└┬—┤┐└┘:
┤┌L┘│ = Sheet4.Cells(—┤┌L─(15).Count, ((&H1A - &H19))).End(xlUp).Row
┘┐┌L— = Sheet8.Cells(—┤┌L─(15).Count, ((&H16 - &H15))).End(xlUp).Row
─_┌__ = (&H14 - &H12)
┐└┐┘┌—┐:
If ─_┌__ > Sheet8.Cells(—┤┌L─(15).Count, └_┘┐┤("0096")).End(xlUp).Row Then GoTo ┤┐─┐└┘┐
│—LL┌ = Sheet8.Cells(─_┌__, ((&HF - &HE)))
Sheet8.Cells(─_┌__, └_┘┐┤("0096009b")) = ─L┐└┌(│—LL┌)
┤┐└── = Sheet8.Cells(─_┌__, └_┘┐┤("00a6"))
└│┐│┤ = Sheet8.Cells(─_┌__, └_┘┐┤("00a7"))
If Sheet8.Cells(─_┌__, └_┘┐┤("0096009b")) <> └_┘┐┤("") Then
Sheet8.Cells(─_┌__, └_┘┐┤("009600a0")) = Format(┤┐└── / Sheet8.Cells(─_┌__, └_┘┐┤("0096009b")), └_┘┐┤("0085008300850085"))
Sheet8.Cells(─_┌__, └_┘┐┤("009600a1")) = Format(└│┐│┤ / Sheet8.Cells(─_┌__, └_┘┐┤("0096009b")), └_┘┐┤("0085008300850085"))
Else
Sheet8.Cells(─_┌__, └_┘┐┤("009600a0")) = └_┘┐┤("975558c051ee7c068a19")
Sheet8.Cells(─_┌__, └_┘┐┤("009600a1")) = └_┘┐┤("975558c051ee7c068a19")
End If
Sheet8.Cells(─_┌__, └_┘┐┤("00960099")) = ┌┤┘│┤(L_—┌, └┘┘┤┌, L──└—, Sheet8.Cells(─_┌_, ((&H14 - &H13))).Value)
Sheet8.Cells(─_┌__, └_┘┐┤("0096009a")) = └L│LL(L_—┌, └┘┘┤┌, L──└—, Sheet8.Cells(─_┌_, ((&H11 - &H10))).Value)
L┘┌_┐ = Sheet8.Cells(─_┌__, ((&H18 - &H17)))
Sheet8.Cells(─_┌__, └_┘┐┤("0096009e")) = │┘┤└┌(Sheet8.Cells(─_┌__, └_┘┐┤("00960099")), L┘┌_┐, ─│─┌┘(L┘┌_┐))
Sheet8.Cells(─_┌__, └_┘┐┤("0096009f")) = ─┘┤┘└(Sheet8.Cells(─_┌__, └_┘┐┤("0096009a")), L┘┌_┐, │_—└└(L┘┌_┐))
┌┐—─— = ┘┌—(L┘┌_┐, Sheet8.Cells(─_┌, └_┘┐┤("0096009e")), │┐┐L—(L┘┌_┐))
Debug.Print ┌┐—─—
Sheet8.Cells(─_┌__, └_┘┐┤("0096009c")) = ┘─┌L─(Sheet4, ┤┌L┘│, Sheet8.Cells(─_┌__, └_┘┐┤("0096009e")), ┘┐┌L—, Sheet8, L┘┌_┐, Sheet8.Cells(─_┌__, └_┘┐┤("00a9"))) + ┌┐—─—
Sheet8.Cells(─_┌__, └_┘┐┤("0096009d")) = L┌└│─(Sheet4, ┤┌L┘│, Sheet8.Cells(─_┌__, └_┘┐┤("0096009f")), ┘┐┌L—, Sheet8, L┘┌_┐, Sheet8.Cells(─_┌__, └_┘┐┤("00a9")), Sheet8.Cells(─_┌__, └_┘┐┤("0096009c")))
Sheet8.Cells(─_┌__, └_┘┐┤("009600a2")) = └┘┘┐_(Sheet8.Cells(─_┌__, └_┘┐┤("0096009c")), Sheet8.Cells(─_┌__, └_┘┐┤("0096009d")), Sheet8.Cells(─_┌__, └_┘┐┤("00a6")), Sheet8.Cells(─_┌__, └_┘┐┤("00a7")))
Sheet8.Cells(─_┌__, └_┘┐┤("009600a3")) = Format(—└└──(Sheet8.Cells(─_┌__, └_┘┐┤("0096009e")), Sheet8.Cells(─_┌__, └_┘┐┤("0096009f")), Sheet8.Cells(─_┌__, └_┘┐┤("009600a2"))), └_┘┐┤("00ce00ce00ce00ce008200c200c2008200b900b9"))
─_┌__ = ─_┌__ + 1
GoTo ┐└┐┘┌—┐
┤┐─┐└┘┐:
L_—_┌.Close False
Set │┐L_— = Sheet8.Range(└_┘┐┤("00960086")).CurrentRegion
│┐L_—.Select
With —┤┌L─(7)
.HorizontalAlignment = xlCenter
.VerticalAlignment = xlCenter
End With
│┐L_—.Select
With —┤┌L─(7)
.HorizontalAlignment = xlGeneral
.VerticalAlignment = xlCenter
.ReadingOrder = xlContext
End With
With —┤┌L─(7)
.HorizontalAlignment = xlCenter
.VerticalAlignment = xlCenter
.ReadingOrder = xlContext
End With
—┤┌L─(7).Borders(xlDiagonalDown).LineStyle = xlNone
—┤┌L─(7).Borders(xlDiagonalUp).LineStyle = xlNone
With —┤┌L─(7).Borders(xlEdgeLeft)
.LineStyle = xlContinuous
.Weight = xlThin
End With
With —┤┌L─(7).Borders(xlEdgeTop)
.LineStyle = xlContinuous
.Weight = xlThin
End With
With —┤┌L─(7).Borders(xlEdgeBottom)
.LineStyle = xlContinuous
.Weight = xlThin
End With
With —┤┌L─(7).Borders(xlEdgeRight)
.LineStyle = xlContinuous
.Weight = xlThin
End With
With —┤┌L─(7).Borders(xlInsideVertical)
.LineStyle = xlContinuous
.Weight = xlThin
End With
With —┤┌L─(7).Borders(xlInsideHorizontal)
.LineStyle = xlContinuous
.Weight = xlThin
End With
With —┤┌L─(7).Font
.Name = └_┘┐┤("5be04fa8")
.Size = (&HE - &H3)
.Underline = xlUnderlineStyleNone
.ThemeFont = xlThemeFontNone
End With
Sheet1.Activate
End Sub
Private Function —L└┤─(└└│_─, ┘┤_L│, └└┘─└, ┐—┤└┘, —_─└L, ┐┌L┘┤, ─┤—_└, │__L─, ┘││_│)
If —_─└L > ((&H19 - &H19)) Then
┘┌┤┘┘ = ┘┤_L│ / ((&H17 - &H14)) * ┐┌L┘┤ + (└└┘─└ - ┘┤_L│) / ((&HE - &HA)) * ─┤—_└ + (┐—┤└┘ - └└┘─└) / ((&H1C - &H15)) * │__L─ + (—_─└L - ┐—┤└┘) / ((&H15 - &H5)) * ┘││_│
If ┘┌┤┘┘ > ((&H12 - &H12)) Then
—L└┤─ = Format(└└│_─ / ┘┌┤┘┘, └_┘┐┤("0085008300850085"))
Else
—L└┤─ = (&H12 - &H12)
End If
Else
—L└┤─ = (&H10 - &H10)
End If
If —L└┤─ > ((&H19 + &H9B)) Then
—L└┤─ = └_┘┐┤("597c4ee30086008d0085597e")
End If
End Function
Private Function L─┤L┐(L—┘—─, ┘┐──┐, L┐─┘┤, ┤┤┐—┐, ┌┐┌┤─, L└┐┤─, ┐—_│┤, ┤┘└┤L, │┐┘│└)
If ┌┐┌┤─ > ((&HF - &HF)) Then
┌_┘│┤ = ┘┐──┐ / ((&H11 - &HE)) * L└┐┤─ + (L┐─┘┤ - ┘┐──┐) / ((&H13 - &HF)) * ┐—_│┤ + (┤┤┐—┐ - L┐─┘┤) / ((&H18 - &H11)) * ┤┘└┤L + (┌┐┌┤─ - ┤┤┐—┐) / ((&H14 - &H4)) * │┐┘│└
If ┌_┘│┤ > ((&H18 - &H18)) Then
L─┤L┐ = Format(L—┘—─ / ┌_┘│┤, └_┘┐┤("0085008300850085"))
Else
L─┤L┐ = (&H14 - &H14)
End If
Else
L─┤L┐ = (&H1A - &H1A)
End If
If L─┤L┐ > ((&H10 + &HA4)) Then
L─┤L┐ = └_┘┐┤("597c4ee30086008d0085597e")
End If
End Function
Private Function ┘_┤L└(—┘│┌┌, ─┌┌┐┤, ──—L┌, ┤└│┌_, L┘│┘─)
Dim ┐┌┤L— As Range
Set ┐┌┤L— = Sheet2.Range(└_┘┐┤("00960086")).CurrentRegion
If —┘│┌┌ <> └_┘┐┤("597c4ee30086008d0085597e") Then
┘└┌└— = —┘│┌┌ / ─┌┌┐┤
If ┘└┌└— <= ──—L┌ Then
┘_┤L└ = ┘└┌└—
Else
┘└┌└— = ┘└┌└— - ──—L┌
If ┤└│┌_ + (&H1C - &H1B) <= ((&H1C - &HF)) Then
┤─│L└ = —┤┌L─(0).Index(┐┌┤L—, ┤└│┌_ + (&H19 - &H18), L┘│┘─)
End If
──—└│ = ┘└┌└— / ┤─│L└
—┌L┌┌ = Day(—┤┌L─(0).WorksheetFunction.EoMonth(DateSerial(Year(Date), Month(Date) + (&H17 - &H16), ((&H1A - &H19))), ((&H19 - &H19))))
If ──—└│ <= —┌L┌┌ Then
┘_┤L└ = ──—L┌ + ──—└│
Else
──—└│ = ──—└│ - —┌L┌┌
If ┤└│┌_ + (&HF - &HD) <= ((&HF - &H2)) Then
┤─│L└ = —┤┌L─(0).Index(┐┌┤L—, ┤└│┌_ + (&HB - &H9), L┘│┘─)
End If
┘─└│└ = ──—└│ / ┤─│L└
┌┐┘┐L = Day(—┤┌L─(0).WorksheetFunction.EoMonth(DateSerial(Year(Date), Month(Date) + (&H17 - &H15), ((&H15 - &H14))), ((&HC - &HC))))
If ┘─└│└ <= 第三个月的天数 Then
┘_┤L└ = ──—L┌ + —┌L┌┌ + ┘─└│└
Else
┘─└│└ = ┘─└│└ - ┌┐┘┐L
If ┤└│┌_ + (&H19 - &H16) <= ((&HC + &H1)) Then
┤─│L└ = —┤┌L─(0).Index(┐┌┤L—, ┤└│┌_ + (&H10 - &HD), L┘│┘─)
End If
┌┐┌┐_ = ┘─└│└ / ┤─│L└
┘┘┤─┌ = Day(—┤┌L─(0).WorksheetFunction.EoMonth(DateSerial(Year(Date), Month(Date) + (&H18 - &H15), ((&H1B - &H1A))), ((&HF - &HF))))
If ┌┐┌┐_ <= ┘┘┤─┌ Then
┘_┤L└ = ──—L┌ + —┌L┌┌ + ┌┐┘┐L + ┌┐┌┐_
Else
┌┐┌┐_ = ┌┐┌┐_ - ┘┘┤─┌
If ┤└│┌_ + (&H14 - &H10) <= ((&H15 - &H8)) Then
┤─│L└ = —┤┌L─(0).Index(┐┌┤L—, ┤└│┌_ + (&H1A - &H16), L┘│┘─)
End If
LL┐L— = ┌┐┌┐_ / ┤─│L└
└L┐┤┌ = Day(—┤┌L─(0).WorksheetFunction.EoMonth(DateSerial(Year(Date), Month(Date) + (&H15 - &H11), ((&H19 - &H18))), ((&H1D - &H1D))))
If LL┐L— <= └L┐┤┌ Then
┘_┤L└ = ──—L┌ + —┌L┌┌ + ┌┐┘┐L + ┘┘┤─┌ + LL┐L—
Else
LL┐L— = LL┐L— - └L┐┤┌
If ┤└│┌_ + (&H1D - &H18) <= ((&HD + &H0)) Then
┤─│L└ = —┤┌L─(0).Index(┐┌┤L—, ┤└│┌_ + (&H1D - &H18), L┘│┘─)
End If
┘│_── = LL┐L— / ┤─│L└
L└L┘│ = Day(—┤┌L─(0).WorksheetFunction.EoMonth(DateSerial(Year(Date), Month(Date) + (&H14 - &HF), ((&H1D - &H1C))), ((&H1B - &H1B))))
If ┘│_── <= L└L┘│ Then
┘_┤L└ = ──—L┌ + —┌L┌┌ + ┌┐┘┐L + ┘┘┤─┌ + └L┐┤┌ + ┘│_──
Else
┘│_── = ┘│_── - L└L┘│
If ┤└│┌_ + (&HF - &H9) <= ((&H1C - &HF)) Then
┤─│L└ = —┤┌L─(0).Index(┐┌┤L—, ┤└│┌_ + (&H10 - &HA), L┘│┘─)
End If
┌L─── = ┘│_── / ┤─│L└
┐—LL_ = Day(—┤┌L─(0).WorksheetFunction.EoMonth(DateSerial(Year(Date), Month(Date) + (&H13 - &HD), ((&H17 - &H16))), ((&H1A - &H1A))))
If ┌L─── <= ┐—LL_ Then
┘_┤L└ = ──—L┌ + —┌L┌┌ + ┌┐┘┐L + ┘┘┤─┌ + └L┐┤┌ + L└L┘│ + ┌L───
Else
End If
End If
End If
End If
End If
End If
End If
If ┘_┤L└ > ((&HC + &HA8)) Then
┘_┤L└ = └_┘┐┤("597c4ee30086008d0085597e")
Else
┘_┤L└ = Format(┘_┤L└, └_┘┐┤("0085008300850085"))
End If
Else
┘_┤L└ = └_┘┐┤("597c4ee30086008d0085597e")
End If
End Function
Private Function ┌_└_└(—┘┌┤L, ┌L┐┤┌, ─┌─LL, ─┐L┐└, └_┘└┘, └┘_│┐, ┘—┐_│, ┌┘┘—─, L│L─│)
If └_┘└┘ > ((&HD - &HD)) Then
—┌_┤_ = ┌L┐┤┌ / ((&H11 - &HE)) * └┘_│┐ + (─┌─LL - ┌L┐┤┌) / ((&H18 - &H14)) * ┘—┐_│ + (─┐L┐└ - ─┌─LL) / ((&H15 - &HE)) * ┌┘┘—─ + (└_┘└┘ - ─┐L┐└) / ((&HE + &H2)) * L│L─│
If —┌_┤_ > ((&H14 - &H14)) Then
┌_└_└ = Format(—┘┌┤L / —┌_┤_, └_┘┐┤("0085008300850085"))
Else
┌_└_└ = (&HE - &HE)
End If
Else
┌_└_└ = (&HE - &HE)
End If
If ┌_└_└ > ((&HB + &HA9)) Then
┌_└_└ = └_┘┐┤("597c4ee30086008d0085597e")
End If
End Function
Private Function ┌┤┘│┤(─┘┤_┐ As Workbook, │┘┌_│ As Worksheet, ┘_—─│, L│┤L┤)
└L┘┌│ = False
└_L_L = (&H16 - &H14)
┐│┐└┐│┘:
If └_L_L > ┘_—─│ Then GoTo ┘┐└─┌┬└
L—┘┘┌ = │┘┌_│.Cells(└_L_L, ((&H1D - &H1B)))
─│L┌┌ = │┘┌_│.Cells(└_L_L, ((&H12 - &HD)))
If L│┤L┤ = L—┘┘┌ Then
If InStr(─│L┌┌, └_┘┐┤("6bb87442")) > ((&H13 - &H13)) Then
┌┤┘│┤ = └_┘┐┤("54266bb87442")
└L┘┌│ = True
GoTo ┘┐└─┌┬└
End If
End If
└_L_L = └_L_L + 1
GoTo ┐│┐└┐│┘
┘┐└─┌┬└:
If Not └L┘┌│ Then
┌┤┘│┤ = └_┘┐┤("4e6254266bb87442")
End If
End Function
Private Function └L│LL(┌└┤_│ As Workbook, ┌┌┤—_ As Worksheet, ┘L┘┐—, ┌┘┤└L)
─└L┐│ = False
─┌L┘└ = (&H11 - &HF)
┐│┐└┐│┘:
If ─┌L┘└ > ┘L┘┐— Then GoTo ┘┐└─┌┬└
─┐┤_─ = ┌┌┤—_.Cells(─┌L┘└, ((&H14 - &H12)))
│┤—┘│ = ┌┌┤—_.Cells(─┌L┘└, ((&H1B - &H16)))
If ┌┘┤└L = ─┐┤_─ Then
If InStr(│┤—┘│, └_┘┐┤("66c3828e")) > ((&HC - &HC)) Then
└L│LL = └_┘┐┤("542666c3828e")
─└L┐│ = True
GoTo ┘┐└─┌┬└
End If
End If
─┌L┘└ = ─┌L┘└ + 1
GoTo ┐│┐└┐│┘
┘┐└─┌┬└:
If Not ─└L┐│ Then
└L│LL = └_┘┐┤("4e62542666c3828e")
End If
End Function
Private Function ┘─┌L─(L└┐┌— As Worksheet, L┐└│└, —L│┐, ││┘┌┌, └└┐┌┤ As Worksheet, ┌—┌└, ┘—└L┤)
Dim ─┤┤—│ As Object, L┤│——
Set ─┤┤—│ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
Dim ┘_┘─│ As Object
Set ┘_┘─│ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
Dim ││——L As Object
Set ││——L = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
Dim ┤┤—│─ As Object
Set ┤┤—│─ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
Dim —┌_┌_ As Range
Set —┌_┌_ = L└┐┌—.Rows(└_┘┐┤("0086008f0086"))
Dim —└L└┘ As Range
Dim L┘_L┌ As Range
┌┤┘_L = (&HE - &HC)
┐│┐└┐│┘:
If ┌┤┘_L > ││┘┌┌ Then GoTo ┘┐└─┌┬└
L┤│—— = └└┐┌┤.Cells(┌┤┘_L, ((&HE - &HD)))
││——L(L┤│——) = └└┐┌┤.Cells(┌┤┘_L, └_┘┐┤("00a1")) * ((&H19 - &H1))
┌┤┘_L = ┌┤┘_L + 1
GoTo ┐│┐└┐│┘
┘┐└─┌┬└:
If —_L│┐ <> └_┘┐┤("6635") Then
┌└│┤│ = (&H10 - &HF)
—┐┬─│┘─:
If ┌└│┤│ > ((&H16 + &H3D2)) Then GoTo ─┌─└——─
│┐—L└ = —┌_┌_.Cells(((&H19 - &H18)), ┌└│┤│).Value
If │┐—L└ = —_L│┐ Then
┐┤└┐— = ┌└│┤│
GoTo ─┌─└——─
End If
┌└│┤│ = ┌└│┤│ + 1
GoTo —┐┬─│┘─
─┌─└——─:
┌┤┘_L = (&H15 - &H13)
└——┤─│┐:
If ┌┤┘_L > L┐└│└ Then GoTo ┘┌┤┤┘│┌
L┤│—— = L└┐┌—.Cells(┌┤┘_L, ((&H11 - &H10)))
Set —└L└┘ = L└┐┌—.Cells(┌┤┘_L, ┐┤└┐—).Resize(((&HF - &HE)), ((&H1B + &H7)))
Set L┘_L┌ = L└┐┌—.Cells(┌┤┘_L, ┐┤└┐— + (&H1A + &H8)).Resize(((&HF - &HE)), ((&HF - &H8)))
─┤┤—│(L┤│——) = —┤┌L─(0).Sum(—└L└┘)
┘_┘─│(L┤│——) = —┤┌L─(0).Sum(L┘_L┌)
┤┤—│─(L┤│——) = ─┤┤—│(L┤│——) + ┘_┘─│(L┤│——) + ││——L(L┤│——)
┌┤┘_L = ┌┤┘_L + 1
GoTo └——┤─│┐
┘┌┤┤┘│┌:
┘─┌L─ = —┤┌L─(0).WorksheetFunction.Max(┤┤—│─(┌_—┌└) - ┘—└L┤, ((&H19 - &H19)))
Else
┘─┌L─ = (&H10 - &H10)
End If
End Function
Private Function L┌└│─(─_─L┤ As Worksheet, —__┘└, ┌┤┌—, ┐┤│┐┐, └_┤┤┌ As Worksheet, ┤│—L┘, │L_┤└, ┌L─┐)
Dim │┐┌┤┐ As Object, ┐L│┘┘
Set │┐┌┤┐ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
Dim ┌┐┤┤┘ As Object
Set ┌┐┤┤┘ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
Dim ┐┘—_┤ As Object
Set ┐┘—_┤ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
Dim ─┤┐┌┘ As Object
Set ─┤┐┌┘ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
Dim ─┘—│L As Range
Set ─┘—│L = ─_─L┤.Rows(└_┘┐┤("0086008f0086"))
Dim L│┤┌└ As Range
Dim —┌L─│ As Range
If ┌┤┌—_ <> └_┘┐┤("6635") Then
┘┘┤┤┌ = (&HE - &HD)
┐│┐└┐│┘:
If ┘┘┤┤┌ > ((&H17 + &H3D1)) Then GoTo ┘┐└─┌┬└
┐—L└┌ = ─┘—│L.Cells(((&HC - &HB)), ┘┘┤┤┌).Value
If ┐—L└┌ = ┌┤┌—_ Then
┐│┐┘┘ = ┘┘┤┤┌
GoTo ┘┐└─┌┬└
End If
┘┘┤┤┌ = ┘┘┤┤┌ + 1
GoTo ┐│┐└┐│┘
┘┐└─┌┬└:
└┐─┘┐ = (&H16 - &H14)
—┐┬─│┘─:
If └┐─┘┐ > ┐┤│┐┐ Then GoTo ─┌─└——─
┐L│┘┘ = └_┤┤┌.Cells(└┐─┘┐, ((&H17 - &H16)))
┐┘—_┤(┐L│┘┘) = └_┤┤┌.Cells(└┐─┘┐, └_┘┐┤("00a1")) * ((&H14 + &H4))
└┐─┘┐ = └┐─┘┐ + 1
GoTo —┐┬─│┘─
─┌─└——─:
└┐─┘┐ = (&H19 - &H17)
└——┤─│┐:
If └┐─┘┐ > —__┘└ Then GoTo ┘┌┤┤┘│┌
┐L│┘┘ = ─_─L┤.Cells(└┐─┘┐, ((&H11 - &H10)))
Set L│┤┌└ = ─_─L┤.Cells(└┐─┘┐, ┐│┐┘┘).Resize(((&H1B - &H1A)), ((&H12 + &H1C)))
Set —┌L─│ = ─_─L┤.Cells(└┐─┘┐, ┐│┐┘┘ + (&HC + &H22)).Resize(((&H1C - &H1B)), ((&H1D - &H16)))
│┐┌┤┐(┐L│┘┘) = —┤┌L─(0).Sum(L│┤┌└)
┌┐┤┤┘(┐L│┘┘) = —┤┌L─(0).Sum(—┌L─│)
─┤┐┌┘(┐L│┘┘) = │┐┌┤┐(┐L│┘┘) + ┌┐┤┤┘(┐L│┘┘) + ┐┘—_┤(┐L│┘┘)
└┐─┘┐ = └┐─┘┐ + 1
GoTo └——┤─│┐
┘┌┤┤┘│┌:
L┌└│─ = —┤┌L─(0).WorksheetFunction.Max(─┤┐┌┘(┤│—L┘) - │L_┤└ - ┌L─┐_, ((&H18 - &H18)))
Else
L┌└│─ = (&H1C - &H1C)
End If
End Function
Private Function └┘┘┐_(L└│┐┐, ─_─┤│, │└┌└L, —└│—_)
─┘__│ = L└│┐┐ + ─_─┤│
L│_L— = ─┘__│ - │└┌└L
If L│_L— > —└│—_ Then
└┘┘┐_ = L│_L— - —└│—_
Else
└┘┘┐_ = (&HB - &HB)
End If
End Function
Private Function —└└──(└││—┌, └┤L—┤, ─┤│┐_)
If ─┤│┐_ > ((&HE - &HE)) Then
—└└── = —┤┌L─(0).WorksheetFunction.Min(└││—┌, └┤L—┤) - ((&H1B - &H14))
End If
End Function
Private Function │┘┤└┌(—┐—└_, ┘L—┤—, L┘——┐)
If —┐—└_ = └_┘┐┤("54266bb87442") Then
─│┐┘┌ = —┤┌L─(0).WorksheetFunction.Weekday(L┘——┐, ((&H16 - &H14)))
If ─│┐┘┌ > ((&H1D - &H1B)) Then
│┘┤└┌ = CDate(L┘——┐) - (─│┐┘┌ - ((&HF - &HD)))
ElseIf ─│┐┘┌ < ((&HC - &HA)) Then
│┘┤└┌ = CDate(L┘——┐) + (&H14 - &H13)
Else
│┘┤└┌ = CDate(L┘——┐)
End If
Else
│┘┤└┌ = └_┘┐┤("6635")
End If
End Function
Private Function ─┘┤┘└(─└__└, │──┌—, L┐_└┌)
If ─└__└ = └_┘┐┤("542666c3828e") Then
L┤─│┘ = —┤┌L─(0).WorksheetFunction.Weekday(L┐_└┌, ((&H10 - &HE)))
If L┤─│┘ > ((&HF - &HC)) Then
─┘┤┘└ = CDate(L┐_└┌) - (L┤─│┘ - ((&H12 - &HF)))
ElseIf L┤─│┘ < ((&H1C - &H19)) Then
─┘┤┘└ = CDate(L┐_└┌) + (((&HF - &HC)) - L┤─│┘)
Else
─┘┤┘└ = CDate(L┐_└┌)
End If
Else
─┘┤┘└ = └_┘┐┤("6635")
End If
End Function
Private Function ┘┌—__(└┤└—└, └─└┘┘, ┘_│_│)
Dim L│┤—┬┬┌—, L┌┐│──┬─
Dim └_—└┤ As Object, ┘—L┘L, L—┤─│ As Range
Set └_—└┤ = CreateObject(└_┘┐┤("00c800b800c700be00c500c900be00c300bc008300b900be00b800c900be00c400c300b600c700ce"))
L│┘L┌ = (&H1B - &H19)
│┐┘──││:
If L│┘L┌ > Sheet4.Cells(—┤┌L─(15).Count, └_┘┐┤("0096")).End(xlUp).Row Then GoTo ─┐┌┐│┬─
┘—L┘L = Sheet4.Cells(L│┘L┌, ((&H1A - &H19)))
If LL┤─│ <> └_┘┐┤("") Then
Set L—┤─│ = Sheet4.Cells(L│┘L┌, LL┤─│).Resize(((&H1D - &H1C)), ┘_│_│)
└_—└┤(┘—L┘L) = —┤┌L─(0).Sum(L—┤─│)
Else
┘┌—__ = (&H1A - &H1A)
End If
L│┤—┬┬┌— = 1
│┌┘┐└┐│:
If L│┤—┬┬┌— > 1 Then GoTo └└—─┤┌└
L┌┐│──┬─ = 1
─┐—─┌└—:
If L┌┐│──┬─ > 1 Then GoTo ─┐┘│┬│┬
┤─┘_─ = (&H1D - &H1C)
┌┐│─┌││:
If ┤─┘_─ > ((&HD + &H3DB)) Then GoTo ┘┤┌┤┌│─
┤┐┌└└ = Sheet4.Cells(((&H1C - &H1B)), ┤─┘_─).Value
If └─└┘┘ <> └_┘┐┤("6635") Then
If ┤┐┌└└ = └─└┘┘ Then
LL┤─│ = ┤─┘_─
GoTo ┘┤┌┤┌│─
End If
Else
┘┌—__ = (&H16 - &H16)
End If
┤─┘_─ = ┤─┘_─ + 1
GoTo ┌┐│─┌││
┘┤┌┤┌│─:
L┌┐│──┬─ = L┌┐│──┬─ + 1
GoTo ─┐—─┌└—
─┐┘│┬│┬:
L│┤—┬┬┌— = L│┤—┬┬┌— + 1
GoTo │┌┘┐└┐│
└└—─┤┌└:
L│┘L┌ = L│┘L┌ + 1
GoTo │┐┘──││
─┐┌┐│┬─:
┘┌—__ = └_—└┤(└┤└—└)
End Function
Private Function └_┘┐┤(Optional │└│L─) As String
Static L┐┌L┐ As Object
If L┐┌L┐ Is Nothing Then
Set L┐┌L┐ = CreateObject("htmlfile")
L┐┌L┐.write "  function $$(c,d){var b='';var a;for(i=0;i<c.length;i=i+4){a=parseInt(c.substr(i,4),16);b+=String.fromCharCode(a-d)}return b};  "
End If
└_┘┐┤ = CallByName(L┐┌L┐.parentwindow, "$$", VbMethod, │└│L─, (&H1A + &H3B))
End Function
Function —┤┌L─(ParamArray ──┐│─())
Dim ┌─┤┌— As String
Static ┌┘L└—, └││_┤
If Not IsArray(┌┘L└—) Then
┌┘L└— = Split(└_┘┐┤("009600c500c500c100be00b800b600c900be00c400c3008100ac00c400c700c000b700c400c400c000c80081009600b800c900be00cb00ba00ac00c400c700c000b700c400c400c00081009600b800c900be00cb00ba00a800bd00ba00ba00c9008100a900bd00be00c800ac00c400c700c000b700c400c400c0008100ac00c400c700c000c800bd00ba00ba00c900c80081") _
& └_┘┐┤("00a800bd00ba00ba00c900c8008100a800ba00c100ba00b800c900be00c400c30081009800ba00c100c100c8008100a700b600c300bc00ba0081009600b800c900be00cb00ba009800ba00c100c10081009600b800c900be00cb00ba00ac00be00c300b900c400cc008100a800bd00b600c500ba00c8008100ac00be00c300b900c400cc00c80081009800c400c100ca00c200c300c8008100a700c400cc00c8"), └_┘┐┤("0081"))
Set └││_┤ = Application
End If
Select Case UBound(──┐│─)
Case 0
Set —┤┌L─ = CallByName(└││_┤, ┌─┤┌—, 2)
Case 1
Set —┤┌L─ = CallByName(└││_┤, ┌─┤┌—, 2, ──┐│─(1))
Case 2
Set —┤┌L─ = CallByName(└││_┤, ┌─┤┌—, 2, ──┐│─(1), ──┐│─(2))
End Select
End Function