﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>netstandard2.0;net6.0;net8.0</TargetFrameworks>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageRequireLicenseAcceptance>true</PackageRequireLicenseAcceptance>
    <PackageLicenseFile>LICENSE.txt</PackageLicenseFile>
    <PackageProjectUrl>https://github.com/sbruyere/vbSparkle</PackageProjectUrl>
    <RepositoryUrl>https://github.com/sbruyere/vbSparkle</RepositoryUrl>
    <PackageTags>vbscript, visualbasic, vba, deobfuscation, malware, reverse</PackageTags>
    <Description>vbSparkle is a source-to-source multi-platform Visual Basic deobfuscator based on partial-evaluation and is mainly dedicated to the analysis of malicious code written in VBScript and VBA (Office Macro).

It is written in native C# and provides a .Net Standard library, and works on Windows, Linux, MacOS, etc..

The parsing of Visual Basic Script and VBA is processed through the use of ANTLR grammar &amp; lexer parsers.</Description>
    <Copyright>Airbus CERT, Sylvain Bruyere</Copyright>
    <Authors>Airbus CERT, Sylvain Bruyere</Authors>
    <Company>Airbus</Company>
    <Version>*******</Version>
    <RootNamespace>vbSparkle</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Antlr4.Runtime.Standard" Version="4.13.1" />
    <PackageReference Include="MathNet.Symbolics" Version="0.25.0" />
    <PackageReference Include="System.Text.Encoding.CodePages" Version="8.0.0" />
    <PackageReference Include="vbeDecoder" Version="1.0.2" />
  </ItemGroup>

  <ItemGroup>
    <Antlr4 Update="VBPreprocessors.g4">
      <ForceAtn>false</ForceAtn>
    </Antlr4>
    <Antlr4 Update="VBScript-preprocessor.g4">
      <DefaultCustomToolNamespace>$([MSBuild]::ValueOrDefault('$(RootNamespace).%(DefaultCustomToolNamespace)', '').TrimEnd('.'))</DefaultCustomToolNamespace>
      <CustomToolNamespace>$([MSBuild]::ValueOrDefault(%(CustomToolNamespace), %(DefaultCustomToolNamespace)))</CustomToolNamespace>
    </Antlr4>
    <Antlr4 Update="VBScript.g4">
      <CustomToolNamespace>vbSparkle</CustomToolNamespace>
    </Antlr4>
  </ItemGroup>

  <ItemGroup>
    <None Include="..\..\LICENSE.txt">
      <Pack>True</Pack>
      <PackagePath></PackagePath>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
  </ItemGroup>

</Project>
