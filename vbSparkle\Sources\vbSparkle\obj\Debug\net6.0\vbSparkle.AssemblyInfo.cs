//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("Airbus")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyCopyrightAttribute("Airbus CERT, Sylvain Bruyere")]
[assembly: System.Reflection.AssemblyDescriptionAttribute(@"vbSparkle is a source-to-source multi-platform Visual Basic deobfuscator based on partial-evaluation and is mainly dedicated to the analysis of malicious code written in VBScript and VBA (Office Macro).

It is written in native C# and provides a .Net Standard library, and works on Windows, Linux, MacOS, etc..

The parsing of Visual Basic Script and VBA is processed through the use of ANTLR grammar & lexer parsers.")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("*******+d342d4cc136286c4c81c389ff78b1361833bd601")]
[assembly: System.Reflection.AssemblyProductAttribute("vbSparkle")]
[assembly: System.Reflection.AssemblyTitleAttribute("vbSparkle")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyMetadataAttribute("RepositoryUrl", "https://github.com/sbruyere/vbSparkle")]

// Generated by the MSBuild WriteCodeFragment class.

