{"format": 1, "restore": {"/mnt/c/Projects/Tools/VbDeobf/VbDeobf.csproj": {}}, "projects": {"/mnt/c/Projects/Tools/VbDeobf/VbDeobf.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/mnt/c/Projects/Tools/VbDeobf/VbDeobf.csproj", "projectName": "VbDeobf", "projectPath": "/mnt/c/Projects/Tools/VbDeobf/VbDeobf.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/mnt/c/Projects/Tools/VbDeobf/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Antlr4.Runtime.Standard": {"target": "Package", "version": "[4.13.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[9.0.5, 9.0.5]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/9.0.106/PortableRuntimeIdentifierGraph.json"}}}}}