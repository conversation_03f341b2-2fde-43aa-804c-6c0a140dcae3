//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     ANTLR Version: 4.13.1
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// Generated from VBAParser.g4 by ANTLR 4.13.1

// Unreachable code detected
#pragma warning disable 0162
// The variable '...' is assigned but its value is never used
#pragma warning disable 0219
// Missing XML comment for publicly visible type or member '...'
#pragma warning disable 1591
// Ambiguous reference in cref attribute
#pragma warning disable 419

using Antlr4.Runtime.Misc;
using Antlr4.Runtime.Tree;
using IToken = Antlr4.Runtime.IToken;

/// <summary>
/// This interface defines a complete generic visitor for a parse tree produced
/// by <see cref="VBAParser"/>.
/// </summary>
/// <typeparam name="Result">The return type of the visit operation.</typeparam>
[System.CodeDom.Compiler.GeneratedCode("ANTLR", "4.13.1")]
[System.CLSCompliant(false)]
public interface IVBAParserVisitor<Result> : IParseTreeVisitor<Result> {
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.startRule"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitStartRule([NotNull] VBAParser.StartRuleContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.module"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModule([NotNull] VBAParser.ModuleContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.moduleHeader"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleHeader([NotNull] VBAParser.ModuleHeaderContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.moduleConfigReferences"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleConfigReferences([NotNull] VBAParser.ModuleConfigReferencesContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.moduleConfigReferenceElement"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleConfigReferenceElement([NotNull] VBAParser.ModuleConfigReferenceElementContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.moduleConfig"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleConfig([NotNull] VBAParser.ModuleConfigContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.moduleConfigProperty"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleConfigProperty([NotNull] VBAParser.ModuleConfigPropertyContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.moduleConfigElement"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleConfigElement([NotNull] VBAParser.ModuleConfigElementContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.germanStyleFloatingPointNumber"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitGermanStyleFloatingPointNumber([NotNull] VBAParser.GermanStyleFloatingPointNumberContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.shortcut"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitShortcut([NotNull] VBAParser.ShortcutContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.resource"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitResource([NotNull] VBAParser.ResourceContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.moduleAttributes"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleAttributes([NotNull] VBAParser.ModuleAttributesContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.attributeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitAttributeStmt([NotNull] VBAParser.AttributeStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.attributeName"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitAttributeName([NotNull] VBAParser.AttributeNameContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.attributeValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitAttributeValue([NotNull] VBAParser.AttributeValueContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.moduleDeclarations"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleDeclarations([NotNull] VBAParser.ModuleDeclarationsContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>optionBaseStmt</c>
	/// labeled alternative in <see cref="VBAParser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOptionBaseStmt([NotNull] VBAParser.OptionBaseStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>optionCompareStmt</c>
	/// labeled alternative in <see cref="VBAParser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOptionCompareStmt([NotNull] VBAParser.OptionCompareStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>optionExplicitStmt</c>
	/// labeled alternative in <see cref="VBAParser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOptionExplicitStmt([NotNull] VBAParser.OptionExplicitStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>optionPrivateModuleStmt</c>
	/// labeled alternative in <see cref="VBAParser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOptionPrivateModuleStmt([NotNull] VBAParser.OptionPrivateModuleStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.moduleDeclarationsElement"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleDeclarationsElement([NotNull] VBAParser.ModuleDeclarationsElementContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.moduleVariableStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleVariableStmt([NotNull] VBAParser.ModuleVariableStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.moduleConstStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleConstStmt([NotNull] VBAParser.ModuleConstStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.moduleBody"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleBody([NotNull] VBAParser.ModuleBodyContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.moduleBodyElement"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleBodyElement([NotNull] VBAParser.ModuleBodyElementContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.block"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitBlock([NotNull] VBAParser.BlockContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.unterminatedBlock"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitUnterminatedBlock([NotNull] VBAParser.UnterminatedBlockContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.blockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitBlockStmt([NotNull] VBAParser.BlockStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.mainBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitMainBlockStmt([NotNull] VBAParser.MainBlockStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.fileStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitFileStmt([NotNull] VBAParser.FileStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.openStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOpenStmt([NotNull] VBAParser.OpenStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.pathName"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitPathName([NotNull] VBAParser.PathNameContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.modeClause"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModeClause([NotNull] VBAParser.ModeClauseContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.fileMode"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitFileMode([NotNull] VBAParser.FileModeContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.accessClause"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitAccessClause([NotNull] VBAParser.AccessClauseContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.access"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitAccess([NotNull] VBAParser.AccessContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.lock"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLock([NotNull] VBAParser.LockContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.lenClause"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLenClause([NotNull] VBAParser.LenClauseContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.recLength"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitRecLength([NotNull] VBAParser.RecLengthContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.fileNumber"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitFileNumber([NotNull] VBAParser.FileNumberContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.markedFileNumber"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitMarkedFileNumber([NotNull] VBAParser.MarkedFileNumberContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.unmarkedFileNumber"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitUnmarkedFileNumber([NotNull] VBAParser.UnmarkedFileNumberContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.closeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCloseStmt([NotNull] VBAParser.CloseStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.resetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitResetStmt([NotNull] VBAParser.ResetStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.fileNumberList"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitFileNumberList([NotNull] VBAParser.FileNumberListContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.seekStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSeekStmt([NotNull] VBAParser.SeekStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.position"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitPosition([NotNull] VBAParser.PositionContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.lockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLockStmt([NotNull] VBAParser.LockStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.recordRange"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitRecordRange([NotNull] VBAParser.RecordRangeContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.startRecordNumber"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitStartRecordNumber([NotNull] VBAParser.StartRecordNumberContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.endRecordNumber"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitEndRecordNumber([NotNull] VBAParser.EndRecordNumberContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.unlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitUnlockStmt([NotNull] VBAParser.UnlockStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.lineInputStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLineInputStmt([NotNull] VBAParser.LineInputStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.variableName"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVariableName([NotNull] VBAParser.VariableNameContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.widthStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitWidthStmt([NotNull] VBAParser.WidthStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.lineWidth"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLineWidth([NotNull] VBAParser.LineWidthContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.printMethod"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitPrintMethod([NotNull] VBAParser.PrintMethodContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.printStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitPrintStmt([NotNull] VBAParser.PrintStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.unqualifiedObjectPrintStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitUnqualifiedObjectPrintStmt([NotNull] VBAParser.UnqualifiedObjectPrintStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.outputList"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOutputList([NotNull] VBAParser.OutputListContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.outputItem"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOutputItem([NotNull] VBAParser.OutputItemContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.outputClause"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOutputClause([NotNull] VBAParser.OutputClauseContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.charPosition"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCharPosition([NotNull] VBAParser.CharPositionContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.outputExpression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOutputExpression([NotNull] VBAParser.OutputExpressionContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.spcClause"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSpcClause([NotNull] VBAParser.SpcClauseContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.spcNumber"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSpcNumber([NotNull] VBAParser.SpcNumberContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.tabClause"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitTabClause([NotNull] VBAParser.TabClauseContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.tabNumberClause"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitTabNumberClause([NotNull] VBAParser.TabNumberClauseContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.tabNumber"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitTabNumber([NotNull] VBAParser.TabNumberContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.writeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitWriteStmt([NotNull] VBAParser.WriteStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.inputStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitInputStmt([NotNull] VBAParser.InputStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.inputList"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitInputList([NotNull] VBAParser.InputListContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.inputVariable"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitInputVariable([NotNull] VBAParser.InputVariableContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.putStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitPutStmt([NotNull] VBAParser.PutStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.recordNumber"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitRecordNumber([NotNull] VBAParser.RecordNumberContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.data"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitData([NotNull] VBAParser.DataContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.getStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitGetStmt([NotNull] VBAParser.GetStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.variable"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVariable([NotNull] VBAParser.VariableContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.constStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitConstStmt([NotNull] VBAParser.ConstStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.constSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitConstSubStmt([NotNull] VBAParser.ConstSubStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.declareStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitDeclareStmt([NotNull] VBAParser.DeclareStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.argList"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitArgList([NotNull] VBAParser.ArgListContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.arg"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitArg([NotNull] VBAParser.ArgContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.argDefaultValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitArgDefaultValue([NotNull] VBAParser.ArgDefaultValueContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.defDirective"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitDefDirective([NotNull] VBAParser.DefDirectiveContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.defType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitDefType([NotNull] VBAParser.DefTypeContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.letterSpec"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLetterSpec([NotNull] VBAParser.LetterSpecContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.singleLetter"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSingleLetter([NotNull] VBAParser.SingleLetterContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.universalLetterRange"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitUniversalLetterRange([NotNull] VBAParser.UniversalLetterRangeContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.letterRange"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLetterRange([NotNull] VBAParser.LetterRangeContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.doLoopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitDoLoopStmt([NotNull] VBAParser.DoLoopStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.enumerationStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitEnumerationStmt([NotNull] VBAParser.EnumerationStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.enumerationStmt_Constant"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitEnumerationStmt_Constant([NotNull] VBAParser.EnumerationStmt_ConstantContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.endStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitEndStmt([NotNull] VBAParser.EndStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.eraseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitEraseStmt([NotNull] VBAParser.EraseStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.errorStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitErrorStmt([NotNull] VBAParser.ErrorStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.eventStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitEventStmt([NotNull] VBAParser.EventStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.exitStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitExitStmt([NotNull] VBAParser.ExitStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.forEachStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitForEachStmt([NotNull] VBAParser.ForEachStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.forNextStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitForNextStmt([NotNull] VBAParser.ForNextStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.stepStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitStepStmt([NotNull] VBAParser.StepStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.functionStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitFunctionStmt([NotNull] VBAParser.FunctionStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.functionName"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitFunctionName([NotNull] VBAParser.FunctionNameContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.goSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitGoSubStmt([NotNull] VBAParser.GoSubStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.goToStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitGoToStmt([NotNull] VBAParser.GoToStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.ifStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitIfStmt([NotNull] VBAParser.IfStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.elseIfBlock"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitElseIfBlock([NotNull] VBAParser.ElseIfBlockContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.elseBlock"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitElseBlock([NotNull] VBAParser.ElseBlockContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.singleLineIfStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSingleLineIfStmt([NotNull] VBAParser.SingleLineIfStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.ifWithNonEmptyThen"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitIfWithNonEmptyThen([NotNull] VBAParser.IfWithNonEmptyThenContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.ifWithEmptyThen"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitIfWithEmptyThen([NotNull] VBAParser.IfWithEmptyThenContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.singleLineElseClause"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSingleLineElseClause([NotNull] VBAParser.SingleLineElseClauseContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.listOrLabel"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitListOrLabel([NotNull] VBAParser.ListOrLabelContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.sameLineStatement"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSameLineStatement([NotNull] VBAParser.SameLineStatementContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.emptyThenStatement"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitEmptyThenStatement([NotNull] VBAParser.EmptyThenStatementContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.booleanExpression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitBooleanExpression([NotNull] VBAParser.BooleanExpressionContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.implementsStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitImplementsStmt([NotNull] VBAParser.ImplementsStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.letStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLetStmt([NotNull] VBAParser.LetStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.lsetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLsetStmt([NotNull] VBAParser.LsetStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.onErrorStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOnErrorStmt([NotNull] VBAParser.OnErrorStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.onGoToStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOnGoToStmt([NotNull] VBAParser.OnGoToStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.onGoSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOnGoSubStmt([NotNull] VBAParser.OnGoSubStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.propertyGetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitPropertyGetStmt([NotNull] VBAParser.PropertyGetStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.propertySetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitPropertySetStmt([NotNull] VBAParser.PropertySetStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.propertyLetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitPropertyLetStmt([NotNull] VBAParser.PropertyLetStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.raiseEventStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitRaiseEventStmt([NotNull] VBAParser.RaiseEventStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.eventArgumentList"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitEventArgumentList([NotNull] VBAParser.EventArgumentListContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.eventArgument"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitEventArgument([NotNull] VBAParser.EventArgumentContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.redimStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitRedimStmt([NotNull] VBAParser.RedimStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.redimDeclarationList"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitRedimDeclarationList([NotNull] VBAParser.RedimDeclarationListContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.redimVariableDeclaration"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitRedimVariableDeclaration([NotNull] VBAParser.RedimVariableDeclarationContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.midStatement"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitMidStatement([NotNull] VBAParser.MidStatementContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.modeSpecifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModeSpecifier([NotNull] VBAParser.ModeSpecifierContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.integerExpression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitIntegerExpression([NotNull] VBAParser.IntegerExpressionContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.callStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCallStmt([NotNull] VBAParser.CallStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.resumeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitResumeStmt([NotNull] VBAParser.ResumeStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.returnStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitReturnStmt([NotNull] VBAParser.ReturnStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.rsetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitRsetStmt([NotNull] VBAParser.RsetStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.stopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitStopStmt([NotNull] VBAParser.StopStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.nameStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitNameStmt([NotNull] VBAParser.NameStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.selectCaseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSelectCaseStmt([NotNull] VBAParser.SelectCaseStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.selectExpression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSelectExpression([NotNull] VBAParser.SelectExpressionContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.caseClause"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCaseClause([NotNull] VBAParser.CaseClauseContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.caseElseClause"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCaseElseClause([NotNull] VBAParser.CaseElseClauseContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.rangeClause"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitRangeClause([NotNull] VBAParser.RangeClauseContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.selectStartValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSelectStartValue([NotNull] VBAParser.SelectStartValueContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.selectEndValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSelectEndValue([NotNull] VBAParser.SelectEndValueContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.setStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSetStmt([NotNull] VBAParser.SetStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.subStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSubStmt([NotNull] VBAParser.SubStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.subroutineName"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSubroutineName([NotNull] VBAParser.SubroutineNameContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.udtDeclaration"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitUdtDeclaration([NotNull] VBAParser.UdtDeclarationContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.udtMemberList"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitUdtMemberList([NotNull] VBAParser.UdtMemberListContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.udtMember"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitUdtMember([NotNull] VBAParser.UdtMemberContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.untypedNameMemberDeclaration"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitUntypedNameMemberDeclaration([NotNull] VBAParser.UntypedNameMemberDeclarationContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.reservedNameMemberDeclaration"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitReservedNameMemberDeclaration([NotNull] VBAParser.ReservedNameMemberDeclarationContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.optionalArrayClause"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOptionalArrayClause([NotNull] VBAParser.OptionalArrayClauseContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.arrayDim"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitArrayDim([NotNull] VBAParser.ArrayDimContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.boundsList"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitBoundsList([NotNull] VBAParser.BoundsListContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.dimSpec"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitDimSpec([NotNull] VBAParser.DimSpecContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.lowerBound"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLowerBound([NotNull] VBAParser.LowerBoundContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.upperBound"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitUpperBound([NotNull] VBAParser.UpperBoundContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.constantExpression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitConstantExpression([NotNull] VBAParser.ConstantExpressionContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.variableStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVariableStmt([NotNull] VBAParser.VariableStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.variableListStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVariableListStmt([NotNull] VBAParser.VariableListStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.variableSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVariableSubStmt([NotNull] VBAParser.VariableSubStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.whileWendStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitWhileWendStmt([NotNull] VBAParser.WhileWendStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.withStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitWithStmt([NotNull] VBAParser.WithStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.lineSpecialForm"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLineSpecialForm([NotNull] VBAParser.LineSpecialFormContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.circleSpecialForm"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCircleSpecialForm([NotNull] VBAParser.CircleSpecialFormContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.scaleSpecialForm"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitScaleSpecialForm([NotNull] VBAParser.ScaleSpecialFormContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.pSetSpecialForm"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitPSetSpecialForm([NotNull] VBAParser.PSetSpecialFormContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.tuple"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitTuple([NotNull] VBAParser.TupleContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.lineSpecialFormOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLineSpecialFormOption([NotNull] VBAParser.LineSpecialFormOptionContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.unrestrictedIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitUnrestrictedIdentifier([NotNull] VBAParser.UnrestrictedIdentifierContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.legalLabelIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLegalLabelIdentifier([NotNull] VBAParser.LegalLabelIdentifierContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.identifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitIdentifier([NotNull] VBAParser.IdentifierContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.untypedIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitUntypedIdentifier([NotNull] VBAParser.UntypedIdentifierContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.typedIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitTypedIdentifier([NotNull] VBAParser.TypedIdentifierContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.identifierValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitIdentifierValue([NotNull] VBAParser.IdentifierValueContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.foreignName"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitForeignName([NotNull] VBAParser.ForeignNameContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.foreignIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitForeignIdentifier([NotNull] VBAParser.ForeignIdentifierContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.asTypeClause"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitAsTypeClause([NotNull] VBAParser.AsTypeClauseContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.baseType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitBaseType([NotNull] VBAParser.BaseTypeContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.comparisonOperator"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitComparisonOperator([NotNull] VBAParser.ComparisonOperatorContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>ctLiteralExpr</c>
	/// labeled alternative in <see cref="VBAParser.complexType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCtLiteralExpr([NotNull] VBAParser.CtLiteralExprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>ctLExpr</c>
	/// labeled alternative in <see cref="VBAParser.complexType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCtLExpr([NotNull] VBAParser.CtLExprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>ctBuiltInTypeExpr</c>
	/// labeled alternative in <see cref="VBAParser.complexType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCtBuiltInTypeExpr([NotNull] VBAParser.CtBuiltInTypeExprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>ctParenthesizedExpr</c>
	/// labeled alternative in <see cref="VBAParser.complexType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCtParenthesizedExpr([NotNull] VBAParser.CtParenthesizedExprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>ctTypeofexpr</c>
	/// labeled alternative in <see cref="VBAParser.complexType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCtTypeofexpr([NotNull] VBAParser.CtTypeofexprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>ctNewExpr</c>
	/// labeled alternative in <see cref="VBAParser.complexType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCtNewExpr([NotNull] VBAParser.CtNewExprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>ctMarkedFileNumberExpr</c>
	/// labeled alternative in <see cref="VBAParser.complexType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCtMarkedFileNumberExpr([NotNull] VBAParser.CtMarkedFileNumberExprContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.fieldLength"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitFieldLength([NotNull] VBAParser.FieldLengthContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.statementLabelDefinition"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitStatementLabelDefinition([NotNull] VBAParser.StatementLabelDefinitionContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.identifierStatementLabel"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitIdentifierStatementLabel([NotNull] VBAParser.IdentifierStatementLabelContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.standaloneLineNumberLabel"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitStandaloneLineNumberLabel([NotNull] VBAParser.StandaloneLineNumberLabelContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.combinedLabels"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCombinedLabels([NotNull] VBAParser.CombinedLabelsContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.lineNumberLabel"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLineNumberLabel([NotNull] VBAParser.LineNumberLabelContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.numberLiteral"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitNumberLiteral([NotNull] VBAParser.NumberLiteralContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.type"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitType([NotNull] VBAParser.TypeContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.typeHint"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitTypeHint([NotNull] VBAParser.TypeHintContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.visibility"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVisibility([NotNull] VBAParser.VisibilityContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>newExpr</c>
	/// labeled alternative in <see cref="VBAParser.expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitNewExpr([NotNull] VBAParser.NewExprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>unaryMinusOp</c>
	/// labeled alternative in <see cref="VBAParser.expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitUnaryMinusOp([NotNull] VBAParser.UnaryMinusOpContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>powOp</c>
	/// labeled alternative in <see cref="VBAParser.expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitPowOp([NotNull] VBAParser.PowOpContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>addOp</c>
	/// labeled alternative in <see cref="VBAParser.expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitAddOp([NotNull] VBAParser.AddOpContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>typeofexpr</c>
	/// labeled alternative in <see cref="VBAParser.expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitTypeofexpr([NotNull] VBAParser.TypeofexprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>intDivOp</c>
	/// labeled alternative in <see cref="VBAParser.expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitIntDivOp([NotNull] VBAParser.IntDivOpContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>logicalImpOp</c>
	/// labeled alternative in <see cref="VBAParser.expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLogicalImpOp([NotNull] VBAParser.LogicalImpOpContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>concatOp</c>
	/// labeled alternative in <see cref="VBAParser.expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitConcatOp([NotNull] VBAParser.ConcatOpContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>markedFileNumberExpr</c>
	/// labeled alternative in <see cref="VBAParser.expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitMarkedFileNumberExpr([NotNull] VBAParser.MarkedFileNumberExprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>modOp</c>
	/// labeled alternative in <see cref="VBAParser.expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModOp([NotNull] VBAParser.ModOpContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>lExpr</c>
	/// labeled alternative in <see cref="VBAParser.expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLExpr([NotNull] VBAParser.LExprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>multOp</c>
	/// labeled alternative in <see cref="VBAParser.expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitMultOp([NotNull] VBAParser.MultOpContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>logicalXorOp</c>
	/// labeled alternative in <see cref="VBAParser.expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLogicalXorOp([NotNull] VBAParser.LogicalXorOpContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>logicalAndOp</c>
	/// labeled alternative in <see cref="VBAParser.expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLogicalAndOp([NotNull] VBAParser.LogicalAndOpContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>logicalOrOp</c>
	/// labeled alternative in <see cref="VBAParser.expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLogicalOrOp([NotNull] VBAParser.LogicalOrOpContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>relationalOp</c>
	/// labeled alternative in <see cref="VBAParser.expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitRelationalOp([NotNull] VBAParser.RelationalOpContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>logicalEqvOp</c>
	/// labeled alternative in <see cref="VBAParser.expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLogicalEqvOp([NotNull] VBAParser.LogicalEqvOpContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>parenthesizedExpr</c>
	/// labeled alternative in <see cref="VBAParser.expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitParenthesizedExpr([NotNull] VBAParser.ParenthesizedExprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>literalExpr</c>
	/// labeled alternative in <see cref="VBAParser.expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLiteralExpr([NotNull] VBAParser.LiteralExprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>builtInTypeExpr</c>
	/// labeled alternative in <see cref="VBAParser.expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitBuiltInTypeExpr([NotNull] VBAParser.BuiltInTypeExprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>logicalNotOp</c>
	/// labeled alternative in <see cref="VBAParser.expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLogicalNotOp([NotNull] VBAParser.LogicalNotOpContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.literalExpression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLiteralExpression([NotNull] VBAParser.LiteralExpressionContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.literalIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLiteralIdentifier([NotNull] VBAParser.LiteralIdentifierContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.booleanLiteralIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitBooleanLiteralIdentifier([NotNull] VBAParser.BooleanLiteralIdentifierContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.objectLiteralIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitObjectLiteralIdentifier([NotNull] VBAParser.ObjectLiteralIdentifierContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.variantLiteralIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVariantLiteralIdentifier([NotNull] VBAParser.VariantLiteralIdentifierContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>indexExpr</c>
	/// labeled alternative in <see cref="VBAParser.lExpression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitIndexExpr([NotNull] VBAParser.IndexExprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>withMemberAccessExpr</c>
	/// labeled alternative in <see cref="VBAParser.lExpression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitWithMemberAccessExpr([NotNull] VBAParser.WithMemberAccessExprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>withDictionaryAccessExpr</c>
	/// labeled alternative in <see cref="VBAParser.lExpression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitWithDictionaryAccessExpr([NotNull] VBAParser.WithDictionaryAccessExprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>simpleNameExpr</c>
	/// labeled alternative in <see cref="VBAParser.lExpression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSimpleNameExpr([NotNull] VBAParser.SimpleNameExprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>memberAccessExpr</c>
	/// labeled alternative in <see cref="VBAParser.lExpression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitMemberAccessExpr([NotNull] VBAParser.MemberAccessExprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>objectPrintExpr</c>
	/// labeled alternative in <see cref="VBAParser.lExpression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitObjectPrintExpr([NotNull] VBAParser.ObjectPrintExprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>instanceExpr</c>
	/// labeled alternative in <see cref="VBAParser.lExpression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitInstanceExpr([NotNull] VBAParser.InstanceExprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>dictionaryAccessExpr</c>
	/// labeled alternative in <see cref="VBAParser.lExpression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitDictionaryAccessExpr([NotNull] VBAParser.DictionaryAccessExprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>whitespaceIndexExpr</c>
	/// labeled alternative in <see cref="VBAParser.lExpression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitWhitespaceIndexExpr([NotNull] VBAParser.WhitespaceIndexExprContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.dictionaryAccess"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitDictionaryAccess([NotNull] VBAParser.DictionaryAccessContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.builtInType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitBuiltInType([NotNull] VBAParser.BuiltInTypeContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.argumentList"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitArgumentList([NotNull] VBAParser.ArgumentListContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.requiredArgument"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitRequiredArgument([NotNull] VBAParser.RequiredArgumentContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.argument"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitArgument([NotNull] VBAParser.ArgumentContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.positionalArgument"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitPositionalArgument([NotNull] VBAParser.PositionalArgumentContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.namedArgument"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitNamedArgument([NotNull] VBAParser.NamedArgumentContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.missingArgument"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitMissingArgument([NotNull] VBAParser.MissingArgumentContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.argumentExpression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitArgumentExpression([NotNull] VBAParser.ArgumentExpressionContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.lowerBoundArgumentExpression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLowerBoundArgumentExpression([NotNull] VBAParser.LowerBoundArgumentExpressionContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.upperBoundArgumentExpression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitUpperBoundArgumentExpression([NotNull] VBAParser.UpperBoundArgumentExpressionContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.addressOfExpression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitAddressOfExpression([NotNull] VBAParser.AddressOfExpressionContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.keyword"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitKeyword([NotNull] VBAParser.KeywordContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.markerKeyword"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitMarkerKeyword([NotNull] VBAParser.MarkerKeywordContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.statementKeyword"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitStatementKeyword([NotNull] VBAParser.StatementKeywordContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.endOfLine"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitEndOfLine([NotNull] VBAParser.EndOfLineContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.endOfStatement"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitEndOfStatement([NotNull] VBAParser.EndOfStatementContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.individualNonEOFEndOfStatement"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitIndividualNonEOFEndOfStatement([NotNull] VBAParser.IndividualNonEOFEndOfStatementContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.commentOrAnnotation"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCommentOrAnnotation([NotNull] VBAParser.CommentOrAnnotationContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.remComment"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitRemComment([NotNull] VBAParser.RemCommentContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.comment"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitComment([NotNull] VBAParser.CommentContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.commentBody"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCommentBody([NotNull] VBAParser.CommentBodyContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.annotationList"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitAnnotationList([NotNull] VBAParser.AnnotationListContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.annotation"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitAnnotation([NotNull] VBAParser.AnnotationContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.annotationName"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitAnnotationName([NotNull] VBAParser.AnnotationNameContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.annotationArgList"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitAnnotationArgList([NotNull] VBAParser.AnnotationArgListContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.annotationArg"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitAnnotationArg([NotNull] VBAParser.AnnotationArgContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.mandatoryLineContinuation"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitMandatoryLineContinuation([NotNull] VBAParser.MandatoryLineContinuationContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VBAParser.whiteSpace"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitWhiteSpace([NotNull] VBAParser.WhiteSpaceContext context);
}
