using System;
using System.Linq;
using System.Text;

namespace VbDeobf.AST
{
    /// <summary>
    /// Generates VBA code from AST nodes
    /// </summary>
    public class VBACodeGenerator : BaseASTVisitor<string>
    {
        private int _indentLevel = 0;
        private readonly string _indentString = "    "; // 4 spaces
        
        /// <summary>
        /// Generate code with proper indentation
        /// </summary>
        private string Indent(string code = "")
        {
            return new string(' ', _indentLevel * _indentString.Length) + code;
        }
        
        /// <summary>
        /// Increase indentation level
        /// </summary>
        private void IncreaseIndent() => _indentLevel++;
        
        /// <summary>
        /// Decrease indentation level
        /// </summary>
        private void DecreaseIndent() => _indentLevel = Math.Max(0, _indentLevel - 1);
        
        /// <summary>
        /// Generate code for a module
        /// </summary>
        public override string VisitModule(ModuleNode node)
        {
            var sb = new StringBuilder();
            
            // Module header
            if (node.Header != null)
            {
                sb.AppendLine(node.Header.Accept(this));
            }
            
            // Module attributes
            foreach (var attr in node.Attributes)
            {
                sb.AppendLine(attr.Accept(this));
            }
            
            // Module declarations
            sb.AppendLine(node.Declarations.Accept(this));
            
            // Module body
            sb.AppendLine(node.Body.Accept(this));
            
            return sb.ToString();
        }
        
        /// <summary>
        /// Generate code for module header
        /// </summary>
        public override string VisitModuleHeader(ModuleHeaderNode node)
        {
            var result = $"VERSION {node.Version}";
            if (node.IsClass)
            {
                result += " CLASS";
            }
            return result;
        }
        
        /// <summary>
        /// Generate code for module declarations
        /// </summary>
        public override string VisitModuleDeclarations(ModuleDeclarationsNode node)
        {
            var sb = new StringBuilder();
            
            // Options
            foreach (var option in node.Options)
            {
                sb.AppendLine(option.Accept(this));
            }
            
            // Declare statements
            foreach (var declare in node.DeclareStatements)
            {
                sb.AppendLine(declare.Accept(this));
            }
            
            // Declarations
            foreach (var decl in node.Declarations)
            {
                sb.AppendLine(decl.Accept(this));
                sb.AppendLine(); // Add blank line after declarations
            }
            
            return sb.ToString();
        }
        
        /// <summary>
        /// Generate code for module body
        /// </summary>
        public override string VisitModuleBody(ModuleBodyNode node)
        {
            var sb = new StringBuilder();
            
            foreach (var procedure in node.Procedures)
            {
                sb.AppendLine(procedure.Accept(this));
                sb.AppendLine(); // Add blank line between procedures
            }
            
            return sb.ToString();
        }
        
        /// <summary>
        /// Generate code for function declaration
        /// </summary>
        public override string VisitFunctionDeclaration(FunctionDeclarationNode node)
        {
            var sb = new StringBuilder();
            
            // Function signature
            var signature = new StringBuilder();
            
            // Visibility
            if (node.Visibility != VisibilityModifier.Public)
            {
                signature.Append($"{node.Visibility} ");
            }
            
            // Static
            if (node.IsStatic)
            {
                signature.Append("Static ");
            }
            
            signature.Append($"Function {node.Name}");
            signature.Append(node.Parameters.Accept(this));
            
            // Return type
            if (node.ReturnType != null)
            {
                signature.Append($" As {node.ReturnType.Accept(this)}");
            }
            
            sb.AppendLine(Indent(signature.ToString()));
            
            // Function body
            IncreaseIndent();
            sb.Append(node.Body.Accept(this));
            DecreaseIndent();
            
            // End Function
            sb.AppendLine(Indent("End Function"));
            
            return sb.ToString();
        }
        
        /// <summary>
        /// Generate code for subroutine declaration
        /// </summary>
        public override string VisitSubroutineDeclaration(SubroutineDeclarationNode node)
        {
            var sb = new StringBuilder();
            
            // Subroutine signature
            var signature = new StringBuilder();
            
            // Visibility
            if (node.Visibility != VisibilityModifier.Public)
            {
                signature.Append($"{node.Visibility} ");
            }
            
            // Static
            if (node.IsStatic)
            {
                signature.Append("Static ");
            }
            
            signature.Append($"Sub {node.Name}");
            signature.Append(node.Parameters.Accept(this));
            
            sb.AppendLine(Indent(signature.ToString()));
            
            // Subroutine body
            IncreaseIndent();
            sb.Append(node.Body.Accept(this));
            DecreaseIndent();
            
            // End Sub
            sb.AppendLine(Indent("End Sub"));
            
            return sb.ToString();
        }
        
        /// <summary>
        /// Generate code for parameter list
        /// </summary>
        public override string VisitParameterList(ParameterListNode node)
        {
            if (node.Parameters.Count == 0)
                return "()";
            
            var parameters = node.Parameters.Select(p => p.Accept(this));
            return $"({string.Join(", ", parameters)})";
        }
        
        /// <summary>
        /// Generate code for parameter
        /// </summary>
        public override string VisitParameter(ParameterNode node)
        {
            var sb = new StringBuilder();
            
            if (node.IsOptional)
                sb.Append("Optional ");
            
            if (node.Modifier != ParameterModifier.None)
                sb.Append($"{node.Modifier} ");
            
            if (node.IsParamArray)
                sb.Append("ParamArray ");
            
            sb.Append(node.Name);
            
            if (node.Type != null)
                sb.Append($" As {node.Type.Accept(this)}");
            
            if (node.DefaultValue != null)
                sb.Append($" = {node.DefaultValue.Accept(this)}");
            
            return sb.ToString();
        }
        
        /// <summary>
        /// Generate code for block
        /// </summary>
        public override string VisitBlock(BlockNode node)
        {
            var sb = new StringBuilder();
            
            foreach (var statement in node.Statements)
            {
                sb.AppendLine(Indent(statement.Accept(this)));
            }
            
            return sb.ToString();
        }
        
        /// <summary>
        /// Generate code for assignment statement
        /// </summary>
        public override string VisitAssignmentStatement(AssignmentStatementNode node)
        {
            var keyword = node.IsSetAssignment ? "Set " : "";
            return $"{keyword}{node.Target.Accept(this)} = {node.Value.Accept(this)}";
        }
        
        /// <summary>
        /// Generate code for call statement
        /// </summary>
        public override string VisitCallStatement(CallStatementNode node)
        {
            var keyword = node.HasCallKeyword ? "Call " : "";
            return $"{keyword}{node.Expression.Accept(this)}";
        }
        
        /// <summary>
        /// Generate code for if statement
        /// </summary>
        public override string VisitIfStatement(IfStatementNode node)
        {
            var sb = new StringBuilder();
            
            sb.AppendLine($"If {node.Condition.Accept(this)} Then");
            
            IncreaseIndent();
            sb.Append(node.ThenBlock.Accept(this));
            DecreaseIndent();
            
            // ElseIf blocks
            foreach (var elseIf in node.ElseIfBlocks)
            {
                sb.AppendLine($"ElseIf {elseIf.Condition.Accept(this)} Then");
                IncreaseIndent();
                sb.Append(elseIf.Block.Accept(this));
                DecreaseIndent();
            }
            
            // Else block
            if (node.ElseBlock != null)
            {
                sb.AppendLine("Else");
                IncreaseIndent();
                sb.Append(node.ElseBlock.Block.Accept(this));
                DecreaseIndent();
            }
            
            sb.Append("End If");
            
            return sb.ToString();
        }
        
        /// <summary>
        /// Generate code for binary expression
        /// </summary>
        public override string VisitBinaryExpression(BinaryExpressionNode node)
        {
            var left = node.Left.Accept(this);
            var right = node.Right.Accept(this);
            var op = GetBinaryOperatorString(node.Operator);
            
            return $"{left} {op} {right}";
        }
        
        /// <summary>
        /// Generate code for unary expression
        /// </summary>
        public override string VisitUnaryExpression(UnaryExpressionNode node)
        {
            var operand = node.Operand.Accept(this);
            var op = GetUnaryOperatorString(node.Operator);
            
            return $"{op}{operand}";
        }
        
        /// <summary>
        /// Generate code for identifier expression
        /// </summary>
        public override string VisitIdentifierExpression(IdentifierExpressionNode node)
        {
            return node.Name;
        }
        
        /// <summary>
        /// Generate code for string literal
        /// </summary>
        public override string VisitStringLiteral(StringLiteralNode node)
        {
            return $"\"{node.Value}\"";
        }
        
        /// <summary>
        /// Generate code for number literal
        /// </summary>
        public override string VisitNumberLiteral(NumberLiteralNode node)
        {
            return node.Value.ToString() ?? "0";
        }
        
        /// <summary>
        /// Generate code for boolean literal
        /// </summary>
        public override string VisitBooleanLiteral(BooleanLiteralNode node)
        {
            return node.Value ? "True" : "False";
        }
        
        /// <summary>
        /// Get string representation of binary operator
        /// </summary>
        private string GetBinaryOperatorString(BinaryOperator op)
        {
            return op switch
            {
                BinaryOperator.Add => "+",
                BinaryOperator.Subtract => "-",
                BinaryOperator.Multiply => "*",
                BinaryOperator.Divide => "/",
                BinaryOperator.IntegerDivide => "\\",
                BinaryOperator.Modulo => "Mod",
                BinaryOperator.Power => "^",
                BinaryOperator.Concatenate => "&",
                BinaryOperator.Equal => "=",
                BinaryOperator.NotEqual => "<>",
                BinaryOperator.LessThan => "<",
                BinaryOperator.LessThanOrEqual => "<=",
                BinaryOperator.GreaterThan => ">",
                BinaryOperator.GreaterThanOrEqual => ">=",
                BinaryOperator.Like => "Like",
                BinaryOperator.Is => "Is",
                BinaryOperator.And => "And",
                BinaryOperator.Or => "Or",
                BinaryOperator.Xor => "Xor",
                BinaryOperator.Eqv => "Eqv",
                BinaryOperator.Imp => "Imp",
                _ => "+"
            };
        }
        
        /// <summary>
        /// Get string representation of unary operator
        /// </summary>
        private string GetUnaryOperatorString(UnaryOperator op)
        {
            return op switch
            {
                UnaryOperator.Plus => "+",
                UnaryOperator.Minus => "-",
                UnaryOperator.Not => "Not ",
                _ => ""
            };
        }
        
        /// <summary>
        /// Default visit returns empty string
        /// </summary>
        protected override string DefaultVisit(IASTNode node)
        {
            return "";
        }
    }
}
