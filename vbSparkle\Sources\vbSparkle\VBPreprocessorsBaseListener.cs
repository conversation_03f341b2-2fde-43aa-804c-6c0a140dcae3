//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     ANTLR Version: 4.13.1
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// Generated from VBPreprocessors.g4 by ANTLR 4.13.1

// Unreachable code detected
#pragma warning disable 0162
// The variable '...' is assigned but its value is never used
#pragma warning disable 0219
// Missing XML comment for publicly visible type or member '...'
#pragma warning disable 1591
// Ambiguous reference in cref attribute
#pragma warning disable 419


using Antlr4.Runtime.Misc;
using IErrorNode = Antlr4.Runtime.Tree.IErrorNode;
using ITerminalNode = Antlr4.Runtime.Tree.ITerminalNode;
using IToken = Antlr4.Runtime.IToken;
using ParserRuleContext = Antlr4.Runtime.ParserRuleContext;

/// <summary>
/// This class provides an empty implementation of <see cref="IVBPreprocessorsListener"/>,
/// which can be extended to create a listener which only needs to handle a subset
/// of the available methods.
/// </summary>
[System.CodeDom.Compiler.GeneratedCode("ANTLR", "4.13.1")]
[System.Diagnostics.DebuggerNonUserCode]
[System.CLSCompliant(false)]
public partial class VBPreprocessorsBaseListener : IVBPreprocessorsListener {
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.startRule"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterStartRule([NotNull] VBPreprocessorsParser.StartRuleContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.startRule"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitStartRule([NotNull] VBPreprocessorsParser.StartRuleContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.macroConst"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterMacroConst([NotNull] VBPreprocessorsParser.MacroConstContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.macroConst"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitMacroConst([NotNull] VBPreprocessorsParser.MacroConstContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.codeBlock"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCodeBlock([NotNull] VBPreprocessorsParser.CodeBlockContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.codeBlock"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCodeBlock([NotNull] VBPreprocessorsParser.CodeBlockContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vmacroIf</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.codeBlockBody"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVmacroIf([NotNull] VBPreprocessorsParser.VmacroIfContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vmacroIf</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.codeBlockBody"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVmacroIf([NotNull] VBPreprocessorsParser.VmacroIfContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vmacroConst</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.codeBlockBody"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVmacroConst([NotNull] VBPreprocessorsParser.VmacroConstContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vmacroConst</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.codeBlockBody"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVmacroConst([NotNull] VBPreprocessorsParser.VmacroConstContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vcommentBlock</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.codeBlockBody"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVcommentBlock([NotNull] VBPreprocessorsParser.VcommentBlockContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vcommentBlock</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.codeBlockBody"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVcommentBlock([NotNull] VBPreprocessorsParser.VcommentBlockContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vcodeBlock</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.codeBlockBody"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVcodeBlock([NotNull] VBPreprocessorsParser.VcodeBlockContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vcodeBlock</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.codeBlockBody"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVcodeBlock([NotNull] VBPreprocessorsParser.VcodeBlockContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vlineLabel</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.codeBlockBody"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVlineLabel([NotNull] VBPreprocessorsParser.VlineLabelContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vlineLabel</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.codeBlockBody"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVlineLabel([NotNull] VBPreprocessorsParser.VlineLabelContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.nonMacroCodeBlockX"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterNonMacroCodeBlockX([NotNull] VBPreprocessorsParser.NonMacroCodeBlockXContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.nonMacroCodeBlockX"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitNonMacroCodeBlockX([NotNull] VBPreprocessorsParser.NonMacroCodeBlockXContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.moduleInfo"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleInfo([NotNull] VBPreprocessorsParser.ModuleInfoContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.moduleInfo"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleInfo([NotNull] VBPreprocessorsParser.ModuleInfoContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.moduleReferences"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleReferences([NotNull] VBPreprocessorsParser.ModuleReferencesContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.moduleReferences"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleReferences([NotNull] VBPreprocessorsParser.ModuleReferencesContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.moduleReference"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleReference([NotNull] VBPreprocessorsParser.ModuleReferenceContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.moduleReference"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleReference([NotNull] VBPreprocessorsParser.ModuleReferenceContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.moduleReferenceValue"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleReferenceValue([NotNull] VBPreprocessorsParser.ModuleReferenceValueContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.moduleReferenceValue"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleReferenceValue([NotNull] VBPreprocessorsParser.ModuleReferenceValueContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.moduleReferenceComponent"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleReferenceComponent([NotNull] VBPreprocessorsParser.ModuleReferenceComponentContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.moduleReferenceComponent"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleReferenceComponent([NotNull] VBPreprocessorsParser.ModuleReferenceComponentContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.moduleHeader"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleHeader([NotNull] VBPreprocessorsParser.ModuleHeaderContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.moduleHeader"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleHeader([NotNull] VBPreprocessorsParser.ModuleHeaderContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.moduleConfig"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleConfig([NotNull] VBPreprocessorsParser.ModuleConfigContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.moduleConfig"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleConfig([NotNull] VBPreprocessorsParser.ModuleConfigContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.moduleConfigElement"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleConfigElement([NotNull] VBPreprocessorsParser.ModuleConfigElementContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.moduleConfigElement"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleConfigElement([NotNull] VBPreprocessorsParser.ModuleConfigElementContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.ambiguousIdentifier"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterAmbiguousIdentifier([NotNull] VBPreprocessorsParser.AmbiguousIdentifierContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.ambiguousIdentifier"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitAmbiguousIdentifier([NotNull] VBPreprocessorsParser.AmbiguousIdentifierContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.moduleAttributes"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleAttributes([NotNull] VBPreprocessorsParser.ModuleAttributesContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.moduleAttributes"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleAttributes([NotNull] VBPreprocessorsParser.ModuleAttributesContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.macroIfThenElseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterMacroIfThenElseStmt([NotNull] VBPreprocessorsParser.MacroIfThenElseStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.macroIfThenElseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitMacroIfThenElseStmt([NotNull] VBPreprocessorsParser.MacroIfThenElseStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.macroIfBlockCondStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterMacroIfBlockCondStmt([NotNull] VBPreprocessorsParser.MacroIfBlockCondStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.macroIfBlockCondStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitMacroIfBlockCondStmt([NotNull] VBPreprocessorsParser.MacroIfBlockCondStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.macroIfBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterMacroIfBlockStmt([NotNull] VBPreprocessorsParser.MacroIfBlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.macroIfBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitMacroIfBlockStmt([NotNull] VBPreprocessorsParser.MacroIfBlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.macroElseIfBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterMacroElseIfBlockStmt([NotNull] VBPreprocessorsParser.MacroElseIfBlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.macroElseIfBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitMacroElseIfBlockStmt([NotNull] VBPreprocessorsParser.MacroElseIfBlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.macroElseBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterMacroElseBlockStmt([NotNull] VBPreprocessorsParser.MacroElseBlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.macroElseBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitMacroElseBlockStmt([NotNull] VBPreprocessorsParser.MacroElseBlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.commentBlock"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCommentBlock([NotNull] VBPreprocessorsParser.CommentBlockContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.commentBlock"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCommentBlock([NotNull] VBPreprocessorsParser.CommentBlockContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.lineLabel"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLineLabel([NotNull] VBPreprocessorsParser.LineLabelContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.lineLabel"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLineLabel([NotNull] VBPreprocessorsParser.LineLabelContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.nonMacroCodeBlockLine"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterNonMacroCodeBlockLine([NotNull] VBPreprocessorsParser.NonMacroCodeBlockLineContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.nonMacroCodeBlockLine"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitNonMacroCodeBlockLine([NotNull] VBPreprocessorsParser.NonMacroCodeBlockLineContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.nonMacroCodeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterNonMacroCodeStmt([NotNull] VBPreprocessorsParser.NonMacroCodeStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.nonMacroCodeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitNonMacroCodeStmt([NotNull] VBPreprocessorsParser.NonMacroCodeStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.controlProperties"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterControlProperties([NotNull] VBPreprocessorsParser.ControlPropertiesContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.controlProperties"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitControlProperties([NotNull] VBPreprocessorsParser.ControlPropertiesContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.cp_Properties"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCp_Properties([NotNull] VBPreprocessorsParser.Cp_PropertiesContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.cp_Properties"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCp_Properties([NotNull] VBPreprocessorsParser.Cp_PropertiesContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.cp_NestedProperty"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCp_NestedProperty([NotNull] VBPreprocessorsParser.Cp_NestedPropertyContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.cp_NestedProperty"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCp_NestedProperty([NotNull] VBPreprocessorsParser.Cp_NestedPropertyContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.cp_ControlType"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCp_ControlType([NotNull] VBPreprocessorsParser.Cp_ControlTypeContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.cp_ControlType"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCp_ControlType([NotNull] VBPreprocessorsParser.Cp_ControlTypeContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.complexType"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterComplexType([NotNull] VBPreprocessorsParser.ComplexTypeContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.complexType"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitComplexType([NotNull] VBPreprocessorsParser.ComplexTypeContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.cp_ControlIdentifier"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCp_ControlIdentifier([NotNull] VBPreprocessorsParser.Cp_ControlIdentifierContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.cp_ControlIdentifier"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCp_ControlIdentifier([NotNull] VBPreprocessorsParser.Cp_ControlIdentifierContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.attributeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterAttributeStmt([NotNull] VBPreprocessorsParser.AttributeStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.attributeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitAttributeStmt([NotNull] VBPreprocessorsParser.AttributeStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.ifConditionStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterIfConditionStmt([NotNull] VBPreprocessorsParser.IfConditionStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.ifConditionStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitIfConditionStmt([NotNull] VBPreprocessorsParser.IfConditionStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsStruct</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsStruct([NotNull] VBPreprocessorsParser.VsStructContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsStruct</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsStruct([NotNull] VBPreprocessorsParser.VsStructContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsLiteral</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsLiteral([NotNull] VBPreprocessorsParser.VsLiteralContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsLiteral</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsLiteral([NotNull] VBPreprocessorsParser.VsLiteralContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsConstant</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsConstant([NotNull] VBPreprocessorsParser.VsConstantContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsConstant</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsConstant([NotNull] VBPreprocessorsParser.VsConstantContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsUnaryOperation</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsUnaryOperation([NotNull] VBPreprocessorsParser.VsUnaryOperationContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsUnaryOperation</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsUnaryOperation([NotNull] VBPreprocessorsParser.VsUnaryOperationContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsDualOperation</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsDualOperation([NotNull] VBPreprocessorsParser.VsDualOperationContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsDualOperation</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsDualOperation([NotNull] VBPreprocessorsParser.VsDualOperationContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>ltColor</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.delimitedLiteral"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLtColor([NotNull] VBPreprocessorsParser.LtColorContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>ltColor</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.delimitedLiteral"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLtColor([NotNull] VBPreprocessorsParser.LtColorContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>ltOctal</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.delimitedLiteral"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLtOctal([NotNull] VBPreprocessorsParser.LtOctalContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>ltOctal</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.delimitedLiteral"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLtOctal([NotNull] VBPreprocessorsParser.LtOctalContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>ltDate</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.delimitedLiteral"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLtDate([NotNull] VBPreprocessorsParser.LtDateContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>ltDate</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.delimitedLiteral"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLtDate([NotNull] VBPreprocessorsParser.LtDateContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>ltString</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.delimitedLiteral"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLtString([NotNull] VBPreprocessorsParser.LtStringContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>ltString</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.delimitedLiteral"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLtString([NotNull] VBPreprocessorsParser.LtStringContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>ltDouble</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLtDouble([NotNull] VBPreprocessorsParser.LtDoubleContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>ltDouble</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLtDouble([NotNull] VBPreprocessorsParser.LtDoubleContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>ltDelimited</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLtDelimited([NotNull] VBPreprocessorsParser.LtDelimitedContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>ltDelimited</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLtDelimited([NotNull] VBPreprocessorsParser.LtDelimitedContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>ltInteger</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLtInteger([NotNull] VBPreprocessorsParser.LtIntegerContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>ltInteger</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLtInteger([NotNull] VBPreprocessorsParser.LtIntegerContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>ltBoolean</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLtBoolean([NotNull] VBPreprocessorsParser.LtBooleanContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>ltBoolean</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLtBoolean([NotNull] VBPreprocessorsParser.LtBooleanContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.anytoken"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterAnytoken([NotNull] VBPreprocessorsParser.AnytokenContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.anytoken"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitAnytoken([NotNull] VBPreprocessorsParser.AnytokenContext context) { }

	/// <inheritdoc/>
	/// <remarks>The default implementation does nothing.</remarks>
	public virtual void EnterEveryRule([NotNull] ParserRuleContext context) { }
	/// <inheritdoc/>
	/// <remarks>The default implementation does nothing.</remarks>
	public virtual void ExitEveryRule([NotNull] ParserRuleContext context) { }
	/// <inheritdoc/>
	/// <remarks>The default implementation does nothing.</remarks>
	public virtual void VisitTerminal([NotNull] ITerminalNode node) { }
	/// <inheritdoc/>
	/// <remarks>The default implementation does nothing.</remarks>
	public virtual void VisitErrorNode([NotNull] IErrorNode node) { }
}
