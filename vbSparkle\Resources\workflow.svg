<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Généré par Microsoft Visio, SVG Export workflow.svg Page 1 -->
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:ev="http://www.w3.org/2001/xml-events"
		xmlns:v="http://schemas.microsoft.com/visio/2003/SVGExtensions/" width="10.8515in" height="4.57077in"
		viewBox="0 0 781.307 329.095" xml:space="preserve" color-interpolation-filters="sRGB" class="st20">
	<v:documentProperties v:langID="1036" v:viewMarkup="false"/>

	<style type="text/css">
	<![CDATA[
		.st1 {fill:#8e00e0;stroke:#ffffff;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.5}
		.st2 {fill:#feffff;font-family:Nirmala UI Semilight;font-size:0.75em}
		.st3 {fill:#d9e9e9;stroke:#ffffff;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.5}
		.st4 {fill:#00aba9;font-family:Nirmala UI Semilight;font-size:1.16666em;font-weight:bold}
		.st5 {fill:#1ba1e2;stroke:#1ba1e2;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.5}
		.st6 {fill:#fae6d9;stroke:#ffffff;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.5}
		.st7 {fill:#f09609;font-family:Nirmala UI Semilight;font-size:1.16666em;font-weight:bold}
		.st8 {marker-end:url(#mrkr4-25);stroke:#1998d6;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.5}
		.st9 {fill:#1998d6;fill-opacity:1;stroke:#1998d6;stroke-opacity:1;stroke-width:0.16556291390728}
		.st10 {fill:#d80073;stroke:#ffffff;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.5}
		.st11 {fill:#ffffff;font-family:Nirmala UI Semilight;font-size:0.666664em}
		.st12 {fill:#2b862b;stroke:#ffffff;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.5}
		.st13 {fill:#feffff;font-family:Nirmala UI Semilight;font-size:0.666664em}
		.st14 {fill:#d80073;stroke:#d80073;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.5}
		.st15 {fill:#339933}
		.st16 {stroke:#ffffff;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.5}
		.st17 {fill:#ffffff;font-family:Segoe UI;font-size:0.666664em}
		.st18 {font-size:1em}
		.st19 {fill:none;stroke:none;stroke-linecap:round;stroke-linejoin:round;stroke-width:0.5}
		.st20 {fill:none;fill-rule:evenodd;font-size:12px;overflow:visible;stroke-linecap:square;stroke-miterlimit:3}
	]]>
	</style>

	<defs id="Markers">
		<g id="lend4">
			<path d="M 2 1 L 0 0 L 2 -1 L 2 1 " style="stroke:none"/>
		</g>
		<marker id="mrkr4-25" class="st9" v:arrowType="4" v:arrowSize="2" v:setback="12.08" refX="-12.08" orient="auto"
				markerUnits="strokeWidth" overflow="visible">
			<use xlink:href="#lend4" transform="scale(-6.04,-6.04) "/>
		</marker>
	</defs>
	<g v:mID="0" v:index="1" v:groupContext="foregroundPage">
		<v:userDefs>
			<v:ud v:nameU="visNavType" v:val="VT0(1):26"/>
		</v:userDefs>
		<title>Page 1</title>
		<v:pageProperties v:drawingScale="0.0393701" v:pageScale="0.0393701" v:drawingUnits="24" v:shadowOffsetX="8.50394"
				v:shadowOffsetY="-8.50394"/>
		<v:layer v:name="Lien" v:index="0"/>
		<v:layer v:name="Diagramme de flux" v:index="1"/>
		<g id="shape94-1" v:mID="94" v:groupContext="shape" transform="translate(674.758,-31.2073)">
			<title>Feuille.94</title>
			<desc>Residual Script</desc>
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(32500):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
			<v:textRect cx="53.1496" cy="313.93" width="106.3" height="30.3307"/>
			<rect x="0" y="298.764" width="106.299" height="30.3307" class="st1"/>
			<text x="24.96" y="316.63" class="st2" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Residual Script</text>		</g>
		<g id="group27-4" transform="translate(433.104,-0.62298)" v:mID="27" v:groupContext="group">
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(20500):26"/>
			</v:userDefs>
			<title>Feuille.27</title>
			<g id="shape28-5" v:mID="28" v:groupContext="shape">
				<title>Feuille.28</title>
				<v:userDefs>
					<v:ud v:nameU="visNavOrder" v:val="VT0(16500):26"/>
				</v:userDefs>
				<rect x="0" y="74.0218" width="181.417" height="255.073" class="st3"/>
			</g>
			<g id="shape29-7" v:mID="29" v:groupContext="shape" transform="translate(0,-253.984)">
				<title>Feuille.29</title>
				<desc>Node Partial-Evaluators</desc>
				<v:userDefs>
					<v:ud v:nameU="visNavOrder" v:val="VT0(15500):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
				<v:textRect cx="90.7087" cy="314.922" width="181.42" height="28.3465"/>
				<rect x="0" y="300.749" width="181.417" height="28.3465" class="st3"/>
				<text x="10.01" y="319.12" class="st4" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Node       Partial-Evaluators</text>			</g>
		</g>
		<g id="shape48-10" v:mID="48" v:groupContext="shape" transform="translate(811.059,41.6507) rotate(90)">
			<title>Flèche Virage serré.48</title>
			<v:userDefs>
				<v:ud v:nameU="DblArrow" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
				<v:ud v:nameU="visNavOrder" v:val="VT0(30500):26"/>
			</v:userDefs>
			<path d="M0 329.1 L251.65 329.1 L251.65 132.76 L256.24 132.76 L242.06 118.59 L227.89 132.76 L232.48 132.76 L232.48 309.92
						 L0 309.92 L0 319.51 L0 329.1 Z" class="st5"/>
		</g>
		<g id="group24-12" transform="translate(143.224,-0.25)" v:mID="24" v:groupContext="group">
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(18500):26"/>
			</v:userDefs>
			<title>Feuille.24</title>
			<g id="shape21-13" v:mID="21" v:groupContext="shape">
				<title>Feuille.21</title>
				<v:userDefs>
					<v:ud v:nameU="visNavOrder" v:val="VT0(16500):26"/>
				</v:userDefs>
				<rect x="0" y="74.0218" width="187.833" height="255.073" class="st6"/>
			</g>
			<g id="shape20-15" v:mID="20" v:groupContext="shape" transform="translate(0,-253.984)">
				<title>Feuille.20</title>
				<desc>AST</desc>
				<v:userDefs>
					<v:ud v:nameU="visNavOrder" v:val="VT0(15500):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
				<v:textRect cx="93.9163" cy="314.922" width="187.84" height="28.3465"/>
				<rect x="0" y="300.749" width="187.833" height="28.3465" class="st6"/>
				<text x="82.46" y="319.12" class="st7" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>AST</text>			</g>
		</g>
		<g id="shape23-18" v:mID="23" v:groupContext="shape" transform="translate(534.682,41.6507) rotate(90)">
			<title>Flèche Virage serré</title>
			<v:userDefs>
				<v:ud v:nameU="DblArrow" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
				<v:ud v:nameU="visNavOrder" v:val="VT0(17500):26"/>
			</v:userDefs>
			<path d="M0 329.1 L250.66 329.1 L250.66 108.66 L256.24 108.66 L242.06 94.49 L227.89 108.66 L233.47 108.66 L233.47 311.91
						 L0 311.91 L0 320.5 L0 329.1 Z" class="st5"/>
		</g>
		<g id="shape12-20" v:mID="12" v:groupContext="shape" v:layerMember="0" transform="translate(180.298,-216.906)">
			<title>Lien dynamique</title>
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(8500):26"/>
			</v:userDefs>
			<path d="M7.09 329.1 L7.09 337.23" class="st8"/>
		</g>
		<g id="shape13-26" v:mID="13" v:groupContext="shape" v:layerMember="0" transform="translate(180.298,-174.387)">
			<title>Lien dynamique.13</title>
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(9500):26"/>
			</v:userDefs>
			<path d="M7.09 329.1 L7.09 337.23" class="st8"/>
		</g>
		<g id="shape14-31" v:mID="14" v:groupContext="shape" v:layerMember="0" transform="translate(180.298,-131.867)">
			<title>Lien dynamique.14</title>
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(10500):26"/>
			</v:userDefs>
			<path d="M7.09 329.1 L7.09 337.23" class="st8"/>
		</g>
		<g id="shape15-36" v:mID="15" v:groupContext="shape" v:layerMember="0" transform="translate(180.298,-92.1447)">
			<title>Lien dynamique.15</title>
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(11500):26"/>
			</v:userDefs>
			<path d="M7.09 331.89 L7.09 334.43" class="st8"/>
		</g>
		<g id="shape16-41" v:mID="16" v:groupContext="shape" v:layerMember="0" transform="translate(222.818,-66.5956)">
			<title>Lien dynamique.16</title>
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(12500):26"/>
			</v:userDefs>
			<path d="M0 329.1 L10.63 329.1 L10.63 292.17 L17.83 292.17" class="st8"/>
		</g>
		<g id="shape17-46" v:mID="17" v:groupContext="shape" v:layerMember="0" transform="translate(222.818,-59.509)">
			<title>Lien dynamique.17</title>
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(13500):26"/>
			</v:userDefs>
			<path d="M0 322.01 L17.83 322.01" class="st8"/>
		</g>
		<g id="shape18-51" v:mID="18" v:groupContext="shape" v:layerMember="0" transform="translate(222.818,-66.5956)">
			<title>Lien dynamique.18</title>
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(14500):26"/>
			</v:userDefs>
			<path d="M0 329.1 L8.58 329.1 L8.58 366.02 L17.83 366.02" class="st8"/>
		</g>
		<g id="group30-56" transform="translate(151.952,-15.4974)" v:mID="30" v:groupContext="group">
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(21500):26"/>
			</v:userDefs>
			<title>Feuille.30</title>
			<g id="shape4-57" v:mID="4" v:groupContext="shape" v:layerMember="1" transform="translate(-1.59872E-14,-158.889)">
				<title>Start/End</title>
				<desc>Module</desc>
				<v:custProps>
					<v:cp v:nameU="Cost" v:lbl="Coût" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="ProcessNumber" v:lbl="Numéro de processus" v:prompt="" v:type="2" v:format="" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Owner" v:lbl="Propriétaire" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Function" v:lbl="Fonction" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
					<v:cp v:nameU="StartDate" v:lbl="Date de début" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="EndDate" v:lbl="Date de fin" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Status" v:lbl="État" v:prompt="" v:type="4"
							v:format=";Non commencé;En cours;Terminé;Différé;En attente d’une contribution" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
				</v:custProps>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
					<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="visNavOrder" v:val="VT0(500):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
				<v:textRect cx="35.4331" cy="314.922" width="70.87" height="28.3465"/>
				<path d="M14.17 329.1 L56.69 329.1 A14.1732 14.1732 -180 0 0 56.69 300.75 L14.17 300.75 A14.1732 14.1732 -180 1 0
							 14.17 329.1 Z" class="st10"/>
				<text x="22.23" y="317.32" class="st11" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Module</text>			</g>
			<g id="shape5-60" v:mID="5" v:groupContext="shape" v:layerMember="1" transform="translate(-1.59872E-14,-116.37)">
				<title>Start/End.5</title>
				<desc>ModuleBody</desc>
				<v:custProps>
					<v:cp v:nameU="Cost" v:lbl="Coût" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="ProcessNumber" v:lbl="Numéro de processus" v:prompt="" v:type="2" v:format="" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Owner" v:lbl="Propriétaire" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Function" v:lbl="Fonction" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
					<v:cp v:nameU="StartDate" v:lbl="Date de début" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="EndDate" v:lbl="Date de fin" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Status" v:lbl="État" v:prompt="" v:type="4"
							v:format=";Non commencé;En cours;Terminé;Différé;En attente d’une contribution" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
				</v:custProps>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
					<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="visNavOrder" v:val="VT0(1500):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
				<v:textRect cx="35.4331" cy="314.922" width="70.87" height="28.3465"/>
				<path d="M14.17 329.1 L56.69 329.1 A14.1732 14.1732 -180 0 0 56.69 300.75 L14.17 300.75 A14.1732 14.1732 -180 1 0
							 14.17 329.1 Z" class="st10"/>
				<text x="13.53" y="317.32" class="st11" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>ModuleBody</text>			</g>
			<g id="shape6-63" v:mID="6" v:groupContext="shape" v:layerMember="1" transform="translate(-1.59872E-14,-201.409)">
				<title>Start/End.6</title>
				<desc>StartRule</desc>
				<v:custProps>
					<v:cp v:nameU="Cost" v:lbl="Coût" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="ProcessNumber" v:lbl="Numéro de processus" v:prompt="" v:type="2" v:format="" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Owner" v:lbl="Propriétaire" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Function" v:lbl="Fonction" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
					<v:cp v:nameU="StartDate" v:lbl="Date de début" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="EndDate" v:lbl="Date de fin" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Status" v:lbl="État" v:prompt="" v:type="4"
							v:format=";Non commencé;En cours;Terminé;Différé;En attente d’une contribution" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
				</v:custProps>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
					<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="visNavOrder" v:val="VT0(2500):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
				<v:textRect cx="35.4331" cy="314.922" width="70.87" height="28.3465"/>
				<path d="M14.17 329.1 L56.69 329.1 A14.1732 14.1732 -180 0 0 56.69 300.75 L14.17 300.75 A14.1732 14.1732 -180 1 0
							 14.17 329.1 Z" class="st10"/>
				<text x="20.01" y="317.32" class="st11" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>StartRule</text>			</g>
			<g id="shape7-66" v:mID="7" v:groupContext="shape" v:layerMember="1" transform="translate(-1.59872E-14,-73.85)">
				<title>Start/End.7</title>
				<desc>…</desc>
				<v:custProps>
					<v:cp v:nameU="Cost" v:lbl="Coût" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="ProcessNumber" v:lbl="Numéro de processus" v:prompt="" v:type="2" v:format="" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Owner" v:lbl="Propriétaire" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Function" v:lbl="Fonction" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
					<v:cp v:nameU="StartDate" v:lbl="Date de début" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="EndDate" v:lbl="Date de fin" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Status" v:lbl="État" v:prompt="" v:type="4"
							v:format=";Non commencé;En cours;Terminé;Différé;En attente d’une contribution" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
				</v:custProps>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
					<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="visNavOrder" v:val="VT0(3500):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
				<v:textRect cx="35.4331" cy="314.922" width="70.87" height="28.3465"/>
				<path d="M14.17 329.1 L56.69 329.1 A14.1732 14.1732 -180 0 0 56.69 300.75 L14.17 300.75 A14.1732 14.1732 -180 1 0
							 14.17 329.1 Z" class="st10"/>
				<text x="32.64" y="317.32" class="st11" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>…</text>			</g>
			<g id="shape8-69" v:mID="8" v:groupContext="shape" v:layerMember="1" transform="translate(-1.59872E-14,-36.925)">
				<title>Start/End.8</title>
				<desc>Block Statement</desc>
				<v:custProps>
					<v:cp v:nameU="Cost" v:lbl="Coût" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="ProcessNumber" v:lbl="Numéro de processus" v:prompt="" v:type="2" v:format="" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Owner" v:lbl="Propriétaire" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Function" v:lbl="Fonction" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
					<v:cp v:nameU="StartDate" v:lbl="Date de début" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="EndDate" v:lbl="Date de fin" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Status" v:lbl="État" v:prompt="" v:type="4"
							v:format=";Non commencé;En cours;Terminé;Différé;En attente d’une contribution" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
				</v:custProps>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
					<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="visNavOrder" v:val="VT0(4500):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
				<v:textRect cx="35.4331" cy="314.922" width="70.87" height="28.3465"/>
				<path d="M14.17 329.1 L56.69 329.1 A14.1732 14.1732 -180 0 0 56.69 300.75 L14.17 300.75 A14.1732 14.1732 -180 1 0
							 14.17 329.1 Z" class="st10"/>
				<text x="7.67" y="317.32" class="st11" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Block Statement</text>			</g>
			<g id="shape9-72" v:mID="9" v:groupContext="shape" v:layerMember="1" transform="translate(94.7368,-73.85)">
				<title>Start/End.9</title>
				<desc>Statement 1</desc>
				<v:custProps>
					<v:cp v:nameU="Cost" v:lbl="Coût" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="ProcessNumber" v:lbl="Numéro de processus" v:prompt="" v:type="2" v:format="" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Owner" v:lbl="Propriétaire" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Function" v:lbl="Fonction" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
					<v:cp v:nameU="StartDate" v:lbl="Date de début" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="EndDate" v:lbl="Date de fin" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Status" v:lbl="État" v:prompt="" v:type="4"
							v:format=";Non commencé;En cours;Terminé;Différé;En attente d’une contribution" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
				</v:custProps>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
					<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="visNavOrder" v:val="VT0(5500):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
				<v:textRect cx="35.4331" cy="314.922" width="70.87" height="28.3465"/>
				<path d="M14.17 329.1 L56.69 329.1 A14.1732 14.1732 -180 0 0 56.69 300.75 L14.17 300.75 A14.1732 14.1732 -180 1 0
							 14.17 329.1 Z" class="st10"/>
				<text x="15.29" y="317.32" class="st11" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Statement 1</text>			</g>
			<g id="shape10-75" v:mID="10" v:groupContext="shape" v:layerMember="1" transform="translate(94.7368,-36.925)">
				<title>Start/End.10</title>
				<desc>Statement 2</desc>
				<v:custProps>
					<v:cp v:nameU="Cost" v:lbl="Coût" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="ProcessNumber" v:lbl="Numéro de processus" v:prompt="" v:type="2" v:format="" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Owner" v:lbl="Propriétaire" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Function" v:lbl="Fonction" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
					<v:cp v:nameU="StartDate" v:lbl="Date de début" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="EndDate" v:lbl="Date de fin" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Status" v:lbl="État" v:prompt="" v:type="4"
							v:format=";Non commencé;En cours;Terminé;Différé;En attente d’une contribution" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
				</v:custProps>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
					<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="visNavOrder" v:val="VT0(6500):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
				<v:textRect cx="35.4331" cy="314.922" width="70.87" height="28.3465"/>
				<path d="M14.17 329.1 L56.69 329.1 A14.1732 14.1732 -180 0 0 56.69 300.75 L14.17 300.75 A14.1732 14.1732 -180 1 0
							 14.17 329.1 Z" class="st10"/>
				<text x="14.68" y="317.32" class="st11" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Statement 2</text>			</g>
			<g id="shape11-78" v:mID="11" v:groupContext="shape" v:layerMember="1" transform="translate(94.7368,5.68434E-14)">
				<title>Start/End.11</title>
				<desc>Statement 3</desc>
				<v:custProps>
					<v:cp v:nameU="Cost" v:lbl="Coût" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="ProcessNumber" v:lbl="Numéro de processus" v:prompt="" v:type="2" v:format="" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Owner" v:lbl="Propriétaire" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Function" v:lbl="Fonction" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
					<v:cp v:nameU="StartDate" v:lbl="Date de début" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="EndDate" v:lbl="Date de fin" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Status" v:lbl="État" v:prompt="" v:type="4"
							v:format=";Non commencé;En cours;Terminé;Différé;En attente d’une contribution" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
				</v:custProps>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
					<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="visNavOrder" v:val="VT0(7500):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
				<v:textRect cx="35.4331" cy="314.922" width="70.87" height="28.3465"/>
				<path d="M14.17 329.1 L56.69 329.1 A14.1732 14.1732 -180 0 0 56.69 300.75 L14.17 300.75 A14.1732 14.1732 -180 1 0
							 14.17 329.1 Z" class="st10"/>
				<text x="14.68" y="317.32" class="st11" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Statement 3</text>			</g>
		</g>
		<g id="group31-81" transform="translate(441.72,-11.4394)" v:mID="31" v:groupContext="group">
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(22500):26"/>
			</v:userDefs>
			<title>Feuille.31</title>
			<g id="shape32-82" v:mID="32" v:groupContext="shape" v:layerMember="1" transform="translate(-1.59872E-14,-158.889)">
				<title>Start/End</title>
				<desc>Module</desc>
				<v:custProps>
					<v:cp v:nameU="Cost" v:lbl="Coût" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="ProcessNumber" v:lbl="Numéro de processus" v:prompt="" v:type="2" v:format="" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Owner" v:lbl="Propriétaire" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Function" v:lbl="Fonction" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
					<v:cp v:nameU="StartDate" v:lbl="Date de début" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="EndDate" v:lbl="Date de fin" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Status" v:lbl="État" v:prompt="" v:type="4"
							v:format=";Non commencé;En cours;Terminé;Différé;En attente d’une contribution" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
				</v:custProps>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
					<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="visNavOrder" v:val="VT0(500):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
				<v:textRect cx="35.4331" cy="314.922" width="70.87" height="28.3465"/>
				<path d="M14.17 329.1 L56.69 329.1 A14.1732 14.1732 -180 0 0 56.69 300.75 L14.17 300.75 A14.1732 14.1732 -180 1 0
							 14.17 329.1 Z" class="st12"/>
				<text x="22.23" y="317.32" class="st13" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Module</text>			</g>
			<g id="shape33-85" v:mID="33" v:groupContext="shape" v:layerMember="1" transform="translate(-1.59872E-14,-116.37)">
				<title>Start/End.5</title>
				<desc>ModuleBody</desc>
				<v:custProps>
					<v:cp v:nameU="Cost" v:lbl="Coût" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="ProcessNumber" v:lbl="Numéro de processus" v:prompt="" v:type="2" v:format="" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Owner" v:lbl="Propriétaire" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Function" v:lbl="Fonction" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
					<v:cp v:nameU="StartDate" v:lbl="Date de début" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="EndDate" v:lbl="Date de fin" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Status" v:lbl="État" v:prompt="" v:type="4"
							v:format=";Non commencé;En cours;Terminé;Différé;En attente d’une contribution" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
				</v:custProps>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
					<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="visNavOrder" v:val="VT0(1500):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
				<v:textRect cx="35.4331" cy="314.922" width="70.87" height="28.3465"/>
				<path d="M14.17 329.1 L56.69 329.1 A14.1732 14.1732 -180 0 0 56.69 300.75 L14.17 300.75 A14.1732 14.1732 -180 1 0
							 14.17 329.1 Z" class="st12"/>
				<text x="13.53" y="317.32" class="st13" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>ModuleBody</text>			</g>
			<g id="shape34-88" v:mID="34" v:groupContext="shape" v:layerMember="1" transform="translate(-1.59872E-14,-201.409)">
				<title>Start/End.6</title>
				<desc>StartRule</desc>
				<v:custProps>
					<v:cp v:nameU="Cost" v:lbl="Coût" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="ProcessNumber" v:lbl="Numéro de processus" v:prompt="" v:type="2" v:format="" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Owner" v:lbl="Propriétaire" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Function" v:lbl="Fonction" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
					<v:cp v:nameU="StartDate" v:lbl="Date de début" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="EndDate" v:lbl="Date de fin" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Status" v:lbl="État" v:prompt="" v:type="4"
							v:format=";Non commencé;En cours;Terminé;Différé;En attente d’une contribution" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
				</v:custProps>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
					<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="visNavOrder" v:val="VT0(2500):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
				<v:textRect cx="35.4331" cy="314.922" width="70.87" height="28.3465"/>
				<path d="M14.17 329.1 L56.69 329.1 A14.1732 14.1732 -180 0 0 56.69 300.75 L14.17 300.75 A14.1732 14.1732 -180 1 0
							 14.17 329.1 Z" class="st12"/>
				<text x="20.01" y="317.32" class="st13" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>StartRule</text>			</g>
			<g id="shape35-91" v:mID="35" v:groupContext="shape" v:layerMember="1" transform="translate(-1.59872E-14,-73.85)">
				<title>Start/End.7</title>
				<desc>…</desc>
				<v:custProps>
					<v:cp v:nameU="Cost" v:lbl="Coût" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="ProcessNumber" v:lbl="Numéro de processus" v:prompt="" v:type="2" v:format="" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Owner" v:lbl="Propriétaire" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Function" v:lbl="Fonction" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
					<v:cp v:nameU="StartDate" v:lbl="Date de début" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="EndDate" v:lbl="Date de fin" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Status" v:lbl="État" v:prompt="" v:type="4"
							v:format=";Non commencé;En cours;Terminé;Différé;En attente d’une contribution" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
				</v:custProps>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
					<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="visNavOrder" v:val="VT0(3500):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
				<v:textRect cx="35.4331" cy="314.922" width="70.87" height="28.3465"/>
				<path d="M14.17 329.1 L56.69 329.1 A14.1732 14.1732 -180 0 0 56.69 300.75 L14.17 300.75 A14.1732 14.1732 -180 1 0
							 14.17 329.1 Z" class="st12"/>
				<text x="32.64" y="317.32" class="st13" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>…</text>			</g>
			<g id="shape36-94" v:mID="36" v:groupContext="shape" v:layerMember="1" transform="translate(-1.59872E-14,-36.925)">
				<title>Start/End.8</title>
				<desc>Block Statement</desc>
				<v:custProps>
					<v:cp v:nameU="Cost" v:lbl="Coût" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="ProcessNumber" v:lbl="Numéro de processus" v:prompt="" v:type="2" v:format="" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Owner" v:lbl="Propriétaire" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Function" v:lbl="Fonction" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
					<v:cp v:nameU="StartDate" v:lbl="Date de début" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="EndDate" v:lbl="Date de fin" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Status" v:lbl="État" v:prompt="" v:type="4"
							v:format=";Non commencé;En cours;Terminé;Différé;En attente d’une contribution" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
				</v:custProps>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
					<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="visNavOrder" v:val="VT0(4500):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
				<v:textRect cx="35.4331" cy="314.922" width="70.87" height="28.3465"/>
				<path d="M14.17 329.1 L56.69 329.1 A14.1732 14.1732 -180 0 0 56.69 300.75 L14.17 300.75 A14.1732 14.1732 -180 1 0
							 14.17 329.1 Z" class="st12"/>
				<text x="7.67" y="317.32" class="st13" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Block Statement</text>			</g>
			<g id="shape37-97" v:mID="37" v:groupContext="shape" v:layerMember="1" transform="translate(94.7368,-73.85)">
				<title>Start/End.9</title>
				<desc>Statement 1</desc>
				<v:custProps>
					<v:cp v:nameU="Cost" v:lbl="Coût" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="ProcessNumber" v:lbl="Numéro de processus" v:prompt="" v:type="2" v:format="" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Owner" v:lbl="Propriétaire" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Function" v:lbl="Fonction" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
					<v:cp v:nameU="StartDate" v:lbl="Date de début" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="EndDate" v:lbl="Date de fin" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Status" v:lbl="État" v:prompt="" v:type="4"
							v:format=";Non commencé;En cours;Terminé;Différé;En attente d’une contribution" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
				</v:custProps>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
					<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="visNavOrder" v:val="VT0(5500):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
				<v:textRect cx="35.4331" cy="314.922" width="70.87" height="28.3465"/>
				<path d="M14.17 329.1 L56.69 329.1 A14.1732 14.1732 -180 0 0 56.69 300.75 L14.17 300.75 A14.1732 14.1732 -180 1 0
							 14.17 329.1 Z" class="st12"/>
				<text x="15.29" y="317.32" class="st13" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Statement 1</text>			</g>
			<g id="shape38-100" v:mID="38" v:groupContext="shape" v:layerMember="1" transform="translate(94.7368,-36.925)">
				<title>Start/End.10</title>
				<desc>Statement 2</desc>
				<v:custProps>
					<v:cp v:nameU="Cost" v:lbl="Coût" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="ProcessNumber" v:lbl="Numéro de processus" v:prompt="" v:type="2" v:format="" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Owner" v:lbl="Propriétaire" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Function" v:lbl="Fonction" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
					<v:cp v:nameU="StartDate" v:lbl="Date de début" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="EndDate" v:lbl="Date de fin" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Status" v:lbl="État" v:prompt="" v:type="4"
							v:format=";Non commencé;En cours;Terminé;Différé;En attente d’une contribution" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
				</v:custProps>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
					<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="visNavOrder" v:val="VT0(6500):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
				<v:textRect cx="35.4331" cy="314.922" width="70.87" height="28.3465"/>
				<path d="M14.17 329.1 L56.69 329.1 A14.1732 14.1732 -180 0 0 56.69 300.75 L14.17 300.75 A14.1732 14.1732 -180 1 0
							 14.17 329.1 Z" class="st12"/>
				<text x="14.68" y="317.32" class="st13" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Statement 2</text>			</g>
			<g id="shape39-103" v:mID="39" v:groupContext="shape" v:layerMember="1" transform="translate(94.7368,5.68434E-14)">
				<title>Start/End.11</title>
				<desc>Statement 3</desc>
				<v:custProps>
					<v:cp v:nameU="Cost" v:lbl="Coût" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="ProcessNumber" v:lbl="Numéro de processus" v:prompt="" v:type="2" v:format="" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Owner" v:lbl="Propriétaire" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Function" v:lbl="Fonction" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
					<v:cp v:nameU="StartDate" v:lbl="Date de début" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="EndDate" v:lbl="Date de fin" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
							v:ask="false" v:langID="1036" v:cal="0"/>
					<v:cp v:nameU="Status" v:lbl="État" v:prompt="" v:type="4"
							v:format=";Non commencé;En cours;Terminé;Différé;En attente d’une contribution" v:sortKey=""
							v:invis="false" v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
				</v:custProps>
				<v:userDefs>
					<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
					<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
					<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.39370078740157):24"/>
					<v:ud v:nameU="visNavOrder" v:val="VT0(7500):26"/>
				</v:userDefs>
				<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
				<v:textRect cx="35.4331" cy="314.922" width="70.87" height="28.3465"/>
				<path d="M14.17 329.1 L56.69 329.1 A14.1732 14.1732 -180 0 0 56.69 300.75 L14.17 300.75 A14.1732 14.1732 -180 1 0
							 14.17 329.1 Z" class="st12"/>
				<text x="14.68" y="317.32" class="st13" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Statement 3</text>			</g>
		</g>
		<g id="shape40-106" v:mID="40" v:groupContext="shape" v:layerMember="0" transform="translate(470.066,-212.848)">
			<title>Lien dynamique.40</title>
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(23500):26"/>
			</v:userDefs>
			<path d="M7.09 329.1 L7.09 337.23" class="st8"/>
		</g>
		<g id="shape41-111" v:mID="41" v:groupContext="shape" v:layerMember="0" transform="translate(470.066,-170.329)">
			<title>Lien dynamique.41</title>
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(24500):26"/>
			</v:userDefs>
			<path d="M7.09 329.1 L7.09 337.23" class="st8"/>
		</g>
		<g id="shape42-116" v:mID="42" v:groupContext="shape" v:layerMember="0" transform="translate(470.066,-127.809)">
			<title>Lien dynamique.42</title>
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(25500):26"/>
			</v:userDefs>
			<path d="M7.09 329.1 L7.09 337.23" class="st8"/>
		</g>
		<g id="shape43-121" v:mID="43" v:groupContext="shape" v:layerMember="0" transform="translate(470.066,-88.0867)">
			<title>Lien dynamique.43</title>
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(26500):26"/>
			</v:userDefs>
			<path d="M7.09 331.89 L7.09 334.43" class="st8"/>
		</g>
		<g id="shape45-126" v:mID="45" v:groupContext="shape" v:layerMember="0" transform="translate(512.586,-62.5376)">
			<title>Lien dynamique.45</title>
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(27500):26"/>
			</v:userDefs>
			<path d="M0 329.1 L10.63 329.1 L10.63 292.17 L17.83 292.17" class="st8"/>
		</g>
		<g id="shape46-131" v:mID="46" v:groupContext="shape" v:layerMember="0" transform="translate(512.586,-62.5376)">
			<title>Lien dynamique.46</title>
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(28500):26"/>
			</v:userDefs>
			<path d="M0 329.1 L10.26 329.1 L10.26 366.02 L17.83 366.02" class="st8"/>
		</g>
		<g id="shape47-136" v:mID="47" v:groupContext="shape" v:layerMember="0" transform="translate(512.586,-55.451)">
			<title>Lien dynamique.47</title>
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(29500):26"/>
			</v:userDefs>
			<path d="M0 322.01 L17.83 322.01" class="st8"/>
		</g>
		<g id="shape95-141" v:mID="95" v:groupContext="shape" transform="translate(12.1591,-252.25)">
			<title>Feuille.95</title>
			<desc>Script Input</desc>
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(33500):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
			<v:textRect cx="53.1496" cy="313.93" width="106.3" height="30.3307"/>
			<rect x="0" y="298.764" width="106.299" height="30.3307" class="st1"/>
			<text x="30.94" y="316.63" class="st2" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Script Input</text>		</g>
		<g id="shape96-144" v:mID="96" v:groupContext="shape" transform="translate(388.623,77.4568) rotate(90)">
			<title>Flèche Virage serré.96</title>
			<v:userDefs>
				<v:ud v:nameU="DblArrow" v:prompt="" v:val="VT0(0):5"/>
				<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
				<v:ud v:nameU="visNavOrder" v:val="VT0(34500):26"/>
			</v:userDefs>
			<path d="M0 329.1 L208.23 329.1 L208.23 258.23 L214.19 258.23 L200.02 244.06 L185.85 258.23 L191.82 258.23 L191.82 312.68
						 L0 312.68 L0 320.89 L0 329.1 Z" class="st14"/>
		</g>
		<g id="shape97-146" v:mID="97" v:groupContext="shape" v:layerMember="1" transform="translate(29.8757,-172.94)">
			<title>Sous-processus</title>
			<desc>Lexer</desc>
			<v:custProps>
				<v:cp v:nameU="Cost" v:lbl="Coût" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false" v:ask="false"
						v:langID="1036" v:cal="0"/>
				<v:cp v:nameU="ProcessNumber" v:lbl="Numéro de processus" v:prompt="" v:type="2" v:format="" v:sortKey=""
						v:invis="false" v:ask="false" v:langID="1036" v:cal="0"/>
				<v:cp v:nameU="Owner" v:lbl="Propriétaire" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1036" v:cal="0"/>
				<v:cp v:nameU="Function" v:lbl="Fonction" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
				<v:cp v:nameU="StartDate" v:lbl="Date de début" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1036" v:cal="0"/>
				<v:cp v:nameU="EndDate" v:lbl="Date de fin" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1036" v:cal="0"/>
				<v:cp v:nameU="Status" v:lbl="État" v:prompt="" v:type="4"
						v:format=";Non commencé;En cours;Terminé;Différé;En attente d’une contribution" v:sortKey=""
						v:invis="false" v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
			</v:custProps>
			<v:userDefs>
				<v:ud v:nameU="AntiScale" v:val="VT0(1):26"/>
				<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
				<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
				<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.59055118110236):24"/>
				<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.59055118110236):24"/>
				<v:ud v:nameU="visNavOrder" v:val="VT0(35500):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
			<v:textRect cx="35.4331" cy="307.835" width="53.86" height="42.5197"/>
			<path d="M0 329.1 L70.87 329.1 L70.87 286.58 L0 286.58 L0 329.1 Z" class="st15"/>
			<path d="M0 329.1 L70.87 329.1 L70.87 286.58 L0 286.58 L0 329.1" class="st16"/>
			<path d="M8.5 329.1 L8.5 286.58" class="st16"/>
			<path d="M62.36 329.1 L62.36 286.58" class="st16"/>
			<text x="26.14" y="310.24" class="st17" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Lexer</text>		</g>
		<g id="shape98-152" v:mID="98" v:groupContext="shape" v:layerMember="1" transform="translate(29.8757,-102.073)">
			<title>Sous-processus.98</title>
			<desc>Parser</desc>
			<v:custProps>
				<v:cp v:nameU="Cost" v:lbl="Coût" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false" v:ask="false"
						v:langID="1036" v:cal="0"/>
				<v:cp v:nameU="ProcessNumber" v:lbl="Numéro de processus" v:prompt="" v:type="2" v:format="" v:sortKey=""
						v:invis="false" v:ask="false" v:langID="1036" v:cal="0"/>
				<v:cp v:nameU="Owner" v:lbl="Propriétaire" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1036" v:cal="0"/>
				<v:cp v:nameU="Function" v:lbl="Fonction" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
				<v:cp v:nameU="StartDate" v:lbl="Date de début" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1036" v:cal="0"/>
				<v:cp v:nameU="EndDate" v:lbl="Date de fin" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1036" v:cal="0"/>
				<v:cp v:nameU="Status" v:lbl="État" v:prompt="" v:type="4"
						v:format=";Non commencé;En cours;Terminé;Différé;En attente d’une contribution" v:sortKey=""
						v:invis="false" v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
			</v:custProps>
			<v:userDefs>
				<v:ud v:nameU="AntiScale" v:val="VT0(1):26"/>
				<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
				<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
				<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.59055118110236):24"/>
				<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.59055118110236):24"/>
				<v:ud v:nameU="visNavOrder" v:val="VT0(36500):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
			<v:textRect cx="35.4331" cy="307.835" width="53.86" height="42.5197"/>
			<path d="M0 329.1 L70.87 329.1 L70.87 286.58 L0 286.58 L0 329.1 Z" class="st15"/>
			<path d="M0 329.1 L70.87 329.1 L70.87 286.58 L0 286.58 L0 329.1" class="st16"/>
			<path d="M8.5 329.1 L8.5 286.58" class="st16"/>
			<path d="M62.36 329.1 L62.36 286.58" class="st16"/>
			<text x="24.59" y="310.24" class="st17" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Parser</text>		</g>
		<g id="shape99-158" v:mID="99" v:groupContext="shape" v:layerMember="1" transform="translate(180,-286.325)">
			<title>Sous-processus.99</title>
			<desc>Visitor</desc>
			<v:custProps>
				<v:cp v:nameU="Cost" v:lbl="Coût" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false" v:ask="false"
						v:langID="1036" v:cal="0"/>
				<v:cp v:nameU="ProcessNumber" v:lbl="Numéro de processus" v:prompt="" v:type="2" v:format="" v:sortKey=""
						v:invis="false" v:ask="false" v:langID="1036" v:cal="0"/>
				<v:cp v:nameU="Owner" v:lbl="Propriétaire" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1036" v:cal="0"/>
				<v:cp v:nameU="Function" v:lbl="Fonction" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
				<v:cp v:nameU="StartDate" v:lbl="Date de début" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1036" v:cal="0"/>
				<v:cp v:nameU="EndDate" v:lbl="Date de fin" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1036" v:cal="0"/>
				<v:cp v:nameU="Status" v:lbl="État" v:prompt="" v:type="4"
						v:format=";Non commencé;En cours;Terminé;Différé;En attente d’une contribution" v:sortKey=""
						v:invis="false" v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
			</v:custProps>
			<v:userDefs>
				<v:ud v:nameU="AntiScale" v:val="VT0(1):26"/>
				<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
				<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
				<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.59055118110236):24"/>
				<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.59055118110236):24"/>
				<v:ud v:nameU="visNavOrder" v:val="VT0(37500):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
			<v:textRect cx="35.4331" cy="307.835" width="53.86" height="42.5197"/>
			<path d="M0 329.1 L70.87 329.1 L70.87 286.58 L0 286.58 L0 329.1 Z" class="st15"/>
			<path d="M0 329.1 L70.87 329.1 L70.87 286.58 L0 286.58 L0 329.1" class="st16"/>
			<path d="M8.5 329.1 L8.5 286.58" class="st16"/>
			<path d="M62.36 329.1 L62.36 286.58" class="st16"/>
			<text x="24.22" y="310.24" class="st17" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Visitor</text>		</g>
		<g id="shape101-164" v:mID="101" v:groupContext="shape" v:layerMember="1" transform="translate(447.053,-286.325)">
			<title>Sous-processus.101</title>
			<desc>Partial Evaluators / Prettifier</desc>
			<v:custProps>
				<v:cp v:nameU="Cost" v:lbl="Coût" v:prompt="" v:type="7" v:format="@" v:sortKey="" v:invis="false" v:ask="false"
						v:langID="1036" v:cal="0"/>
				<v:cp v:nameU="ProcessNumber" v:lbl="Numéro de processus" v:prompt="" v:type="2" v:format="" v:sortKey=""
						v:invis="false" v:ask="false" v:langID="1036" v:cal="0"/>
				<v:cp v:nameU="Owner" v:lbl="Propriétaire" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1036" v:cal="0"/>
				<v:cp v:nameU="Function" v:lbl="Fonction" v:prompt="" v:type="0" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
				<v:cp v:nameU="StartDate" v:lbl="Date de début" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1036" v:cal="0"/>
				<v:cp v:nameU="EndDate" v:lbl="Date de fin" v:prompt="" v:type="5" v:format="" v:sortKey="" v:invis="false"
						v:ask="false" v:langID="1036" v:cal="0"/>
				<v:cp v:nameU="Status" v:lbl="État" v:prompt="" v:type="4"
						v:format=";Non commencé;En cours;Terminé;Différé;En attente d’une contribution" v:sortKey=""
						v:invis="false" v:ask="false" v:langID="1036" v:cal="0" v:val="VT4()"/>
			</v:custProps>
			<v:userDefs>
				<v:ud v:nameU="AntiScale" v:val="VT0(1):26"/>
				<v:ud v:nameU="visVersion" v:prompt="" v:val="VT0(15):26"/>
				<v:ud v:nameU="DefaultWidth" v:prompt="" v:val="VT0(0.98425196850394):24"/>
				<v:ud v:nameU="DefaultHeight" v:prompt="" v:val="VT0(0.59055118110236):24"/>
				<v:ud v:nameU="ResizeTxtHeight" v:prompt="" v:val="VT0(0.59055118110236):24"/>
				<v:ud v:nameU="visNavOrder" v:val="VT0(38500):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
			<v:textRect cx="46.3614" cy="307.835" width="75.72" height="42.5197"/>
			<path d="M0 329.1 L92.72 329.1 L92.72 286.58 L0 286.58 L0 329.1 Z" class="st15"/>
			<path d="M0 329.1 L92.72 329.1 L92.72 286.58 L0 286.58 L0 329.1" class="st16"/>
			<path d="M8.5 329.1 L8.5 286.58" class="st16"/>
			<path d="M84.22 329.1 L84.22 286.58" class="st16"/>
			<text x="13.59" y="305.44" class="st17" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Partial Evaluators / <tspan
						x="31.26" dy="1.2em" class="st18">Prettifier</tspan></text>		</g>
		<g id="shape102-171" v:mID="102" v:groupContext="shape" transform="translate(54.2851,-45.3805)">
			<title>Feuille.102</title>
			<desc>Create the AST</desc>
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(39500):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
			<v:textRect cx="50" cy="323.095" width="100" height="12"/>
			<rect x="0" y="317.095" width="100" height="12" class="st19"/>
			<text x="23.86" y="325.49" class="st17" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Create the AST</text>		</g>
		<g id="shape104-174" v:mID="104" v:groupContext="shape" transform="translate(316.126,-39.3805)">
			<title>Feuille.104</title>
			<desc>Create each node evaluators</desc>
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(40500):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
			<v:textRect cx="57.0717" cy="323.095" width="114.15" height="12"/>
			<rect x="0" y="317.095" width="114.143" height="12" class="st19"/>
			<text x="6.95" y="325.49" class="st17" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Create each node evaluators</text>		</g>
		<g id="shape105-177" v:mID="105" v:groupContext="shape" transform="translate(590.278,-40.3727)">
			<title>Feuille.105</title>
			<desc>Evaluate each nodes</desc>
			<v:userDefs>
				<v:ud v:nameU="visNavOrder" v:val="VT0(41500):26"/>
			</v:userDefs>
			<v:textBlock v:margins="rect(2,2,2,2)" v:tabSpace="42.5197"/>
			<v:textRect cx="57.0717" cy="323.095" width="114.15" height="12"/>
			<rect x="0" y="317.095" width="114.143" height="12" class="st19"/>
			<text x="21.2" y="325.49" class="st17" v:langID="1033"><v:paragraph v:horizAlign="1"/><v:tabList/>Evaluate each nodes</text>		</g>
	</g>
</svg>
