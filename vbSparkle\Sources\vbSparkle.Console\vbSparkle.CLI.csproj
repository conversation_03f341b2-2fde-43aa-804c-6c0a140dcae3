﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <StartupObject>vbSparkle.CLI.Program</StartupObject>
    <GeneratePackageOnBuild>False</GeneratePackageOnBuild>
    <Authors><PERSON><PERSON><PERSON><PERSON>, Airbus CERT</Authors>
    <Company>Airbus CERT</Company>
    <Product>vbSparkle CLI</Product>
    <Copyright>Airbus CERT</Copyright>
    <PackageProjectUrl>https://github.com/airbus-cert/vbSparkle</PackageProjectUrl>
    <PackageReadmeFile>README.md</PackageReadmeFile>
    <RepositoryUrl>https://github.com/airbus-cert/vbSparkle</RepositoryUrl>
    <FileVersion></FileVersion>
    <Version>1.0.3</Version>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="demo2.txt" />
    <Compile Include="demo.txt" />
  </ItemGroup>

  <ItemGroup>
    <None Include="..\..\README.md">
      <Pack>True</Pack>
      <PackagePath>\</PackagePath>
    </None>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Colorful.Console" Version="1.2.15" />
    <PackageReference Include="CommandLineParser" Version="2.9.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\vbSparkle\vbSparkle.csproj" />
  </ItemGroup>

</Project>
