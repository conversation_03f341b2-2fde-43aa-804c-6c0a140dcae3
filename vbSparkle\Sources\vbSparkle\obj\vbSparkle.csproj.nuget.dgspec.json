{"format": 1, "restore": {"C:\\Projects\\Tools\\VbDeobf\\Sources\\vbSparkle\\vbSparkle.csproj": {}}, "projects": {"C:\\Projects\\Tools\\VbDeobf\\Sources\\vbSparkle\\vbSparkle.csproj": {"version": "1.2.1.1", "restore": {"projectUniqueName": "C:\\Projects\\Tools\\VbDeobf\\Sources\\vbSparkle\\vbSparkle.csproj", "projectName": "vbSparkle", "projectPath": "C:\\Projects\\Tools\\VbDeobf\\Sources\\vbSparkle\\vbSparkle.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\Tools\\VbDeobf\\Sources\\vbSparkle\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0", "net8.0", "netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}, "net8.0": {"targetAlias": "net8.0", "projectReferences": {}}, "netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Antlr4.Runtime.Standard": {"target": "Package", "version": "[4.13.1, )"}, "MathNet.Symbolics": {"target": "Package", "version": "[0.25.0, )"}, "System.Text.Encoding.CodePages": {"target": "Package", "version": "[8.0.0, )"}, "vbeDecoder": {"target": "Package", "version": "[1.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}, "net8.0": {"targetAlias": "net8.0", "dependencies": {"Antlr4.Runtime.Standard": {"target": "Package", "version": "[4.13.1, )"}, "MathNet.Symbolics": {"target": "Package", "version": "[0.25.0, )"}, "System.Text.Encoding.CodePages": {"target": "Package", "version": "[8.0.0, )"}, "vbeDecoder": {"target": "Package", "version": "[1.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}, "netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"Antlr4.Runtime.Standard": {"target": "Package", "version": "[4.13.1, )"}, "MathNet.Symbolics": {"target": "Package", "version": "[0.25.0, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "System.Text.Encoding.CodePages": {"target": "Package", "version": "[8.0.0, )"}, "vbeDecoder": {"target": "Package", "version": "[1.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}