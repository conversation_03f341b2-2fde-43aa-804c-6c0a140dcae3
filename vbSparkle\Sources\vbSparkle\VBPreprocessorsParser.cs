//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     ANTLR Version: 4.13.1
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// Generated from VBPreprocessors.g4 by ANTLR 4.13.1

// Unreachable code detected
#pragma warning disable 0162
// The variable '...' is assigned but its value is never used
#pragma warning disable 0219
// Missing XML comment for publicly visible type or member '...'
#pragma warning disable 1591
// Ambiguous reference in cref attribute
#pragma warning disable 419

using System;
using System.IO;
using System.Text;
using System.Diagnostics;
using System.Collections.Generic;
using Antlr4.Runtime;
using Antlr4.Runtime.Atn;
using Antlr4.Runtime.Misc;
using Antlr4.Runtime.Tree;
using DFA = Antlr4.Runtime.Dfa.DFA;

[System.CodeDom.Compiler.GeneratedCode("ANTLR", "4.13.1")]
[System.CLSCompliant(false)]
public partial class VBPreprocessorsParser : Parser {
	protected static DFA[] decisionToDFA;
	protected static PredictionContextCache sharedContextCache = new PredictionContextCache();
	public const int
		MACRO_CONST=1, AND=2, ATTRIBUTE=3, BEGIN=4, BEGINPROPERTY=5, CLASS=6, 
		CONST=7, ELSE=8, ELSEIF=9, END_IF=10, END=11, ENDPROPERTY=12, EQV=13, 
		FALSE=14, IF=15, IMP=16, IS=17, LIKE=18, MACRO_IF=19, MACRO_ELSEIF=20, 
		MACRO_ELSE=21, MACRO_END_IF=22, MOD=23, NOT=24, NOTHING=25, NULL=26, OBJECT=27, 
		OR=28, THEN=29, TRUE=30, VERSION=31, XOR=32, AMPERSAND=33, AT=34, COMMA=35, 
		DIV=36, DOT=37, EQ=38, EXCLAMATIONMARK=39, GEQ=40, GT=41, HASH=42, LEQ=43, 
		LBRACE=44, LPAREN=45, LT=46, MINUS=47, MULT=48, NEQ=49, PLUS=50, POW=51, 
		RBRACE=52, RPAREN=53, SEMICOLON=54, L_SQUARE_BRACKET=55, R_SQUARE_BRACKET=56, 
		STRINGLITERAL=57, DATELITERAL=58, HEXLITERAL=59, INTEGERLITERAL=60, DOUBLELITERAL=61, 
		FILENUMBER=62, OCTALLITERAL=63, GUID=64, NEWLINE=65, IDENTIFIER=66, COMMENT=67, 
		LINE_CONTINUATION=68, WS=69, ANYCHAR=70, REM=71, LABEL_L=72;
	public const int
		RULE_startRule = 0, RULE_macroConst = 1, RULE_codeBlock = 2, RULE_codeBlockBody = 3, 
		RULE_nonMacroCodeBlockX = 4, RULE_moduleInfo = 5, RULE_moduleReferences = 6, 
		RULE_moduleReference = 7, RULE_moduleReferenceValue = 8, RULE_moduleReferenceComponent = 9, 
		RULE_moduleHeader = 10, RULE_moduleConfig = 11, RULE_moduleConfigElement = 12, 
		RULE_ambiguousIdentifier = 13, RULE_moduleAttributes = 14, RULE_macroIfThenElseStmt = 15, 
		RULE_macroIfBlockCondStmt = 16, RULE_macroIfBlockStmt = 17, RULE_macroElseIfBlockStmt = 18, 
		RULE_macroElseBlockStmt = 19, RULE_commentBlock = 20, RULE_lineLabel = 21, 
		RULE_nonMacroCodeBlockLine = 22, RULE_nonMacroCodeStmt = 23, RULE_controlProperties = 24, 
		RULE_cp_Properties = 25, RULE_cp_NestedProperty = 26, RULE_cp_ControlType = 27, 
		RULE_complexType = 28, RULE_cp_ControlIdentifier = 29, RULE_attributeStmt = 30, 
		RULE_ifConditionStmt = 31, RULE_valueStmt = 32, RULE_delimitedLiteral = 33, 
		RULE_literal = 34, RULE_anytoken = 35;
	public static readonly string[] ruleNames = {
		"startRule", "macroConst", "codeBlock", "codeBlockBody", "nonMacroCodeBlockX", 
		"moduleInfo", "moduleReferences", "moduleReference", "moduleReferenceValue", 
		"moduleReferenceComponent", "moduleHeader", "moduleConfig", "moduleConfigElement", 
		"ambiguousIdentifier", "moduleAttributes", "macroIfThenElseStmt", "macroIfBlockCondStmt", 
		"macroIfBlockStmt", "macroElseIfBlockStmt", "macroElseBlockStmt", "commentBlock", 
		"lineLabel", "nonMacroCodeBlockLine", "nonMacroCodeStmt", "controlProperties", 
		"cp_Properties", "cp_NestedProperty", "cp_ControlType", "complexType", 
		"cp_ControlIdentifier", "attributeStmt", "ifConditionStmt", "valueStmt", 
		"delimitedLiteral", "literal", "anytoken"
	};

	private static readonly string[] _LiteralNames = {
		null, null, null, null, null, null, null, null, null, null, null, null, 
		null, null, null, null, null, null, null, null, null, null, null, null, 
		null, null, null, null, null, null, null, null, null, "'&'", "'@'", "','", 
		null, "'.'", "'='", "'!'", "'>='", "'>'", "'#'", "'<='", "'{'", "'('", 
		"'<'", "'-'", "'*'", "'<>'", "'+'", "'^'", "'}'", "')'", "';'", "'['", 
		"']'"
	};
	private static readonly string[] _SymbolicNames = {
		null, "MACRO_CONST", "AND", "ATTRIBUTE", "BEGIN", "BEGINPROPERTY", "CLASS", 
		"CONST", "ELSE", "ELSEIF", "END_IF", "END", "ENDPROPERTY", "EQV", "FALSE", 
		"IF", "IMP", "IS", "LIKE", "MACRO_IF", "MACRO_ELSEIF", "MACRO_ELSE", "MACRO_END_IF", 
		"MOD", "NOT", "NOTHING", "NULL", "OBJECT", "OR", "THEN", "TRUE", "VERSION", 
		"XOR", "AMPERSAND", "AT", "COMMA", "DIV", "DOT", "EQ", "EXCLAMATIONMARK", 
		"GEQ", "GT", "HASH", "LEQ", "LBRACE", "LPAREN", "LT", "MINUS", "MULT", 
		"NEQ", "PLUS", "POW", "RBRACE", "RPAREN", "SEMICOLON", "L_SQUARE_BRACKET", 
		"R_SQUARE_BRACKET", "STRINGLITERAL", "DATELITERAL", "HEXLITERAL", "INTEGERLITERAL", 
		"DOUBLELITERAL", "FILENUMBER", "OCTALLITERAL", "GUID", "NEWLINE", "IDENTIFIER", 
		"COMMENT", "LINE_CONTINUATION", "WS", "ANYCHAR", "REM", "LABEL_L"
	};
	public static readonly IVocabulary DefaultVocabulary = new Vocabulary(_LiteralNames, _SymbolicNames);

	[NotNull]
	public override IVocabulary Vocabulary
	{
		get
		{
			return DefaultVocabulary;
		}
	}

	public override string GrammarFileName { get { return "VBPreprocessors.g4"; } }

	public override string[] RuleNames { get { return ruleNames; } }

	public override int[] SerializedAtn { get { return _serializedATN; } }

	static VBPreprocessorsParser() {
		decisionToDFA = new DFA[_ATN.NumberOfDecisions];
		for (int i = 0; i < _ATN.NumberOfDecisions; i++) {
			decisionToDFA[i] = new DFA(_ATN.GetDecisionState(i), i);
		}
	}

		public VBPreprocessorsParser(ITokenStream input) : this(input, Console.Out, Console.Error) { }

		public VBPreprocessorsParser(ITokenStream input, TextWriter output, TextWriter errorOutput)
		: base(input, output, errorOutput)
	{
		Interpreter = new ParserATNSimulator(this, _ATN, decisionToDFA, sharedContextCache);
	}

	public partial class StartRuleContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] NEWLINE() { return GetTokens(VBPreprocessorsParser.NEWLINE); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode NEWLINE(int i) {
			return GetToken(VBPreprocessorsParser.NEWLINE, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public CodeBlockContext[] codeBlock() {
			return GetRuleContexts<CodeBlockContext>();
		}
		[System.Diagnostics.DebuggerNonUserCode] public CodeBlockContext codeBlock(int i) {
			return GetRuleContext<CodeBlockContext>(i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode Eof() { return GetToken(VBPreprocessorsParser.Eof, 0); }
		public StartRuleContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_startRule; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterStartRule(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitStartRule(this);
		}
	}

	[RuleVersion(0)]
	public StartRuleContext startRule() {
		StartRuleContext _localctx = new StartRuleContext(Context, State);
		EnterRule(_localctx, 0, RULE_startRule);
		int _la;
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 75;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			while (_la==NEWLINE) {
				{
				{
				State = 72;
				Match(NEWLINE);
				}
				}
				State = 77;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			}
			State = 81;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			while ((((_la) & ~0x3f) == 0 && ((1L << _la) & -7340034L) != 0) || ((((_la - 64)) & ~0x3f) == 0 && ((1L << (_la - 64)) & 509L) != 0)) {
				{
				{
				State = 78;
				codeBlock();
				}
				}
				State = 83;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			}
			State = 85;
			ErrorHandler.Sync(this);
			switch ( Interpreter.AdaptivePredict(TokenStream,2,Context) ) {
			case 1:
				{
				State = 84;
				Match(Eof);
				}
				break;
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class MacroConstContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode MACRO_CONST() { return GetToken(VBPreprocessorsParser.MACRO_CONST, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode IDENTIFIER() { return GetToken(VBPreprocessorsParser.IDENTIFIER, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode EQ() { return GetToken(VBPreprocessorsParser.EQ, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ValueStmtContext valueStmt() {
			return GetRuleContext<ValueStmtContext>(0);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] WS() { return GetTokens(VBPreprocessorsParser.WS); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode WS(int i) {
			return GetToken(VBPreprocessorsParser.WS, i);
		}
		public MacroConstContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_macroConst; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterMacroConst(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitMacroConst(this);
		}
	}

	[RuleVersion(0)]
	public MacroConstContext macroConst() {
		MacroConstContext _localctx = new MacroConstContext(Context, State);
		EnterRule(_localctx, 2, RULE_macroConst);
		int _la;
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 87;
			Match(MACRO_CONST);
			State = 88;
			Match(IDENTIFIER);
			State = 90;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==WS) {
				{
				State = 89;
				Match(WS);
				}
			}

			State = 92;
			Match(EQ);
			State = 94;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==WS) {
				{
				State = 93;
				Match(WS);
				}
			}

			State = 96;
			valueStmt(0);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class CodeBlockContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public CodeBlockBodyContext codeBlockBody() {
			return GetRuleContext<CodeBlockBodyContext>(0);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode Eof() { return GetToken(VBPreprocessorsParser.Eof, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public LineLabelContext[] lineLabel() {
			return GetRuleContexts<LineLabelContext>();
		}
		[System.Diagnostics.DebuggerNonUserCode] public LineLabelContext lineLabel(int i) {
			return GetRuleContext<LineLabelContext>(i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public CommentBlockContext commentBlock() {
			return GetRuleContext<CommentBlockContext>(0);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] NEWLINE() { return GetTokens(VBPreprocessorsParser.NEWLINE); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode NEWLINE(int i) {
			return GetToken(VBPreprocessorsParser.NEWLINE, i);
		}
		public CodeBlockContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_codeBlock; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterCodeBlock(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitCodeBlock(this);
		}
	}

	[RuleVersion(0)]
	public CodeBlockContext codeBlock() {
		CodeBlockContext _localctx = new CodeBlockContext(Context, State);
		EnterRule(_localctx, 4, RULE_codeBlock);
		int _la;
		try {
			int _alt;
			EnterOuterAlt(_localctx, 1);
			{
			State = 101;
			ErrorHandler.Sync(this);
			_alt = Interpreter.AdaptivePredict(TokenStream,5,Context);
			while ( _alt!=2 && _alt!=global::Antlr4.Runtime.Atn.ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					{
					{
					State = 98;
					lineLabel();
					}
					} 
				}
				State = 103;
				ErrorHandler.Sync(this);
				_alt = Interpreter.AdaptivePredict(TokenStream,5,Context);
			}
			State = 104;
			codeBlockBody();
			State = 106;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==COMMENT) {
				{
				State = 105;
				commentBlock();
				}
			}

			State = 114;
			ErrorHandler.Sync(this);
			switch (TokenStream.LA(1)) {
			case NEWLINE:
				{
				State = 109;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
				do {
					{
					{
					State = 108;
					Match(NEWLINE);
					}
					}
					State = 111;
					ErrorHandler.Sync(this);
					_la = TokenStream.LA(1);
				} while ( _la==NEWLINE );
				}
				break;
			case Eof:
				{
				State = 113;
				Match(Eof);
				}
				break;
			default:
				throw new NoViableAltException(this);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class CodeBlockBodyContext : ParserRuleContext {
		public CodeBlockBodyContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_codeBlockBody; } }
	 
		public CodeBlockBodyContext() { }
		public virtual void CopyFrom(CodeBlockBodyContext context) {
			base.CopyFrom(context);
		}
	}
	public partial class VcommentBlockContext : CodeBlockBodyContext {
		[System.Diagnostics.DebuggerNonUserCode] public CommentBlockContext commentBlock() {
			return GetRuleContext<CommentBlockContext>(0);
		}
		public VcommentBlockContext(CodeBlockBodyContext context) { CopyFrom(context); }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterVcommentBlock(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitVcommentBlock(this);
		}
	}
	public partial class VmacroIfContext : CodeBlockBodyContext {
		[System.Diagnostics.DebuggerNonUserCode] public MacroIfThenElseStmtContext macroIfThenElseStmt() {
			return GetRuleContext<MacroIfThenElseStmtContext>(0);
		}
		public VmacroIfContext(CodeBlockBodyContext context) { CopyFrom(context); }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterVmacroIf(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitVmacroIf(this);
		}
	}
	public partial class VcodeBlockContext : CodeBlockBodyContext {
		[System.Diagnostics.DebuggerNonUserCode] public NonMacroCodeBlockXContext nonMacroCodeBlockX() {
			return GetRuleContext<NonMacroCodeBlockXContext>(0);
		}
		public VcodeBlockContext(CodeBlockBodyContext context) { CopyFrom(context); }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterVcodeBlock(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitVcodeBlock(this);
		}
	}
	public partial class VmacroConstContext : CodeBlockBodyContext {
		[System.Diagnostics.DebuggerNonUserCode] public MacroConstContext macroConst() {
			return GetRuleContext<MacroConstContext>(0);
		}
		public VmacroConstContext(CodeBlockBodyContext context) { CopyFrom(context); }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterVmacroConst(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitVmacroConst(this);
		}
	}
	public partial class VlineLabelContext : CodeBlockBodyContext {
		[System.Diagnostics.DebuggerNonUserCode] public LineLabelContext lineLabel() {
			return GetRuleContext<LineLabelContext>(0);
		}
		public VlineLabelContext(CodeBlockBodyContext context) { CopyFrom(context); }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterVlineLabel(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitVlineLabel(this);
		}
	}

	[RuleVersion(0)]
	public CodeBlockBodyContext codeBlockBody() {
		CodeBlockBodyContext _localctx = new CodeBlockBodyContext(Context, State);
		EnterRule(_localctx, 6, RULE_codeBlockBody);
		try {
			State = 121;
			ErrorHandler.Sync(this);
			switch ( Interpreter.AdaptivePredict(TokenStream,9,Context) ) {
			case 1:
				_localctx = new VmacroIfContext(_localctx);
				EnterOuterAlt(_localctx, 1);
				{
				State = 116;
				macroIfThenElseStmt();
				}
				break;
			case 2:
				_localctx = new VmacroConstContext(_localctx);
				EnterOuterAlt(_localctx, 2);
				{
				State = 117;
				macroConst();
				}
				break;
			case 3:
				_localctx = new VcommentBlockContext(_localctx);
				EnterOuterAlt(_localctx, 3);
				{
				State = 118;
				commentBlock();
				}
				break;
			case 4:
				_localctx = new VcodeBlockContext(_localctx);
				EnterOuterAlt(_localctx, 4);
				{
				State = 119;
				nonMacroCodeBlockX();
				}
				break;
			case 5:
				_localctx = new VlineLabelContext(_localctx);
				EnterOuterAlt(_localctx, 5);
				{
				State = 120;
				lineLabel();
				}
				break;
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class NonMacroCodeBlockXContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public NonMacroCodeBlockLineContext[] nonMacroCodeBlockLine() {
			return GetRuleContexts<NonMacroCodeBlockLineContext>();
		}
		[System.Diagnostics.DebuggerNonUserCode] public NonMacroCodeBlockLineContext nonMacroCodeBlockLine(int i) {
			return GetRuleContext<NonMacroCodeBlockLineContext>(i);
		}
		public NonMacroCodeBlockXContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_nonMacroCodeBlockX; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterNonMacroCodeBlockX(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitNonMacroCodeBlockX(this);
		}
	}

	[RuleVersion(0)]
	public NonMacroCodeBlockXContext nonMacroCodeBlockX() {
		NonMacroCodeBlockXContext _localctx = new NonMacroCodeBlockXContext(Context, State);
		EnterRule(_localctx, 8, RULE_nonMacroCodeBlockX);
		int _la;
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 124;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			do {
				{
				{
				State = 123;
				nonMacroCodeBlockLine();
				}
				}
				State = 126;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			} while ( (((_la) & ~0x3f) == 0 && ((1L << _la) & -7864324L) != 0) || ((((_la - 64)) & ~0x3f) == 0 && ((1L << (_la - 64)) & 501L) != 0) );
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class ModuleInfoContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ModuleReferencesContext moduleReferences() {
			return GetRuleContext<ModuleReferencesContext>(0);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] NEWLINE() { return GetTokens(VBPreprocessorsParser.NEWLINE); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode NEWLINE(int i) {
			return GetToken(VBPreprocessorsParser.NEWLINE, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ControlPropertiesContext controlProperties() {
			return GetRuleContext<ControlPropertiesContext>(0);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ModuleConfigContext moduleConfig() {
			return GetRuleContext<ModuleConfigContext>(0);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ModuleAttributesContext moduleAttributes() {
			return GetRuleContext<ModuleAttributesContext>(0);
		}
		public ModuleInfoContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_moduleInfo; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterModuleInfo(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitModuleInfo(this);
		}
	}

	[RuleVersion(0)]
	public ModuleInfoContext moduleInfo() {
		ModuleInfoContext _localctx = new ModuleInfoContext(Context, State);
		EnterRule(_localctx, 10, RULE_moduleInfo);
		int _la;
		try {
			int _alt;
			EnterOuterAlt(_localctx, 1);
			{
			State = 129;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==OBJECT) {
				{
				State = 128;
				moduleReferences();
				}
			}

			State = 134;
			ErrorHandler.Sync(this);
			_alt = Interpreter.AdaptivePredict(TokenStream,12,Context);
			while ( _alt!=2 && _alt!=global::Antlr4.Runtime.Atn.ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					{
					{
					State = 131;
					Match(NEWLINE);
					}
					} 
				}
				State = 136;
				ErrorHandler.Sync(this);
				_alt = Interpreter.AdaptivePredict(TokenStream,12,Context);
			}
			State = 138;
			ErrorHandler.Sync(this);
			switch ( Interpreter.AdaptivePredict(TokenStream,13,Context) ) {
			case 1:
				{
				State = 137;
				controlProperties();
				}
				break;
			}
			State = 143;
			ErrorHandler.Sync(this);
			_alt = Interpreter.AdaptivePredict(TokenStream,14,Context);
			while ( _alt!=2 && _alt!=global::Antlr4.Runtime.Atn.ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					{
					{
					State = 140;
					Match(NEWLINE);
					}
					} 
				}
				State = 145;
				ErrorHandler.Sync(this);
				_alt = Interpreter.AdaptivePredict(TokenStream,14,Context);
			}
			State = 147;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==BEGIN) {
				{
				State = 146;
				moduleConfig();
				}
			}

			State = 152;
			ErrorHandler.Sync(this);
			_alt = Interpreter.AdaptivePredict(TokenStream,16,Context);
			while ( _alt!=2 && _alt!=global::Antlr4.Runtime.Atn.ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					{
					{
					State = 149;
					Match(NEWLINE);
					}
					} 
				}
				State = 154;
				ErrorHandler.Sync(this);
				_alt = Interpreter.AdaptivePredict(TokenStream,16,Context);
			}
			State = 156;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==ATTRIBUTE) {
				{
				State = 155;
				moduleAttributes();
				}
			}

			State = 161;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			while (_la==NEWLINE) {
				{
				{
				State = 158;
				Match(NEWLINE);
				}
				}
				State = 163;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class ModuleReferencesContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ModuleReferenceContext[] moduleReference() {
			return GetRuleContexts<ModuleReferenceContext>();
		}
		[System.Diagnostics.DebuggerNonUserCode] public ModuleReferenceContext moduleReference(int i) {
			return GetRuleContext<ModuleReferenceContext>(i);
		}
		public ModuleReferencesContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_moduleReferences; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterModuleReferences(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitModuleReferences(this);
		}
	}

	[RuleVersion(0)]
	public ModuleReferencesContext moduleReferences() {
		ModuleReferencesContext _localctx = new ModuleReferencesContext(Context, State);
		EnterRule(_localctx, 12, RULE_moduleReferences);
		int _la;
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 165;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			do {
				{
				{
				State = 164;
				moduleReference();
				}
				}
				State = 167;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			} while ( _la==OBJECT );
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class ModuleReferenceContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode OBJECT() { return GetToken(VBPreprocessorsParser.OBJECT, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode EQ() { return GetToken(VBPreprocessorsParser.EQ, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ModuleReferenceValueContext moduleReferenceValue() {
			return GetRuleContext<ModuleReferenceValueContext>(0);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] WS() { return GetTokens(VBPreprocessorsParser.WS); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode WS(int i) {
			return GetToken(VBPreprocessorsParser.WS, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode SEMICOLON() { return GetToken(VBPreprocessorsParser.SEMICOLON, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ModuleReferenceComponentContext moduleReferenceComponent() {
			return GetRuleContext<ModuleReferenceComponentContext>(0);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] NEWLINE() { return GetTokens(VBPreprocessorsParser.NEWLINE); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode NEWLINE(int i) {
			return GetToken(VBPreprocessorsParser.NEWLINE, i);
		}
		public ModuleReferenceContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_moduleReference; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterModuleReference(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitModuleReference(this);
		}
	}

	[RuleVersion(0)]
	public ModuleReferenceContext moduleReference() {
		ModuleReferenceContext _localctx = new ModuleReferenceContext(Context, State);
		EnterRule(_localctx, 14, RULE_moduleReference);
		int _la;
		try {
			int _alt;
			EnterOuterAlt(_localctx, 1);
			{
			State = 169;
			Match(OBJECT);
			State = 171;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==WS) {
				{
				State = 170;
				Match(WS);
				}
			}

			State = 173;
			Match(EQ);
			State = 175;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==WS) {
				{
				State = 174;
				Match(WS);
				}
			}

			State = 177;
			moduleReferenceValue();
			State = 183;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==SEMICOLON) {
				{
				State = 178;
				Match(SEMICOLON);
				State = 180;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
				if (_la==WS) {
					{
					State = 179;
					Match(WS);
					}
				}

				State = 182;
				moduleReferenceComponent();
				}
			}

			State = 188;
			ErrorHandler.Sync(this);
			_alt = Interpreter.AdaptivePredict(TokenStream,24,Context);
			while ( _alt!=2 && _alt!=global::Antlr4.Runtime.Atn.ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					{
					{
					State = 185;
					Match(NEWLINE);
					}
					} 
				}
				State = 190;
				ErrorHandler.Sync(this);
				_alt = Interpreter.AdaptivePredict(TokenStream,24,Context);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class ModuleReferenceValueContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode STRINGLITERAL() { return GetToken(VBPreprocessorsParser.STRINGLITERAL, 0); }
		public ModuleReferenceValueContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_moduleReferenceValue; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterModuleReferenceValue(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitModuleReferenceValue(this);
		}
	}

	[RuleVersion(0)]
	public ModuleReferenceValueContext moduleReferenceValue() {
		ModuleReferenceValueContext _localctx = new ModuleReferenceValueContext(Context, State);
		EnterRule(_localctx, 16, RULE_moduleReferenceValue);
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 191;
			Match(STRINGLITERAL);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class ModuleReferenceComponentContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode STRINGLITERAL() { return GetToken(VBPreprocessorsParser.STRINGLITERAL, 0); }
		public ModuleReferenceComponentContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_moduleReferenceComponent; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterModuleReferenceComponent(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitModuleReferenceComponent(this);
		}
	}

	[RuleVersion(0)]
	public ModuleReferenceComponentContext moduleReferenceComponent() {
		ModuleReferenceComponentContext _localctx = new ModuleReferenceComponentContext(Context, State);
		EnterRule(_localctx, 18, RULE_moduleReferenceComponent);
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 193;
			Match(STRINGLITERAL);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class ModuleHeaderContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode VERSION() { return GetToken(VBPreprocessorsParser.VERSION, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] WS() { return GetTokens(VBPreprocessorsParser.WS); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode WS(int i) {
			return GetToken(VBPreprocessorsParser.WS, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode DOUBLELITERAL() { return GetToken(VBPreprocessorsParser.DOUBLELITERAL, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode CLASS() { return GetToken(VBPreprocessorsParser.CLASS, 0); }
		public ModuleHeaderContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_moduleHeader; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterModuleHeader(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitModuleHeader(this);
		}
	}

	[RuleVersion(0)]
	public ModuleHeaderContext moduleHeader() {
		ModuleHeaderContext _localctx = new ModuleHeaderContext(Context, State);
		EnterRule(_localctx, 20, RULE_moduleHeader);
		int _la;
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 195;
			Match(VERSION);
			State = 196;
			Match(WS);
			State = 197;
			Match(DOUBLELITERAL);
			State = 200;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==WS) {
				{
				State = 198;
				Match(WS);
				State = 199;
				Match(CLASS);
				}
			}

			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class ModuleConfigContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode BEGIN() { return GetToken(VBPreprocessorsParser.BEGIN, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode END() { return GetToken(VBPreprocessorsParser.END, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] NEWLINE() { return GetTokens(VBPreprocessorsParser.NEWLINE); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode NEWLINE(int i) {
			return GetToken(VBPreprocessorsParser.NEWLINE, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ModuleConfigElementContext[] moduleConfigElement() {
			return GetRuleContexts<ModuleConfigElementContext>();
		}
		[System.Diagnostics.DebuggerNonUserCode] public ModuleConfigElementContext moduleConfigElement(int i) {
			return GetRuleContext<ModuleConfigElementContext>(i);
		}
		public ModuleConfigContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_moduleConfig; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterModuleConfig(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitModuleConfig(this);
		}
	}

	[RuleVersion(0)]
	public ModuleConfigContext moduleConfig() {
		ModuleConfigContext _localctx = new ModuleConfigContext(Context, State);
		EnterRule(_localctx, 22, RULE_moduleConfig);
		int _la;
		try {
			int _alt;
			EnterOuterAlt(_localctx, 1);
			{
			State = 202;
			Match(BEGIN);
			State = 204;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			do {
				{
				{
				State = 203;
				Match(NEWLINE);
				}
				}
				State = 206;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			} while ( _la==NEWLINE );
			State = 209;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			do {
				{
				{
				State = 208;
				moduleConfigElement();
				}
				}
				State = 211;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			} while ( _la==L_SQUARE_BRACKET || _la==IDENTIFIER );
			State = 213;
			Match(END);
			State = 215;
			ErrorHandler.Sync(this);
			_alt = 1;
			do {
				switch (_alt) {
				case 1:
					{
					{
					State = 214;
					Match(NEWLINE);
					}
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				State = 217;
				ErrorHandler.Sync(this);
				_alt = Interpreter.AdaptivePredict(TokenStream,28,Context);
			} while ( _alt!=2 && _alt!=global::Antlr4.Runtime.Atn.ATN.INVALID_ALT_NUMBER );
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class ModuleConfigElementContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public AmbiguousIdentifierContext ambiguousIdentifier() {
			return GetRuleContext<AmbiguousIdentifierContext>(0);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode EQ() { return GetToken(VBPreprocessorsParser.EQ, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public LiteralContext literal() {
			return GetRuleContext<LiteralContext>(0);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode NEWLINE() { return GetToken(VBPreprocessorsParser.NEWLINE, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] WS() { return GetTokens(VBPreprocessorsParser.WS); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode WS(int i) {
			return GetToken(VBPreprocessorsParser.WS, i);
		}
		public ModuleConfigElementContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_moduleConfigElement; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterModuleConfigElement(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitModuleConfigElement(this);
		}
	}

	[RuleVersion(0)]
	public ModuleConfigElementContext moduleConfigElement() {
		ModuleConfigElementContext _localctx = new ModuleConfigElementContext(Context, State);
		EnterRule(_localctx, 24, RULE_moduleConfigElement);
		int _la;
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 219;
			ambiguousIdentifier();
			State = 221;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==WS) {
				{
				State = 220;
				Match(WS);
				}
			}

			State = 223;
			Match(EQ);
			State = 225;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==WS) {
				{
				State = 224;
				Match(WS);
				}
			}

			State = 227;
			literal();
			State = 228;
			Match(NEWLINE);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class AmbiguousIdentifierContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] IDENTIFIER() { return GetTokens(VBPreprocessorsParser.IDENTIFIER); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode IDENTIFIER(int i) {
			return GetToken(VBPreprocessorsParser.IDENTIFIER, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode L_SQUARE_BRACKET() { return GetToken(VBPreprocessorsParser.L_SQUARE_BRACKET, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode R_SQUARE_BRACKET() { return GetToken(VBPreprocessorsParser.R_SQUARE_BRACKET, 0); }
		public AmbiguousIdentifierContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_ambiguousIdentifier; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterAmbiguousIdentifier(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitAmbiguousIdentifier(this);
		}
	}

	[RuleVersion(0)]
	public AmbiguousIdentifierContext ambiguousIdentifier() {
		AmbiguousIdentifierContext _localctx = new AmbiguousIdentifierContext(Context, State);
		EnterRule(_localctx, 26, RULE_ambiguousIdentifier);
		int _la;
		try {
			int _alt;
			State = 242;
			ErrorHandler.Sync(this);
			switch (TokenStream.LA(1)) {
			case IDENTIFIER:
				EnterOuterAlt(_localctx, 1);
				{
				State = 231;
				ErrorHandler.Sync(this);
				_alt = 1;
				do {
					switch (_alt) {
					case 1:
						{
						{
						State = 230;
						Match(IDENTIFIER);
						}
						}
						break;
					default:
						throw new NoViableAltException(this);
					}
					State = 233;
					ErrorHandler.Sync(this);
					_alt = Interpreter.AdaptivePredict(TokenStream,31,Context);
				} while ( _alt!=2 && _alt!=global::Antlr4.Runtime.Atn.ATN.INVALID_ALT_NUMBER );
				}
				break;
			case L_SQUARE_BRACKET:
				EnterOuterAlt(_localctx, 2);
				{
				State = 235;
				Match(L_SQUARE_BRACKET);
				State = 237;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
				do {
					{
					{
					State = 236;
					Match(IDENTIFIER);
					}
					}
					State = 239;
					ErrorHandler.Sync(this);
					_la = TokenStream.LA(1);
				} while ( _la==IDENTIFIER );
				State = 241;
				Match(R_SQUARE_BRACKET);
				}
				break;
			default:
				throw new NoViableAltException(this);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class ModuleAttributesContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public AttributeStmtContext[] attributeStmt() {
			return GetRuleContexts<AttributeStmtContext>();
		}
		[System.Diagnostics.DebuggerNonUserCode] public AttributeStmtContext attributeStmt(int i) {
			return GetRuleContext<AttributeStmtContext>(i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] NEWLINE() { return GetTokens(VBPreprocessorsParser.NEWLINE); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode NEWLINE(int i) {
			return GetToken(VBPreprocessorsParser.NEWLINE, i);
		}
		public ModuleAttributesContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_moduleAttributes; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterModuleAttributes(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitModuleAttributes(this);
		}
	}

	[RuleVersion(0)]
	public ModuleAttributesContext moduleAttributes() {
		ModuleAttributesContext _localctx = new ModuleAttributesContext(Context, State);
		EnterRule(_localctx, 28, RULE_moduleAttributes);
		int _la;
		try {
			int _alt;
			EnterOuterAlt(_localctx, 1);
			{
			State = 250;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			do {
				{
				{
				State = 244;
				attributeStmt();
				State = 246;
				ErrorHandler.Sync(this);
				_alt = 1;
				do {
					switch (_alt) {
					case 1:
						{
						{
						State = 245;
						Match(NEWLINE);
						}
						}
						break;
					default:
						throw new NoViableAltException(this);
					}
					State = 248;
					ErrorHandler.Sync(this);
					_alt = Interpreter.AdaptivePredict(TokenStream,34,Context);
				} while ( _alt!=2 && _alt!=global::Antlr4.Runtime.Atn.ATN.INVALID_ALT_NUMBER );
				}
				}
				State = 252;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			} while ( _la==ATTRIBUTE );
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class MacroIfThenElseStmtContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public MacroIfBlockStmtContext macroIfBlockStmt() {
			return GetRuleContext<MacroIfBlockStmtContext>(0);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode MACRO_END_IF() { return GetToken(VBPreprocessorsParser.MACRO_END_IF, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public MacroElseIfBlockStmtContext[] macroElseIfBlockStmt() {
			return GetRuleContexts<MacroElseIfBlockStmtContext>();
		}
		[System.Diagnostics.DebuggerNonUserCode] public MacroElseIfBlockStmtContext macroElseIfBlockStmt(int i) {
			return GetRuleContext<MacroElseIfBlockStmtContext>(i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public MacroElseBlockStmtContext macroElseBlockStmt() {
			return GetRuleContext<MacroElseBlockStmtContext>(0);
		}
		public MacroIfThenElseStmtContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_macroIfThenElseStmt; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterMacroIfThenElseStmt(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitMacroIfThenElseStmt(this);
		}
	}

	[RuleVersion(0)]
	public MacroIfThenElseStmtContext macroIfThenElseStmt() {
		MacroIfThenElseStmtContext _localctx = new MacroIfThenElseStmtContext(Context, State);
		EnterRule(_localctx, 30, RULE_macroIfThenElseStmt);
		int _la;
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 254;
			macroIfBlockStmt();
			State = 258;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			while (_la==MACRO_ELSEIF) {
				{
				{
				State = 255;
				macroElseIfBlockStmt();
				}
				}
				State = 260;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			}
			State = 262;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==MACRO_ELSE) {
				{
				State = 261;
				macroElseBlockStmt();
				}
			}

			State = 264;
			Match(MACRO_END_IF);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class MacroIfBlockCondStmtContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode MACRO_IF() { return GetToken(VBPreprocessorsParser.MACRO_IF, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] WS() { return GetTokens(VBPreprocessorsParser.WS); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode WS(int i) {
			return GetToken(VBPreprocessorsParser.WS, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public IfConditionStmtContext ifConditionStmt() {
			return GetRuleContext<IfConditionStmtContext>(0);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode THEN() { return GetToken(VBPreprocessorsParser.THEN, 0); }
		public MacroIfBlockCondStmtContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_macroIfBlockCondStmt; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterMacroIfBlockCondStmt(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitMacroIfBlockCondStmt(this);
		}
	}

	[RuleVersion(0)]
	public MacroIfBlockCondStmtContext macroIfBlockCondStmt() {
		MacroIfBlockCondStmtContext _localctx = new MacroIfBlockCondStmtContext(Context, State);
		EnterRule(_localctx, 32, RULE_macroIfBlockCondStmt);
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 266;
			Match(MACRO_IF);
			State = 267;
			Match(WS);
			State = 268;
			ifConditionStmt();
			State = 269;
			Match(WS);
			State = 270;
			Match(THEN);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class MacroIfBlockStmtContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public MacroIfBlockCondStmtContext macroIfBlockCondStmt() {
			return GetRuleContext<MacroIfBlockCondStmtContext>(0);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode WS() { return GetToken(VBPreprocessorsParser.WS, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public CommentBlockContext[] commentBlock() {
			return GetRuleContexts<CommentBlockContext>();
		}
		[System.Diagnostics.DebuggerNonUserCode] public CommentBlockContext commentBlock(int i) {
			return GetRuleContext<CommentBlockContext>(i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] NEWLINE() { return GetTokens(VBPreprocessorsParser.NEWLINE); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode NEWLINE(int i) {
			return GetToken(VBPreprocessorsParser.NEWLINE, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public CodeBlockContext[] codeBlock() {
			return GetRuleContexts<CodeBlockContext>();
		}
		[System.Diagnostics.DebuggerNonUserCode] public CodeBlockContext codeBlock(int i) {
			return GetRuleContext<CodeBlockContext>(i);
		}
		public MacroIfBlockStmtContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_macroIfBlockStmt; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterMacroIfBlockStmt(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitMacroIfBlockStmt(this);
		}
	}

	[RuleVersion(0)]
	public MacroIfBlockStmtContext macroIfBlockStmt() {
		MacroIfBlockStmtContext _localctx = new MacroIfBlockStmtContext(Context, State);
		EnterRule(_localctx, 34, RULE_macroIfBlockStmt);
		int _la;
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 272;
			macroIfBlockCondStmt();
			State = 274;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==WS) {
				{
				State = 273;
				Match(WS);
				}
			}

			State = 279;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			while (_la==COMMENT) {
				{
				{
				State = 276;
				commentBlock();
				}
				}
				State = 281;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			}
			State = 283;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			do {
				{
				{
				State = 282;
				Match(NEWLINE);
				}
				}
				State = 285;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			} while ( _la==NEWLINE );
			State = 290;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			while ((((_la) & ~0x3f) == 0 && ((1L << _la) & -7340034L) != 0) || ((((_la - 64)) & ~0x3f) == 0 && ((1L << (_la - 64)) & 509L) != 0)) {
				{
				{
				State = 287;
				codeBlock();
				}
				}
				State = 292;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class MacroElseIfBlockStmtContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode MACRO_ELSEIF() { return GetToken(VBPreprocessorsParser.MACRO_ELSEIF, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] WS() { return GetTokens(VBPreprocessorsParser.WS); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode WS(int i) {
			return GetToken(VBPreprocessorsParser.WS, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public IfConditionStmtContext ifConditionStmt() {
			return GetRuleContext<IfConditionStmtContext>(0);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode THEN() { return GetToken(VBPreprocessorsParser.THEN, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public CommentBlockContext[] commentBlock() {
			return GetRuleContexts<CommentBlockContext>();
		}
		[System.Diagnostics.DebuggerNonUserCode] public CommentBlockContext commentBlock(int i) {
			return GetRuleContext<CommentBlockContext>(i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] NEWLINE() { return GetTokens(VBPreprocessorsParser.NEWLINE); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode NEWLINE(int i) {
			return GetToken(VBPreprocessorsParser.NEWLINE, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public CodeBlockContext[] codeBlock() {
			return GetRuleContexts<CodeBlockContext>();
		}
		[System.Diagnostics.DebuggerNonUserCode] public CodeBlockContext codeBlock(int i) {
			return GetRuleContext<CodeBlockContext>(i);
		}
		public MacroElseIfBlockStmtContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_macroElseIfBlockStmt; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterMacroElseIfBlockStmt(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitMacroElseIfBlockStmt(this);
		}
	}

	[RuleVersion(0)]
	public MacroElseIfBlockStmtContext macroElseIfBlockStmt() {
		MacroElseIfBlockStmtContext _localctx = new MacroElseIfBlockStmtContext(Context, State);
		EnterRule(_localctx, 36, RULE_macroElseIfBlockStmt);
		int _la;
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 293;
			Match(MACRO_ELSEIF);
			State = 294;
			Match(WS);
			State = 295;
			ifConditionStmt();
			State = 296;
			Match(WS);
			State = 297;
			Match(THEN);
			State = 299;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==WS) {
				{
				State = 298;
				Match(WS);
				}
			}

			State = 304;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			while (_la==COMMENT) {
				{
				{
				State = 301;
				commentBlock();
				}
				}
				State = 306;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			}
			State = 308;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			do {
				{
				{
				State = 307;
				Match(NEWLINE);
				}
				}
				State = 310;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			} while ( _la==NEWLINE );
			State = 315;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			while ((((_la) & ~0x3f) == 0 && ((1L << _la) & -7340034L) != 0) || ((((_la - 64)) & ~0x3f) == 0 && ((1L << (_la - 64)) & 509L) != 0)) {
				{
				{
				State = 312;
				codeBlock();
				}
				}
				State = 317;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class MacroElseBlockStmtContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode MACRO_ELSE() { return GetToken(VBPreprocessorsParser.MACRO_ELSE, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode WS() { return GetToken(VBPreprocessorsParser.WS, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public CommentBlockContext[] commentBlock() {
			return GetRuleContexts<CommentBlockContext>();
		}
		[System.Diagnostics.DebuggerNonUserCode] public CommentBlockContext commentBlock(int i) {
			return GetRuleContext<CommentBlockContext>(i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] NEWLINE() { return GetTokens(VBPreprocessorsParser.NEWLINE); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode NEWLINE(int i) {
			return GetToken(VBPreprocessorsParser.NEWLINE, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public CodeBlockContext[] codeBlock() {
			return GetRuleContexts<CodeBlockContext>();
		}
		[System.Diagnostics.DebuggerNonUserCode] public CodeBlockContext codeBlock(int i) {
			return GetRuleContext<CodeBlockContext>(i);
		}
		public MacroElseBlockStmtContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_macroElseBlockStmt; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterMacroElseBlockStmt(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitMacroElseBlockStmt(this);
		}
	}

	[RuleVersion(0)]
	public MacroElseBlockStmtContext macroElseBlockStmt() {
		MacroElseBlockStmtContext _localctx = new MacroElseBlockStmtContext(Context, State);
		EnterRule(_localctx, 38, RULE_macroElseBlockStmt);
		int _la;
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 318;
			Match(MACRO_ELSE);
			State = 320;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==WS) {
				{
				State = 319;
				Match(WS);
				}
			}

			State = 325;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			while (_la==COMMENT) {
				{
				{
				State = 322;
				commentBlock();
				}
				}
				State = 327;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			}
			State = 329;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			do {
				{
				{
				State = 328;
				Match(NEWLINE);
				}
				}
				State = 331;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			} while ( _la==NEWLINE );
			State = 336;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			while ((((_la) & ~0x3f) == 0 && ((1L << _la) & -7340034L) != 0) || ((((_la - 64)) & ~0x3f) == 0 && ((1L << (_la - 64)) & 509L) != 0)) {
				{
				{
				State = 333;
				codeBlock();
				}
				}
				State = 338;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class CommentBlockContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] COMMENT() { return GetTokens(VBPreprocessorsParser.COMMENT); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode COMMENT(int i) {
			return GetToken(VBPreprocessorsParser.COMMENT, i);
		}
		public CommentBlockContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_commentBlock; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterCommentBlock(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitCommentBlock(this);
		}
	}

	[RuleVersion(0)]
	public CommentBlockContext commentBlock() {
		CommentBlockContext _localctx = new CommentBlockContext(Context, State);
		EnterRule(_localctx, 40, RULE_commentBlock);
		try {
			int _alt;
			EnterOuterAlt(_localctx, 1);
			{
			State = 340;
			ErrorHandler.Sync(this);
			_alt = 1;
			do {
				switch (_alt) {
				case 1:
					{
					{
					State = 339;
					Match(COMMENT);
					}
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				State = 342;
				ErrorHandler.Sync(this);
				_alt = Interpreter.AdaptivePredict(TokenStream,50,Context);
			} while ( _alt!=2 && _alt!=global::Antlr4.Runtime.Atn.ATN.INVALID_ALT_NUMBER );
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class LineLabelContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode LABEL_L() { return GetToken(VBPreprocessorsParser.LABEL_L, 0); }
		public LineLabelContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_lineLabel; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterLineLabel(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitLineLabel(this);
		}
	}

	[RuleVersion(0)]
	public LineLabelContext lineLabel() {
		LineLabelContext _localctx = new LineLabelContext(Context, State);
		EnterRule(_localctx, 42, RULE_lineLabel);
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 344;
			Match(LABEL_L);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class NonMacroCodeBlockLineContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public NonMacroCodeStmtContext nonMacroCodeStmt() {
			return GetRuleContext<NonMacroCodeStmtContext>(0);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode COMMENT() { return GetToken(VBPreprocessorsParser.COMMENT, 0); }
		public NonMacroCodeBlockLineContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_nonMacroCodeBlockLine; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterNonMacroCodeBlockLine(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitNonMacroCodeBlockLine(this);
		}
	}

	[RuleVersion(0)]
	public NonMacroCodeBlockLineContext nonMacroCodeBlockLine() {
		NonMacroCodeBlockLineContext _localctx = new NonMacroCodeBlockLineContext(Context, State);
		EnterRule(_localctx, 44, RULE_nonMacroCodeBlockLine);
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 346;
			nonMacroCodeStmt();
			State = 348;
			ErrorHandler.Sync(this);
			switch ( Interpreter.AdaptivePredict(TokenStream,51,Context) ) {
			case 1:
				{
				State = 347;
				Match(COMMENT);
				}
				break;
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class NonMacroCodeStmtContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] MACRO_IF() { return GetTokens(VBPreprocessorsParser.MACRO_IF); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode MACRO_IF(int i) {
			return GetToken(VBPreprocessorsParser.MACRO_IF, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] MACRO_ELSE() { return GetTokens(VBPreprocessorsParser.MACRO_ELSE); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode MACRO_ELSE(int i) {
			return GetToken(VBPreprocessorsParser.MACRO_ELSE, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] MACRO_ELSEIF() { return GetTokens(VBPreprocessorsParser.MACRO_ELSEIF); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode MACRO_ELSEIF(int i) {
			return GetToken(VBPreprocessorsParser.MACRO_ELSEIF, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] MACRO_END_IF() { return GetTokens(VBPreprocessorsParser.MACRO_END_IF); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode MACRO_END_IF(int i) {
			return GetToken(VBPreprocessorsParser.MACRO_END_IF, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] MACRO_CONST() { return GetTokens(VBPreprocessorsParser.MACRO_CONST); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode MACRO_CONST(int i) {
			return GetToken(VBPreprocessorsParser.MACRO_CONST, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] COMMENT() { return GetTokens(VBPreprocessorsParser.COMMENT); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode COMMENT(int i) {
			return GetToken(VBPreprocessorsParser.COMMENT, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] NEWLINE() { return GetTokens(VBPreprocessorsParser.NEWLINE); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode NEWLINE(int i) {
			return GetToken(VBPreprocessorsParser.NEWLINE, i);
		}
		public NonMacroCodeStmtContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_nonMacroCodeStmt; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterNonMacroCodeStmt(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitNonMacroCodeStmt(this);
		}
	}

	[RuleVersion(0)]
	public NonMacroCodeStmtContext nonMacroCodeStmt() {
		NonMacroCodeStmtContext _localctx = new NonMacroCodeStmtContext(Context, State);
		EnterRule(_localctx, 46, RULE_nonMacroCodeStmt);
		int _la;
		try {
			int _alt;
			EnterOuterAlt(_localctx, 1);
			{
			State = 351;
			ErrorHandler.Sync(this);
			_alt = 1;
			do {
				switch (_alt) {
				case 1:
					{
					{
					State = 350;
					_la = TokenStream.LA(1);
					if ( _la <= 0 || ((((_la) & ~0x3f) == 0 && ((1L << _la) & 7864322L) != 0) || _la==NEWLINE || _la==COMMENT) ) {
					ErrorHandler.RecoverInline(this);
					}
					else {
						ErrorHandler.ReportMatch(this);
					    Consume();
					}
					}
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				State = 353;
				ErrorHandler.Sync(this);
				_alt = Interpreter.AdaptivePredict(TokenStream,52,Context);
			} while ( _alt!=2 && _alt!=global::Antlr4.Runtime.Atn.ATN.INVALID_ALT_NUMBER );
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class ControlPropertiesContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode BEGIN() { return GetToken(VBPreprocessorsParser.BEGIN, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] WS() { return GetTokens(VBPreprocessorsParser.WS); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode WS(int i) {
			return GetToken(VBPreprocessorsParser.WS, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public Cp_ControlTypeContext cp_ControlType() {
			return GetRuleContext<Cp_ControlTypeContext>(0);
		}
		[System.Diagnostics.DebuggerNonUserCode] public Cp_ControlIdentifierContext cp_ControlIdentifier() {
			return GetRuleContext<Cp_ControlIdentifierContext>(0);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode END() { return GetToken(VBPreprocessorsParser.END, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] NEWLINE() { return GetTokens(VBPreprocessorsParser.NEWLINE); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode NEWLINE(int i) {
			return GetToken(VBPreprocessorsParser.NEWLINE, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public Cp_PropertiesContext[] cp_Properties() {
			return GetRuleContexts<Cp_PropertiesContext>();
		}
		[System.Diagnostics.DebuggerNonUserCode] public Cp_PropertiesContext cp_Properties(int i) {
			return GetRuleContext<Cp_PropertiesContext>(i);
		}
		public ControlPropertiesContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_controlProperties; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterControlProperties(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitControlProperties(this);
		}
	}

	[RuleVersion(0)]
	public ControlPropertiesContext controlProperties() {
		ControlPropertiesContext _localctx = new ControlPropertiesContext(Context, State);
		EnterRule(_localctx, 48, RULE_controlProperties);
		int _la;
		try {
			int _alt;
			EnterOuterAlt(_localctx, 1);
			{
			State = 356;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==WS) {
				{
				State = 355;
				Match(WS);
				}
			}

			State = 358;
			Match(BEGIN);
			State = 359;
			Match(WS);
			State = 360;
			cp_ControlType();
			State = 361;
			Match(WS);
			State = 362;
			cp_ControlIdentifier();
			State = 364;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==WS) {
				{
				State = 363;
				Match(WS);
				}
			}

			State = 367;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			do {
				{
				{
				State = 366;
				Match(NEWLINE);
				}
				}
				State = 369;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			} while ( _la==NEWLINE );
			State = 372;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			do {
				{
				{
				State = 371;
				cp_Properties();
				}
				}
				State = 374;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			} while ( _la==BEGIN || _la==BEGINPROPERTY || _la==WS );
			State = 376;
			Match(END);
			State = 380;
			ErrorHandler.Sync(this);
			_alt = Interpreter.AdaptivePredict(TokenStream,57,Context);
			while ( _alt!=2 && _alt!=global::Antlr4.Runtime.Atn.ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					{
					{
					State = 377;
					Match(NEWLINE);
					}
					} 
				}
				State = 382;
				ErrorHandler.Sync(this);
				_alt = Interpreter.AdaptivePredict(TokenStream,57,Context);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class Cp_PropertiesContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public Cp_NestedPropertyContext cp_NestedProperty() {
			return GetRuleContext<Cp_NestedPropertyContext>(0);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ControlPropertiesContext controlProperties() {
			return GetRuleContext<ControlPropertiesContext>(0);
		}
		public Cp_PropertiesContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_cp_Properties; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterCp_Properties(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitCp_Properties(this);
		}
	}

	[RuleVersion(0)]
	public Cp_PropertiesContext cp_Properties() {
		Cp_PropertiesContext _localctx = new Cp_PropertiesContext(Context, State);
		EnterRule(_localctx, 50, RULE_cp_Properties);
		try {
			State = 385;
			ErrorHandler.Sync(this);
			switch ( Interpreter.AdaptivePredict(TokenStream,58,Context) ) {
			case 1:
				EnterOuterAlt(_localctx, 1);
				{
				State = 383;
				cp_NestedProperty();
				}
				break;
			case 2:
				EnterOuterAlt(_localctx, 2);
				{
				State = 384;
				controlProperties();
				}
				break;
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class Cp_NestedPropertyContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode BEGINPROPERTY() { return GetToken(VBPreprocessorsParser.BEGINPROPERTY, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] WS() { return GetTokens(VBPreprocessorsParser.WS); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode WS(int i) {
			return GetToken(VBPreprocessorsParser.WS, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public AmbiguousIdentifierContext ambiguousIdentifier() {
			return GetRuleContext<AmbiguousIdentifierContext>(0);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode ENDPROPERTY() { return GetToken(VBPreprocessorsParser.ENDPROPERTY, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode LPAREN() { return GetToken(VBPreprocessorsParser.LPAREN, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode INTEGERLITERAL() { return GetToken(VBPreprocessorsParser.INTEGERLITERAL, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode RPAREN() { return GetToken(VBPreprocessorsParser.RPAREN, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode GUID() { return GetToken(VBPreprocessorsParser.GUID, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] NEWLINE() { return GetTokens(VBPreprocessorsParser.NEWLINE); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode NEWLINE(int i) {
			return GetToken(VBPreprocessorsParser.NEWLINE, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public Cp_PropertiesContext[] cp_Properties() {
			return GetRuleContexts<Cp_PropertiesContext>();
		}
		[System.Diagnostics.DebuggerNonUserCode] public Cp_PropertiesContext cp_Properties(int i) {
			return GetRuleContext<Cp_PropertiesContext>(i);
		}
		public Cp_NestedPropertyContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_cp_NestedProperty; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterCp_NestedProperty(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitCp_NestedProperty(this);
		}
	}

	[RuleVersion(0)]
	public Cp_NestedPropertyContext cp_NestedProperty() {
		Cp_NestedPropertyContext _localctx = new Cp_NestedPropertyContext(Context, State);
		EnterRule(_localctx, 52, RULE_cp_NestedProperty);
		int _la;
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 388;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==WS) {
				{
				State = 387;
				Match(WS);
				}
			}

			State = 390;
			Match(BEGINPROPERTY);
			State = 391;
			Match(WS);
			State = 392;
			ambiguousIdentifier();
			State = 396;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==LPAREN) {
				{
				State = 393;
				Match(LPAREN);
				State = 394;
				Match(INTEGERLITERAL);
				State = 395;
				Match(RPAREN);
				}
			}

			State = 400;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==WS) {
				{
				State = 398;
				Match(WS);
				State = 399;
				Match(GUID);
				}
			}

			State = 403;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			do {
				{
				{
				State = 402;
				Match(NEWLINE);
				}
				}
				State = 405;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			} while ( _la==NEWLINE );
			State = 412;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==BEGIN || _la==BEGINPROPERTY || _la==WS) {
				{
				State = 408;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
				do {
					{
					{
					State = 407;
					cp_Properties();
					}
					}
					State = 410;
					ErrorHandler.Sync(this);
					_la = TokenStream.LA(1);
				} while ( _la==BEGIN || _la==BEGINPROPERTY || _la==WS );
				}
			}

			State = 414;
			Match(ENDPROPERTY);
			State = 416;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			do {
				{
				{
				State = 415;
				Match(NEWLINE);
				}
				}
				State = 418;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			} while ( _la==NEWLINE );
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class Cp_ControlTypeContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ComplexTypeContext complexType() {
			return GetRuleContext<ComplexTypeContext>(0);
		}
		public Cp_ControlTypeContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_cp_ControlType; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterCp_ControlType(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitCp_ControlType(this);
		}
	}

	[RuleVersion(0)]
	public Cp_ControlTypeContext cp_ControlType() {
		Cp_ControlTypeContext _localctx = new Cp_ControlTypeContext(Context, State);
		EnterRule(_localctx, 54, RULE_cp_ControlType);
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 420;
			complexType();
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class ComplexTypeContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public AmbiguousIdentifierContext[] ambiguousIdentifier() {
			return GetRuleContexts<AmbiguousIdentifierContext>();
		}
		[System.Diagnostics.DebuggerNonUserCode] public AmbiguousIdentifierContext ambiguousIdentifier(int i) {
			return GetRuleContext<AmbiguousIdentifierContext>(i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] DOT() { return GetTokens(VBPreprocessorsParser.DOT); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode DOT(int i) {
			return GetToken(VBPreprocessorsParser.DOT, i);
		}
		public ComplexTypeContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_complexType; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterComplexType(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitComplexType(this);
		}
	}

	[RuleVersion(0)]
	public ComplexTypeContext complexType() {
		ComplexTypeContext _localctx = new ComplexTypeContext(Context, State);
		EnterRule(_localctx, 56, RULE_complexType);
		int _la;
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 422;
			ambiguousIdentifier();
			State = 427;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			while (_la==DOT) {
				{
				{
				State = 423;
				Match(DOT);
				State = 424;
				ambiguousIdentifier();
				}
				}
				State = 429;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class Cp_ControlIdentifierContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public AmbiguousIdentifierContext ambiguousIdentifier() {
			return GetRuleContext<AmbiguousIdentifierContext>(0);
		}
		public Cp_ControlIdentifierContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_cp_ControlIdentifier; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterCp_ControlIdentifier(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitCp_ControlIdentifier(this);
		}
	}

	[RuleVersion(0)]
	public Cp_ControlIdentifierContext cp_ControlIdentifier() {
		Cp_ControlIdentifierContext _localctx = new Cp_ControlIdentifierContext(Context, State);
		EnterRule(_localctx, 58, RULE_cp_ControlIdentifier);
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 430;
			ambiguousIdentifier();
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class AttributeStmtContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode ATTRIBUTE() { return GetToken(VBPreprocessorsParser.ATTRIBUTE, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] WS() { return GetTokens(VBPreprocessorsParser.WS); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode WS(int i) {
			return GetToken(VBPreprocessorsParser.WS, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public AmbiguousIdentifierContext ambiguousIdentifier() {
			return GetRuleContext<AmbiguousIdentifierContext>(0);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode EQ() { return GetToken(VBPreprocessorsParser.EQ, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public LiteralContext[] literal() {
			return GetRuleContexts<LiteralContext>();
		}
		[System.Diagnostics.DebuggerNonUserCode] public LiteralContext literal(int i) {
			return GetRuleContext<LiteralContext>(i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] COMMA() { return GetTokens(VBPreprocessorsParser.COMMA); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode COMMA(int i) {
			return GetToken(VBPreprocessorsParser.COMMA, i);
		}
		public AttributeStmtContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_attributeStmt; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterAttributeStmt(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitAttributeStmt(this);
		}
	}

	[RuleVersion(0)]
	public AttributeStmtContext attributeStmt() {
		AttributeStmtContext _localctx = new AttributeStmtContext(Context, State);
		EnterRule(_localctx, 60, RULE_attributeStmt);
		int _la;
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 432;
			Match(ATTRIBUTE);
			State = 433;
			Match(WS);
			State = 434;
			ambiguousIdentifier();
			State = 436;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==WS) {
				{
				State = 435;
				Match(WS);
				}
			}

			State = 438;
			Match(EQ);
			State = 440;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			if (_la==WS) {
				{
				State = 439;
				Match(WS);
				}
			}

			State = 442;
			literal();
			State = 453;
			ErrorHandler.Sync(this);
			_la = TokenStream.LA(1);
			while (_la==COMMA || _la==WS) {
				{
				{
				State = 444;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
				if (_la==WS) {
					{
					State = 443;
					Match(WS);
					}
				}

				State = 446;
				Match(COMMA);
				State = 448;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
				if (_la==WS) {
					{
					State = 447;
					Match(WS);
					}
				}

				State = 450;
				literal();
				}
				}
				State = 455;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class IfConditionStmtContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ValueStmtContext valueStmt() {
			return GetRuleContext<ValueStmtContext>(0);
		}
		public IfConditionStmtContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_ifConditionStmt; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterIfConditionStmt(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitIfConditionStmt(this);
		}
	}

	[RuleVersion(0)]
	public IfConditionStmtContext ifConditionStmt() {
		IfConditionStmtContext _localctx = new IfConditionStmtContext(Context, State);
		EnterRule(_localctx, 62, RULE_ifConditionStmt);
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 456;
			valueStmt(0);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class ValueStmtContext : ParserRuleContext {
		public ValueStmtContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_valueStmt; } }
	 
		public ValueStmtContext() { }
		public virtual void CopyFrom(ValueStmtContext context) {
			base.CopyFrom(context);
		}
	}
	public partial class VsStructContext : ValueStmtContext {
		public ValueStmtContext valUnary;
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode LPAREN() { return GetToken(VBPreprocessorsParser.LPAREN, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode RPAREN() { return GetToken(VBPreprocessorsParser.RPAREN, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ValueStmtContext[] valueStmt() {
			return GetRuleContexts<ValueStmtContext>();
		}
		[System.Diagnostics.DebuggerNonUserCode] public ValueStmtContext valueStmt(int i) {
			return GetRuleContext<ValueStmtContext>(i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] WS() { return GetTokens(VBPreprocessorsParser.WS); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode WS(int i) {
			return GetToken(VBPreprocessorsParser.WS, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] COMMA() { return GetTokens(VBPreprocessorsParser.COMMA); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode COMMA(int i) {
			return GetToken(VBPreprocessorsParser.COMMA, i);
		}
		public VsStructContext(ValueStmtContext context) { CopyFrom(context); }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterVsStruct(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitVsStruct(this);
		}
	}
	public partial class VsLiteralContext : ValueStmtContext {
		public LiteralContext valUnary;
		[System.Diagnostics.DebuggerNonUserCode] public LiteralContext literal() {
			return GetRuleContext<LiteralContext>(0);
		}
		public VsLiteralContext(ValueStmtContext context) { CopyFrom(context); }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterVsLiteral(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitVsLiteral(this);
		}
	}
	public partial class VsConstantContext : ValueStmtContext {
		public AmbiguousIdentifierContext valUnary;
		[System.Diagnostics.DebuggerNonUserCode] public AmbiguousIdentifierContext ambiguousIdentifier() {
			return GetRuleContext<AmbiguousIdentifierContext>(0);
		}
		public VsConstantContext(ValueStmtContext context) { CopyFrom(context); }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterVsConstant(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitVsConstant(this);
		}
	}
	public partial class VsUnaryOperationContext : ValueStmtContext {
		public IToken @operator;
		public ValueStmtContext valUnary;
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode PLUS() { return GetToken(VBPreprocessorsParser.PLUS, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ValueStmtContext valueStmt() {
			return GetRuleContext<ValueStmtContext>(0);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] WS() { return GetTokens(VBPreprocessorsParser.WS); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode WS(int i) {
			return GetToken(VBPreprocessorsParser.WS, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode MINUS() { return GetToken(VBPreprocessorsParser.MINUS, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode NOT() { return GetToken(VBPreprocessorsParser.NOT, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode LPAREN() { return GetToken(VBPreprocessorsParser.LPAREN, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode RPAREN() { return GetToken(VBPreprocessorsParser.RPAREN, 0); }
		public VsUnaryOperationContext(ValueStmtContext context) { CopyFrom(context); }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterVsUnaryOperation(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitVsUnaryOperation(this);
		}
	}
	public partial class VsDualOperationContext : ValueStmtContext {
		public ValueStmtContext left;
		public IToken @operator;
		public ValueStmtContext right;
		[System.Diagnostics.DebuggerNonUserCode] public ValueStmtContext[] valueStmt() {
			return GetRuleContexts<ValueStmtContext>();
		}
		[System.Diagnostics.DebuggerNonUserCode] public ValueStmtContext valueStmt(int i) {
			return GetRuleContext<ValueStmtContext>(i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode POW() { return GetToken(VBPreprocessorsParser.POW, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode[] WS() { return GetTokens(VBPreprocessorsParser.WS); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode WS(int i) {
			return GetToken(VBPreprocessorsParser.WS, i);
		}
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode MULT() { return GetToken(VBPreprocessorsParser.MULT, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode DIV() { return GetToken(VBPreprocessorsParser.DIV, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode MOD() { return GetToken(VBPreprocessorsParser.MOD, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode PLUS() { return GetToken(VBPreprocessorsParser.PLUS, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode MINUS() { return GetToken(VBPreprocessorsParser.MINUS, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode AMPERSAND() { return GetToken(VBPreprocessorsParser.AMPERSAND, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode EQ() { return GetToken(VBPreprocessorsParser.EQ, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode NEQ() { return GetToken(VBPreprocessorsParser.NEQ, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode LT() { return GetToken(VBPreprocessorsParser.LT, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode GT() { return GetToken(VBPreprocessorsParser.GT, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode LEQ() { return GetToken(VBPreprocessorsParser.LEQ, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode GEQ() { return GetToken(VBPreprocessorsParser.GEQ, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode AND() { return GetToken(VBPreprocessorsParser.AND, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode OR() { return GetToken(VBPreprocessorsParser.OR, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode XOR() { return GetToken(VBPreprocessorsParser.XOR, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode EQV() { return GetToken(VBPreprocessorsParser.EQV, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode IMP() { return GetToken(VBPreprocessorsParser.IMP, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode LIKE() { return GetToken(VBPreprocessorsParser.LIKE, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode IS() { return GetToken(VBPreprocessorsParser.IS, 0); }
		public VsDualOperationContext(ValueStmtContext context) { CopyFrom(context); }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterVsDualOperation(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitVsDualOperation(this);
		}
	}

	[RuleVersion(0)]
	public ValueStmtContext valueStmt() {
		return valueStmt(0);
	}

	private ValueStmtContext valueStmt(int _p) {
		ParserRuleContext _parentctx = Context;
		int _parentState = State;
		ValueStmtContext _localctx = new ValueStmtContext(Context, _parentState);
		ValueStmtContext _prevctx = _localctx;
		int _startState = 64;
		EnterRecursionRule(_localctx, 64, RULE_valueStmt, _p);
		int _la;
		try {
			int _alt;
			EnterOuterAlt(_localctx, 1);
			{
			State = 509;
			ErrorHandler.Sync(this);
			switch (TokenStream.LA(1)) {
			case FALSE:
			case TRUE:
			case STRINGLITERAL:
			case DATELITERAL:
			case HEXLITERAL:
			case INTEGERLITERAL:
			case DOUBLELITERAL:
			case OCTALLITERAL:
				{
				_localctx = new VsLiteralContext(_localctx);
				Context = _localctx;
				_prevctx = _localctx;

				State = 459;
				((VsLiteralContext)_localctx).valUnary = literal();
				}
				break;
			case L_SQUARE_BRACKET:
			case IDENTIFIER:
				{
				_localctx = new VsConstantContext(_localctx);
				Context = _localctx;
				_prevctx = _localctx;
				State = 460;
				((VsConstantContext)_localctx).valUnary = ambiguousIdentifier();
				}
				break;
			case LPAREN:
				{
				_localctx = new VsStructContext(_localctx);
				Context = _localctx;
				_prevctx = _localctx;
				State = 461;
				Match(LPAREN);
				State = 463;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
				if (_la==WS) {
					{
					State = 462;
					Match(WS);
					}
				}

				State = 465;
				((VsStructContext)_localctx).valUnary = valueStmt(0);
				State = 476;
				ErrorHandler.Sync(this);
				_alt = Interpreter.AdaptivePredict(TokenStream,75,Context);
				while ( _alt!=2 && _alt!=global::Antlr4.Runtime.Atn.ATN.INVALID_ALT_NUMBER ) {
					if ( _alt==1 ) {
						{
						{
						State = 467;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 466;
							Match(WS);
							}
						}

						State = 469;
						Match(COMMA);
						State = 471;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 470;
							Match(WS);
							}
						}

						State = 473;
						valueStmt(0);
						}
						} 
					}
					State = 478;
					ErrorHandler.Sync(this);
					_alt = Interpreter.AdaptivePredict(TokenStream,75,Context);
				}
				State = 480;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
				if (_la==WS) {
					{
					State = 479;
					Match(WS);
					}
				}

				State = 482;
				Match(RPAREN);
				}
				break;
			case PLUS:
				{
				_localctx = new VsUnaryOperationContext(_localctx);
				Context = _localctx;
				_prevctx = _localctx;
				State = 484;
				((VsUnaryOperationContext)_localctx).@operator = Match(PLUS);
				State = 486;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
				if (_la==WS) {
					{
					State = 485;
					Match(WS);
					}
				}

				State = 488;
				((VsUnaryOperationContext)_localctx).valUnary = valueStmt(20);
				}
				break;
			case MINUS:
				{
				_localctx = new VsUnaryOperationContext(_localctx);
				Context = _localctx;
				_prevctx = _localctx;
				State = 489;
				((VsUnaryOperationContext)_localctx).@operator = Match(MINUS);
				State = 491;
				ErrorHandler.Sync(this);
				_la = TokenStream.LA(1);
				if (_la==WS) {
					{
					State = 490;
					Match(WS);
					}
				}

				State = 493;
				((VsUnaryOperationContext)_localctx).valUnary = valueStmt(19);
				}
				break;
			case NOT:
				{
				_localctx = new VsUnaryOperationContext(_localctx);
				Context = _localctx;
				_prevctx = _localctx;
				State = 494;
				((VsUnaryOperationContext)_localctx).@operator = Match(NOT);
				State = 507;
				ErrorHandler.Sync(this);
				switch (TokenStream.LA(1)) {
				case WS:
					{
					State = 495;
					Match(WS);
					State = 496;
					((VsUnaryOperationContext)_localctx).valUnary = valueStmt(0);
					}
					break;
				case LPAREN:
					{
					State = 497;
					Match(LPAREN);
					State = 499;
					ErrorHandler.Sync(this);
					_la = TokenStream.LA(1);
					if (_la==WS) {
						{
						State = 498;
						Match(WS);
						}
					}

					State = 501;
					((VsUnaryOperationContext)_localctx).valUnary = valueStmt(0);
					State = 503;
					ErrorHandler.Sync(this);
					_la = TokenStream.LA(1);
					if (_la==WS) {
						{
						State = 502;
						Match(WS);
						}
					}

					State = 505;
					Match(RPAREN);
					}
					break;
				default:
					throw new NoViableAltException(this);
				}
				}
				break;
			default:
				throw new NoViableAltException(this);
			}
			Context.Stop = TokenStream.LT(-1);
			State = 658;
			ErrorHandler.Sync(this);
			_alt = Interpreter.AdaptivePredict(TokenStream,114,Context);
			while ( _alt!=2 && _alt!=global::Antlr4.Runtime.Atn.ATN.INVALID_ALT_NUMBER ) {
				if ( _alt==1 ) {
					if ( ParseListeners!=null )
						TriggerExitRuleEvent();
					_prevctx = _localctx;
					{
					State = 656;
					ErrorHandler.Sync(this);
					switch ( Interpreter.AdaptivePredict(TokenStream,113,Context) ) {
					case 1:
						{
						_localctx = new VsDualOperationContext(new ValueStmtContext(_parentctx, _parentState));
						((VsDualOperationContext)_localctx).left = _prevctx;
						PushNewRecursionContext(_localctx, _startState, RULE_valueStmt);
						State = 511;
						if (!(Precpred(Context, 18))) throw new FailedPredicateException(this, "Precpred(Context, 18)");
						State = 513;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 512;
							Match(WS);
							}
						}

						State = 515;
						((VsDualOperationContext)_localctx).@operator = Match(POW);
						State = 517;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 516;
							Match(WS);
							}
						}

						State = 519;
						((VsDualOperationContext)_localctx).right = valueStmt(19);
						}
						break;
					case 2:
						{
						_localctx = new VsDualOperationContext(new ValueStmtContext(_parentctx, _parentState));
						((VsDualOperationContext)_localctx).left = _prevctx;
						PushNewRecursionContext(_localctx, _startState, RULE_valueStmt);
						State = 520;
						if (!(Precpred(Context, 17))) throw new FailedPredicateException(this, "Precpred(Context, 17)");
						State = 522;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 521;
							Match(WS);
							}
						}

						State = 524;
						((VsDualOperationContext)_localctx).@operator = TokenStream.LT(1);
						_la = TokenStream.LA(1);
						if ( !((((_la) & ~0x3f) == 0 && ((1L << _la) & 281543704576000L) != 0)) ) {
							((VsDualOperationContext)_localctx).@operator = ErrorHandler.RecoverInline(this);
						}
						else {
							ErrorHandler.ReportMatch(this);
						    Consume();
						}
						State = 526;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 525;
							Match(WS);
							}
						}

						State = 528;
						((VsDualOperationContext)_localctx).right = valueStmt(18);
						}
						break;
					case 3:
						{
						_localctx = new VsDualOperationContext(new ValueStmtContext(_parentctx, _parentState));
						((VsDualOperationContext)_localctx).left = _prevctx;
						PushNewRecursionContext(_localctx, _startState, RULE_valueStmt);
						State = 529;
						if (!(Precpred(Context, 16))) throw new FailedPredicateException(this, "Precpred(Context, 16)");
						State = 531;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 530;
							Match(WS);
							}
						}

						State = 533;
						((VsDualOperationContext)_localctx).@operator = TokenStream.LT(1);
						_la = TokenStream.LA(1);
						if ( !(_la==MINUS || _la==PLUS) ) {
							((VsDualOperationContext)_localctx).@operator = ErrorHandler.RecoverInline(this);
						}
						else {
							ErrorHandler.ReportMatch(this);
						    Consume();
						}
						State = 535;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 534;
							Match(WS);
							}
						}

						State = 537;
						((VsDualOperationContext)_localctx).right = valueStmt(17);
						}
						break;
					case 4:
						{
						_localctx = new VsDualOperationContext(new ValueStmtContext(_parentctx, _parentState));
						((VsDualOperationContext)_localctx).left = _prevctx;
						PushNewRecursionContext(_localctx, _startState, RULE_valueStmt);
						State = 538;
						if (!(Precpred(Context, 15))) throw new FailedPredicateException(this, "Precpred(Context, 15)");
						State = 540;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 539;
							Match(WS);
							}
						}

						State = 542;
						((VsDualOperationContext)_localctx).@operator = Match(AMPERSAND);
						State = 544;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 543;
							Match(WS);
							}
						}

						State = 546;
						((VsDualOperationContext)_localctx).right = valueStmt(16);
						}
						break;
					case 5:
						{
						_localctx = new VsDualOperationContext(new ValueStmtContext(_parentctx, _parentState));
						((VsDualOperationContext)_localctx).left = _prevctx;
						PushNewRecursionContext(_localctx, _startState, RULE_valueStmt);
						State = 547;
						if (!(Precpred(Context, 14))) throw new FailedPredicateException(this, "Precpred(Context, 14)");
						State = 549;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 548;
							Match(WS);
							}
						}

						State = 551;
						((VsDualOperationContext)_localctx).@operator = Match(EQ);
						State = 553;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 552;
							Match(WS);
							}
						}

						State = 555;
						((VsDualOperationContext)_localctx).right = valueStmt(15);
						}
						break;
					case 6:
						{
						_localctx = new VsDualOperationContext(new ValueStmtContext(_parentctx, _parentState));
						((VsDualOperationContext)_localctx).left = _prevctx;
						PushNewRecursionContext(_localctx, _startState, RULE_valueStmt);
						State = 556;
						if (!(Precpred(Context, 13))) throw new FailedPredicateException(this, "Precpred(Context, 13)");
						State = 558;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 557;
							Match(WS);
							}
						}

						State = 560;
						((VsDualOperationContext)_localctx).@operator = Match(NEQ);
						State = 562;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 561;
							Match(WS);
							}
						}

						State = 564;
						((VsDualOperationContext)_localctx).right = valueStmt(14);
						}
						break;
					case 7:
						{
						_localctx = new VsDualOperationContext(new ValueStmtContext(_parentctx, _parentState));
						((VsDualOperationContext)_localctx).left = _prevctx;
						PushNewRecursionContext(_localctx, _startState, RULE_valueStmt);
						State = 565;
						if (!(Precpred(Context, 12))) throw new FailedPredicateException(this, "Precpred(Context, 12)");
						State = 567;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 566;
							Match(WS);
							}
						}

						State = 569;
						((VsDualOperationContext)_localctx).@operator = Match(LT);
						State = 571;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 570;
							Match(WS);
							}
						}

						State = 573;
						((VsDualOperationContext)_localctx).right = valueStmt(13);
						}
						break;
					case 8:
						{
						_localctx = new VsDualOperationContext(new ValueStmtContext(_parentctx, _parentState));
						((VsDualOperationContext)_localctx).left = _prevctx;
						PushNewRecursionContext(_localctx, _startState, RULE_valueStmt);
						State = 574;
						if (!(Precpred(Context, 11))) throw new FailedPredicateException(this, "Precpred(Context, 11)");
						State = 576;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 575;
							Match(WS);
							}
						}

						State = 578;
						((VsDualOperationContext)_localctx).@operator = Match(GT);
						State = 580;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 579;
							Match(WS);
							}
						}

						State = 582;
						((VsDualOperationContext)_localctx).right = valueStmt(12);
						}
						break;
					case 9:
						{
						_localctx = new VsDualOperationContext(new ValueStmtContext(_parentctx, _parentState));
						((VsDualOperationContext)_localctx).left = _prevctx;
						PushNewRecursionContext(_localctx, _startState, RULE_valueStmt);
						State = 583;
						if (!(Precpred(Context, 10))) throw new FailedPredicateException(this, "Precpred(Context, 10)");
						State = 585;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 584;
							Match(WS);
							}
						}

						State = 587;
						((VsDualOperationContext)_localctx).@operator = Match(LEQ);
						State = 589;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 588;
							Match(WS);
							}
						}

						State = 591;
						((VsDualOperationContext)_localctx).right = valueStmt(11);
						}
						break;
					case 10:
						{
						_localctx = new VsDualOperationContext(new ValueStmtContext(_parentctx, _parentState));
						((VsDualOperationContext)_localctx).left = _prevctx;
						PushNewRecursionContext(_localctx, _startState, RULE_valueStmt);
						State = 592;
						if (!(Precpred(Context, 9))) throw new FailedPredicateException(this, "Precpred(Context, 9)");
						State = 594;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 593;
							Match(WS);
							}
						}

						State = 596;
						((VsDualOperationContext)_localctx).@operator = Match(GEQ);
						State = 598;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 597;
							Match(WS);
							}
						}

						State = 600;
						((VsDualOperationContext)_localctx).right = valueStmt(10);
						}
						break;
					case 11:
						{
						_localctx = new VsDualOperationContext(new ValueStmtContext(_parentctx, _parentState));
						((VsDualOperationContext)_localctx).left = _prevctx;
						PushNewRecursionContext(_localctx, _startState, RULE_valueStmt);
						State = 601;
						if (!(Precpred(Context, 8))) throw new FailedPredicateException(this, "Precpred(Context, 8)");
						State = 603;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 602;
							Match(WS);
							}
						}

						State = 605;
						((VsDualOperationContext)_localctx).@operator = Match(AND);
						State = 607;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 606;
							Match(WS);
							}
						}

						State = 609;
						((VsDualOperationContext)_localctx).right = valueStmt(9);
						}
						break;
					case 12:
						{
						_localctx = new VsDualOperationContext(new ValueStmtContext(_parentctx, _parentState));
						((VsDualOperationContext)_localctx).left = _prevctx;
						PushNewRecursionContext(_localctx, _startState, RULE_valueStmt);
						State = 610;
						if (!(Precpred(Context, 7))) throw new FailedPredicateException(this, "Precpred(Context, 7)");
						State = 612;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 611;
							Match(WS);
							}
						}

						State = 614;
						((VsDualOperationContext)_localctx).@operator = Match(OR);
						State = 616;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 615;
							Match(WS);
							}
						}

						State = 618;
						((VsDualOperationContext)_localctx).right = valueStmt(8);
						}
						break;
					case 13:
						{
						_localctx = new VsDualOperationContext(new ValueStmtContext(_parentctx, _parentState));
						((VsDualOperationContext)_localctx).left = _prevctx;
						PushNewRecursionContext(_localctx, _startState, RULE_valueStmt);
						State = 619;
						if (!(Precpred(Context, 6))) throw new FailedPredicateException(this, "Precpred(Context, 6)");
						State = 621;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 620;
							Match(WS);
							}
						}

						State = 623;
						((VsDualOperationContext)_localctx).@operator = Match(XOR);
						State = 625;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 624;
							Match(WS);
							}
						}

						State = 627;
						((VsDualOperationContext)_localctx).right = valueStmt(7);
						}
						break;
					case 14:
						{
						_localctx = new VsDualOperationContext(new ValueStmtContext(_parentctx, _parentState));
						((VsDualOperationContext)_localctx).left = _prevctx;
						PushNewRecursionContext(_localctx, _startState, RULE_valueStmt);
						State = 628;
						if (!(Precpred(Context, 5))) throw new FailedPredicateException(this, "Precpred(Context, 5)");
						State = 630;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 629;
							Match(WS);
							}
						}

						State = 632;
						((VsDualOperationContext)_localctx).@operator = Match(EQV);
						State = 634;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 633;
							Match(WS);
							}
						}

						State = 636;
						((VsDualOperationContext)_localctx).right = valueStmt(6);
						}
						break;
					case 15:
						{
						_localctx = new VsDualOperationContext(new ValueStmtContext(_parentctx, _parentState));
						((VsDualOperationContext)_localctx).left = _prevctx;
						PushNewRecursionContext(_localctx, _startState, RULE_valueStmt);
						State = 637;
						if (!(Precpred(Context, 4))) throw new FailedPredicateException(this, "Precpred(Context, 4)");
						State = 639;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 638;
							Match(WS);
							}
						}

						State = 641;
						((VsDualOperationContext)_localctx).@operator = Match(IMP);
						State = 643;
						ErrorHandler.Sync(this);
						_la = TokenStream.LA(1);
						if (_la==WS) {
							{
							State = 642;
							Match(WS);
							}
						}

						State = 645;
						((VsDualOperationContext)_localctx).right = valueStmt(5);
						}
						break;
					case 16:
						{
						_localctx = new VsDualOperationContext(new ValueStmtContext(_parentctx, _parentState));
						((VsDualOperationContext)_localctx).left = _prevctx;
						PushNewRecursionContext(_localctx, _startState, RULE_valueStmt);
						State = 646;
						if (!(Precpred(Context, 3))) throw new FailedPredicateException(this, "Precpred(Context, 3)");
						State = 647;
						Match(WS);
						State = 648;
						((VsDualOperationContext)_localctx).@operator = Match(LIKE);
						State = 649;
						Match(WS);
						State = 650;
						((VsDualOperationContext)_localctx).right = valueStmt(4);
						}
						break;
					case 17:
						{
						_localctx = new VsDualOperationContext(new ValueStmtContext(_parentctx, _parentState));
						((VsDualOperationContext)_localctx).left = _prevctx;
						PushNewRecursionContext(_localctx, _startState, RULE_valueStmt);
						State = 651;
						if (!(Precpred(Context, 2))) throw new FailedPredicateException(this, "Precpred(Context, 2)");
						State = 652;
						Match(WS);
						State = 653;
						((VsDualOperationContext)_localctx).@operator = Match(IS);
						State = 654;
						Match(WS);
						State = 655;
						((VsDualOperationContext)_localctx).right = valueStmt(3);
						}
						break;
					}
					} 
				}
				State = 660;
				ErrorHandler.Sync(this);
				_alt = Interpreter.AdaptivePredict(TokenStream,114,Context);
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			UnrollRecursionContexts(_parentctx);
		}
		return _localctx;
	}

	public partial class DelimitedLiteralContext : ParserRuleContext {
		public DelimitedLiteralContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_delimitedLiteral; } }
	 
		public DelimitedLiteralContext() { }
		public virtual void CopyFrom(DelimitedLiteralContext context) {
			base.CopyFrom(context);
		}
	}
	public partial class LtStringContext : DelimitedLiteralContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode STRINGLITERAL() { return GetToken(VBPreprocessorsParser.STRINGLITERAL, 0); }
		public LtStringContext(DelimitedLiteralContext context) { CopyFrom(context); }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterLtString(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitLtString(this);
		}
	}
	public partial class LtOctalContext : DelimitedLiteralContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode OCTALLITERAL() { return GetToken(VBPreprocessorsParser.OCTALLITERAL, 0); }
		public LtOctalContext(DelimitedLiteralContext context) { CopyFrom(context); }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterLtOctal(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitLtOctal(this);
		}
	}
	public partial class LtDateContext : DelimitedLiteralContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode DATELITERAL() { return GetToken(VBPreprocessorsParser.DATELITERAL, 0); }
		public LtDateContext(DelimitedLiteralContext context) { CopyFrom(context); }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterLtDate(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitLtDate(this);
		}
	}
	public partial class LtColorContext : DelimitedLiteralContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode HEXLITERAL() { return GetToken(VBPreprocessorsParser.HEXLITERAL, 0); }
		public LtColorContext(DelimitedLiteralContext context) { CopyFrom(context); }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterLtColor(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitLtColor(this);
		}
	}

	[RuleVersion(0)]
	public DelimitedLiteralContext delimitedLiteral() {
		DelimitedLiteralContext _localctx = new DelimitedLiteralContext(Context, State);
		EnterRule(_localctx, 66, RULE_delimitedLiteral);
		try {
			State = 665;
			ErrorHandler.Sync(this);
			switch (TokenStream.LA(1)) {
			case HEXLITERAL:
				_localctx = new LtColorContext(_localctx);
				EnterOuterAlt(_localctx, 1);
				{
				State = 661;
				Match(HEXLITERAL);
				}
				break;
			case OCTALLITERAL:
				_localctx = new LtOctalContext(_localctx);
				EnterOuterAlt(_localctx, 2);
				{
				State = 662;
				Match(OCTALLITERAL);
				}
				break;
			case DATELITERAL:
				_localctx = new LtDateContext(_localctx);
				EnterOuterAlt(_localctx, 3);
				{
				State = 663;
				Match(DATELITERAL);
				}
				break;
			case STRINGLITERAL:
				_localctx = new LtStringContext(_localctx);
				EnterOuterAlt(_localctx, 4);
				{
				State = 664;
				Match(STRINGLITERAL);
				}
				break;
			default:
				throw new NoViableAltException(this);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class LiteralContext : ParserRuleContext {
		public LiteralContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_literal; } }
	 
		public LiteralContext() { }
		public virtual void CopyFrom(LiteralContext context) {
			base.CopyFrom(context);
		}
	}
	public partial class LtIntegerContext : LiteralContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode INTEGERLITERAL() { return GetToken(VBPreprocessorsParser.INTEGERLITERAL, 0); }
		public LtIntegerContext(LiteralContext context) { CopyFrom(context); }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterLtInteger(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitLtInteger(this);
		}
	}
	public partial class LtDoubleContext : LiteralContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode DOUBLELITERAL() { return GetToken(VBPreprocessorsParser.DOUBLELITERAL, 0); }
		public LtDoubleContext(LiteralContext context) { CopyFrom(context); }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterLtDouble(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitLtDouble(this);
		}
	}
	public partial class LtDelimitedContext : LiteralContext {
		[System.Diagnostics.DebuggerNonUserCode] public DelimitedLiteralContext delimitedLiteral() {
			return GetRuleContext<DelimitedLiteralContext>(0);
		}
		public LtDelimitedContext(LiteralContext context) { CopyFrom(context); }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterLtDelimited(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitLtDelimited(this);
		}
	}
	public partial class LtBooleanContext : LiteralContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode TRUE() { return GetToken(VBPreprocessorsParser.TRUE, 0); }
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode FALSE() { return GetToken(VBPreprocessorsParser.FALSE, 0); }
		public LtBooleanContext(LiteralContext context) { CopyFrom(context); }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterLtBoolean(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitLtBoolean(this);
		}
	}

	[RuleVersion(0)]
	public LiteralContext literal() {
		LiteralContext _localctx = new LiteralContext(Context, State);
		EnterRule(_localctx, 68, RULE_literal);
		try {
			State = 672;
			ErrorHandler.Sync(this);
			switch (TokenStream.LA(1)) {
			case DOUBLELITERAL:
				_localctx = new LtDoubleContext(_localctx);
				EnterOuterAlt(_localctx, 1);
				{
				State = 667;
				Match(DOUBLELITERAL);
				}
				break;
			case STRINGLITERAL:
			case DATELITERAL:
			case HEXLITERAL:
			case OCTALLITERAL:
				_localctx = new LtDelimitedContext(_localctx);
				EnterOuterAlt(_localctx, 2);
				{
				State = 668;
				delimitedLiteral();
				}
				break;
			case INTEGERLITERAL:
				_localctx = new LtIntegerContext(_localctx);
				EnterOuterAlt(_localctx, 3);
				{
				State = 669;
				Match(INTEGERLITERAL);
				}
				break;
			case TRUE:
				_localctx = new LtBooleanContext(_localctx);
				EnterOuterAlt(_localctx, 4);
				{
				State = 670;
				Match(TRUE);
				}
				break;
			case FALSE:
				_localctx = new LtBooleanContext(_localctx);
				EnterOuterAlt(_localctx, 5);
				{
				State = 671;
				Match(FALSE);
				}
				break;
			default:
				throw new NoViableAltException(this);
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public partial class AnytokenContext : ParserRuleContext {
		[System.Diagnostics.DebuggerNonUserCode] public ITerminalNode NEWLINE() { return GetToken(VBPreprocessorsParser.NEWLINE, 0); }
		public AnytokenContext(ParserRuleContext parent, int invokingState)
			: base(parent, invokingState)
		{
		}
		public override int RuleIndex { get { return RULE_anytoken; } }
		[System.Diagnostics.DebuggerNonUserCode]
		public override void EnterRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.EnterAnytoken(this);
		}
		[System.Diagnostics.DebuggerNonUserCode]
		public override void ExitRule(IParseTreeListener listener) {
			IVBPreprocessorsListener typedListener = listener as IVBPreprocessorsListener;
			if (typedListener != null) typedListener.ExitAnytoken(this);
		}
	}

	[RuleVersion(0)]
	public AnytokenContext anytoken() {
		AnytokenContext _localctx = new AnytokenContext(Context, State);
		EnterRule(_localctx, 70, RULE_anytoken);
		int _la;
		try {
			EnterOuterAlt(_localctx, 1);
			{
			State = 674;
			_la = TokenStream.LA(1);
			if ( _la <= 0 || (_la==NEWLINE) ) {
			ErrorHandler.RecoverInline(this);
			}
			else {
				ErrorHandler.ReportMatch(this);
			    Consume();
			}
			}
		}
		catch (RecognitionException re) {
			_localctx.exception = re;
			ErrorHandler.ReportError(this, re);
			ErrorHandler.Recover(this, re);
		}
		finally {
			ExitRule();
		}
		return _localctx;
	}

	public override bool Sempred(RuleContext _localctx, int ruleIndex, int predIndex) {
		switch (ruleIndex) {
		case 32: return valueStmt_sempred((ValueStmtContext)_localctx, predIndex);
		}
		return true;
	}
	private bool valueStmt_sempred(ValueStmtContext _localctx, int predIndex) {
		switch (predIndex) {
		case 0: return Precpred(Context, 18);
		case 1: return Precpred(Context, 17);
		case 2: return Precpred(Context, 16);
		case 3: return Precpred(Context, 15);
		case 4: return Precpred(Context, 14);
		case 5: return Precpred(Context, 13);
		case 6: return Precpred(Context, 12);
		case 7: return Precpred(Context, 11);
		case 8: return Precpred(Context, 10);
		case 9: return Precpred(Context, 9);
		case 10: return Precpred(Context, 8);
		case 11: return Precpred(Context, 7);
		case 12: return Precpred(Context, 6);
		case 13: return Precpred(Context, 5);
		case 14: return Precpred(Context, 4);
		case 15: return Precpred(Context, 3);
		case 16: return Precpred(Context, 2);
		}
		return true;
	}

	private static int[] _serializedATN = {
		4,1,72,677,2,0,7,0,2,1,7,1,2,2,7,2,2,3,7,3,2,4,7,4,2,5,7,5,2,6,7,6,2,7,
		7,7,2,8,7,8,2,9,7,9,2,10,7,10,2,11,7,11,2,12,7,12,2,13,7,13,2,14,7,14,
		2,15,7,15,2,16,7,16,2,17,7,17,2,18,7,18,2,19,7,19,2,20,7,20,2,21,7,21,
		2,22,7,22,2,23,7,23,2,24,7,24,2,25,7,25,2,26,7,26,2,27,7,27,2,28,7,28,
		2,29,7,29,2,30,7,30,2,31,7,31,2,32,7,32,2,33,7,33,2,34,7,34,2,35,7,35,
		1,0,5,0,74,8,0,10,0,12,0,77,9,0,1,0,5,0,80,8,0,10,0,12,0,83,9,0,1,0,3,
		0,86,8,0,1,1,1,1,1,1,3,1,91,8,1,1,1,1,1,3,1,95,8,1,1,1,1,1,1,2,5,2,100,
		8,2,10,2,12,2,103,9,2,1,2,1,2,3,2,107,8,2,1,2,4,2,110,8,2,11,2,12,2,111,
		1,2,3,2,115,8,2,1,3,1,3,1,3,1,3,1,3,3,3,122,8,3,1,4,4,4,125,8,4,11,4,12,
		4,126,1,5,3,5,130,8,5,1,5,5,5,133,8,5,10,5,12,5,136,9,5,1,5,3,5,139,8,
		5,1,5,5,5,142,8,5,10,5,12,5,145,9,5,1,5,3,5,148,8,5,1,5,5,5,151,8,5,10,
		5,12,5,154,9,5,1,5,3,5,157,8,5,1,5,5,5,160,8,5,10,5,12,5,163,9,5,1,6,4,
		6,166,8,6,11,6,12,6,167,1,7,1,7,3,7,172,8,7,1,7,1,7,3,7,176,8,7,1,7,1,
		7,1,7,3,7,181,8,7,1,7,3,7,184,8,7,1,7,5,7,187,8,7,10,7,12,7,190,9,7,1,
		8,1,8,1,9,1,9,1,10,1,10,1,10,1,10,1,10,3,10,201,8,10,1,11,1,11,4,11,205,
		8,11,11,11,12,11,206,1,11,4,11,210,8,11,11,11,12,11,211,1,11,1,11,4,11,
		216,8,11,11,11,12,11,217,1,12,1,12,3,12,222,8,12,1,12,1,12,3,12,226,8,
		12,1,12,1,12,1,12,1,13,4,13,232,8,13,11,13,12,13,233,1,13,1,13,4,13,238,
		8,13,11,13,12,13,239,1,13,3,13,243,8,13,1,14,1,14,4,14,247,8,14,11,14,
		12,14,248,4,14,251,8,14,11,14,12,14,252,1,15,1,15,5,15,257,8,15,10,15,
		12,15,260,9,15,1,15,3,15,263,8,15,1,15,1,15,1,16,1,16,1,16,1,16,1,16,1,
		16,1,17,1,17,3,17,275,8,17,1,17,5,17,278,8,17,10,17,12,17,281,9,17,1,17,
		4,17,284,8,17,11,17,12,17,285,1,17,5,17,289,8,17,10,17,12,17,292,9,17,
		1,18,1,18,1,18,1,18,1,18,1,18,3,18,300,8,18,1,18,5,18,303,8,18,10,18,12,
		18,306,9,18,1,18,4,18,309,8,18,11,18,12,18,310,1,18,5,18,314,8,18,10,18,
		12,18,317,9,18,1,19,1,19,3,19,321,8,19,1,19,5,19,324,8,19,10,19,12,19,
		327,9,19,1,19,4,19,330,8,19,11,19,12,19,331,1,19,5,19,335,8,19,10,19,12,
		19,338,9,19,1,20,4,20,341,8,20,11,20,12,20,342,1,21,1,21,1,22,1,22,3,22,
		349,8,22,1,23,4,23,352,8,23,11,23,12,23,353,1,24,3,24,357,8,24,1,24,1,
		24,1,24,1,24,1,24,1,24,3,24,365,8,24,1,24,4,24,368,8,24,11,24,12,24,369,
		1,24,4,24,373,8,24,11,24,12,24,374,1,24,1,24,5,24,379,8,24,10,24,12,24,
		382,9,24,1,25,1,25,3,25,386,8,25,1,26,3,26,389,8,26,1,26,1,26,1,26,1,26,
		1,26,1,26,3,26,397,8,26,1,26,1,26,3,26,401,8,26,1,26,4,26,404,8,26,11,
		26,12,26,405,1,26,4,26,409,8,26,11,26,12,26,410,3,26,413,8,26,1,26,1,26,
		4,26,417,8,26,11,26,12,26,418,1,27,1,27,1,28,1,28,1,28,5,28,426,8,28,10,
		28,12,28,429,9,28,1,29,1,29,1,30,1,30,1,30,1,30,3,30,437,8,30,1,30,1,30,
		3,30,441,8,30,1,30,1,30,3,30,445,8,30,1,30,1,30,3,30,449,8,30,1,30,5,30,
		452,8,30,10,30,12,30,455,9,30,1,31,1,31,1,32,1,32,1,32,1,32,1,32,3,32,
		464,8,32,1,32,1,32,3,32,468,8,32,1,32,1,32,3,32,472,8,32,1,32,5,32,475,
		8,32,10,32,12,32,478,9,32,1,32,3,32,481,8,32,1,32,1,32,1,32,1,32,3,32,
		487,8,32,1,32,1,32,1,32,3,32,492,8,32,1,32,1,32,1,32,1,32,1,32,1,32,3,
		32,500,8,32,1,32,1,32,3,32,504,8,32,1,32,1,32,3,32,508,8,32,3,32,510,8,
		32,1,32,1,32,3,32,514,8,32,1,32,1,32,3,32,518,8,32,1,32,1,32,1,32,3,32,
		523,8,32,1,32,1,32,3,32,527,8,32,1,32,1,32,1,32,3,32,532,8,32,1,32,1,32,
		3,32,536,8,32,1,32,1,32,1,32,3,32,541,8,32,1,32,1,32,3,32,545,8,32,1,32,
		1,32,1,32,3,32,550,8,32,1,32,1,32,3,32,554,8,32,1,32,1,32,1,32,3,32,559,
		8,32,1,32,1,32,3,32,563,8,32,1,32,1,32,1,32,3,32,568,8,32,1,32,1,32,3,
		32,572,8,32,1,32,1,32,1,32,3,32,577,8,32,1,32,1,32,3,32,581,8,32,1,32,
		1,32,1,32,3,32,586,8,32,1,32,1,32,3,32,590,8,32,1,32,1,32,1,32,3,32,595,
		8,32,1,32,1,32,3,32,599,8,32,1,32,1,32,1,32,3,32,604,8,32,1,32,1,32,3,
		32,608,8,32,1,32,1,32,1,32,3,32,613,8,32,1,32,1,32,3,32,617,8,32,1,32,
		1,32,1,32,3,32,622,8,32,1,32,1,32,3,32,626,8,32,1,32,1,32,1,32,3,32,631,
		8,32,1,32,1,32,3,32,635,8,32,1,32,1,32,1,32,3,32,640,8,32,1,32,1,32,3,
		32,644,8,32,1,32,1,32,1,32,1,32,1,32,1,32,1,32,1,32,1,32,1,32,1,32,5,32,
		657,8,32,10,32,12,32,660,9,32,1,33,1,33,1,33,1,33,3,33,666,8,33,1,34,1,
		34,1,34,1,34,1,34,3,34,673,8,34,1,35,1,35,1,35,0,1,64,36,0,2,4,6,8,10,
		12,14,16,18,20,22,24,26,28,30,32,34,36,38,40,42,44,46,48,50,52,54,56,58,
		60,62,64,66,68,70,0,4,4,0,1,1,19,22,65,65,67,67,3,0,23,23,36,36,48,48,
		2,0,47,47,50,50,1,0,65,65,784,0,75,1,0,0,0,2,87,1,0,0,0,4,101,1,0,0,0,
		6,121,1,0,0,0,8,124,1,0,0,0,10,129,1,0,0,0,12,165,1,0,0,0,14,169,1,0,0,
		0,16,191,1,0,0,0,18,193,1,0,0,0,20,195,1,0,0,0,22,202,1,0,0,0,24,219,1,
		0,0,0,26,242,1,0,0,0,28,250,1,0,0,0,30,254,1,0,0,0,32,266,1,0,0,0,34,272,
		1,0,0,0,36,293,1,0,0,0,38,318,1,0,0,0,40,340,1,0,0,0,42,344,1,0,0,0,44,
		346,1,0,0,0,46,351,1,0,0,0,48,356,1,0,0,0,50,385,1,0,0,0,52,388,1,0,0,
		0,54,420,1,0,0,0,56,422,1,0,0,0,58,430,1,0,0,0,60,432,1,0,0,0,62,456,1,
		0,0,0,64,509,1,0,0,0,66,665,1,0,0,0,68,672,1,0,0,0,70,674,1,0,0,0,72,74,
		5,65,0,0,73,72,1,0,0,0,74,77,1,0,0,0,75,73,1,0,0,0,75,76,1,0,0,0,76,81,
		1,0,0,0,77,75,1,0,0,0,78,80,3,4,2,0,79,78,1,0,0,0,80,83,1,0,0,0,81,79,
		1,0,0,0,81,82,1,0,0,0,82,85,1,0,0,0,83,81,1,0,0,0,84,86,5,0,0,1,85,84,
		1,0,0,0,85,86,1,0,0,0,86,1,1,0,0,0,87,88,5,1,0,0,88,90,5,66,0,0,89,91,
		5,69,0,0,90,89,1,0,0,0,90,91,1,0,0,0,91,92,1,0,0,0,92,94,5,38,0,0,93,95,
		5,69,0,0,94,93,1,0,0,0,94,95,1,0,0,0,95,96,1,0,0,0,96,97,3,64,32,0,97,
		3,1,0,0,0,98,100,3,42,21,0,99,98,1,0,0,0,100,103,1,0,0,0,101,99,1,0,0,
		0,101,102,1,0,0,0,102,104,1,0,0,0,103,101,1,0,0,0,104,106,3,6,3,0,105,
		107,3,40,20,0,106,105,1,0,0,0,106,107,1,0,0,0,107,114,1,0,0,0,108,110,
		5,65,0,0,109,108,1,0,0,0,110,111,1,0,0,0,111,109,1,0,0,0,111,112,1,0,0,
		0,112,115,1,0,0,0,113,115,5,0,0,1,114,109,1,0,0,0,114,113,1,0,0,0,115,
		5,1,0,0,0,116,122,3,30,15,0,117,122,3,2,1,0,118,122,3,40,20,0,119,122,
		3,8,4,0,120,122,3,42,21,0,121,116,1,0,0,0,121,117,1,0,0,0,121,118,1,0,
		0,0,121,119,1,0,0,0,121,120,1,0,0,0,122,7,1,0,0,0,123,125,3,44,22,0,124,
		123,1,0,0,0,125,126,1,0,0,0,126,124,1,0,0,0,126,127,1,0,0,0,127,9,1,0,
		0,0,128,130,3,12,6,0,129,128,1,0,0,0,129,130,1,0,0,0,130,134,1,0,0,0,131,
		133,5,65,0,0,132,131,1,0,0,0,133,136,1,0,0,0,134,132,1,0,0,0,134,135,1,
		0,0,0,135,138,1,0,0,0,136,134,1,0,0,0,137,139,3,48,24,0,138,137,1,0,0,
		0,138,139,1,0,0,0,139,143,1,0,0,0,140,142,5,65,0,0,141,140,1,0,0,0,142,
		145,1,0,0,0,143,141,1,0,0,0,143,144,1,0,0,0,144,147,1,0,0,0,145,143,1,
		0,0,0,146,148,3,22,11,0,147,146,1,0,0,0,147,148,1,0,0,0,148,152,1,0,0,
		0,149,151,5,65,0,0,150,149,1,0,0,0,151,154,1,0,0,0,152,150,1,0,0,0,152,
		153,1,0,0,0,153,156,1,0,0,0,154,152,1,0,0,0,155,157,3,28,14,0,156,155,
		1,0,0,0,156,157,1,0,0,0,157,161,1,0,0,0,158,160,5,65,0,0,159,158,1,0,0,
		0,160,163,1,0,0,0,161,159,1,0,0,0,161,162,1,0,0,0,162,11,1,0,0,0,163,161,
		1,0,0,0,164,166,3,14,7,0,165,164,1,0,0,0,166,167,1,0,0,0,167,165,1,0,0,
		0,167,168,1,0,0,0,168,13,1,0,0,0,169,171,5,27,0,0,170,172,5,69,0,0,171,
		170,1,0,0,0,171,172,1,0,0,0,172,173,1,0,0,0,173,175,5,38,0,0,174,176,5,
		69,0,0,175,174,1,0,0,0,175,176,1,0,0,0,176,177,1,0,0,0,177,183,3,16,8,
		0,178,180,5,54,0,0,179,181,5,69,0,0,180,179,1,0,0,0,180,181,1,0,0,0,181,
		182,1,0,0,0,182,184,3,18,9,0,183,178,1,0,0,0,183,184,1,0,0,0,184,188,1,
		0,0,0,185,187,5,65,0,0,186,185,1,0,0,0,187,190,1,0,0,0,188,186,1,0,0,0,
		188,189,1,0,0,0,189,15,1,0,0,0,190,188,1,0,0,0,191,192,5,57,0,0,192,17,
		1,0,0,0,193,194,5,57,0,0,194,19,1,0,0,0,195,196,5,31,0,0,196,197,5,69,
		0,0,197,200,5,61,0,0,198,199,5,69,0,0,199,201,5,6,0,0,200,198,1,0,0,0,
		200,201,1,0,0,0,201,21,1,0,0,0,202,204,5,4,0,0,203,205,5,65,0,0,204,203,
		1,0,0,0,205,206,1,0,0,0,206,204,1,0,0,0,206,207,1,0,0,0,207,209,1,0,0,
		0,208,210,3,24,12,0,209,208,1,0,0,0,210,211,1,0,0,0,211,209,1,0,0,0,211,
		212,1,0,0,0,212,213,1,0,0,0,213,215,5,11,0,0,214,216,5,65,0,0,215,214,
		1,0,0,0,216,217,1,0,0,0,217,215,1,0,0,0,217,218,1,0,0,0,218,23,1,0,0,0,
		219,221,3,26,13,0,220,222,5,69,0,0,221,220,1,0,0,0,221,222,1,0,0,0,222,
		223,1,0,0,0,223,225,5,38,0,0,224,226,5,69,0,0,225,224,1,0,0,0,225,226,
		1,0,0,0,226,227,1,0,0,0,227,228,3,68,34,0,228,229,5,65,0,0,229,25,1,0,
		0,0,230,232,5,66,0,0,231,230,1,0,0,0,232,233,1,0,0,0,233,231,1,0,0,0,233,
		234,1,0,0,0,234,243,1,0,0,0,235,237,5,55,0,0,236,238,5,66,0,0,237,236,
		1,0,0,0,238,239,1,0,0,0,239,237,1,0,0,0,239,240,1,0,0,0,240,241,1,0,0,
		0,241,243,5,56,0,0,242,231,1,0,0,0,242,235,1,0,0,0,243,27,1,0,0,0,244,
		246,3,60,30,0,245,247,5,65,0,0,246,245,1,0,0,0,247,248,1,0,0,0,248,246,
		1,0,0,0,248,249,1,0,0,0,249,251,1,0,0,0,250,244,1,0,0,0,251,252,1,0,0,
		0,252,250,1,0,0,0,252,253,1,0,0,0,253,29,1,0,0,0,254,258,3,34,17,0,255,
		257,3,36,18,0,256,255,1,0,0,0,257,260,1,0,0,0,258,256,1,0,0,0,258,259,
		1,0,0,0,259,262,1,0,0,0,260,258,1,0,0,0,261,263,3,38,19,0,262,261,1,0,
		0,0,262,263,1,0,0,0,263,264,1,0,0,0,264,265,5,22,0,0,265,31,1,0,0,0,266,
		267,5,19,0,0,267,268,5,69,0,0,268,269,3,62,31,0,269,270,5,69,0,0,270,271,
		5,29,0,0,271,33,1,0,0,0,272,274,3,32,16,0,273,275,5,69,0,0,274,273,1,0,
		0,0,274,275,1,0,0,0,275,279,1,0,0,0,276,278,3,40,20,0,277,276,1,0,0,0,
		278,281,1,0,0,0,279,277,1,0,0,0,279,280,1,0,0,0,280,283,1,0,0,0,281,279,
		1,0,0,0,282,284,5,65,0,0,283,282,1,0,0,0,284,285,1,0,0,0,285,283,1,0,0,
		0,285,286,1,0,0,0,286,290,1,0,0,0,287,289,3,4,2,0,288,287,1,0,0,0,289,
		292,1,0,0,0,290,288,1,0,0,0,290,291,1,0,0,0,291,35,1,0,0,0,292,290,1,0,
		0,0,293,294,5,20,0,0,294,295,5,69,0,0,295,296,3,62,31,0,296,297,5,69,0,
		0,297,299,5,29,0,0,298,300,5,69,0,0,299,298,1,0,0,0,299,300,1,0,0,0,300,
		304,1,0,0,0,301,303,3,40,20,0,302,301,1,0,0,0,303,306,1,0,0,0,304,302,
		1,0,0,0,304,305,1,0,0,0,305,308,1,0,0,0,306,304,1,0,0,0,307,309,5,65,0,
		0,308,307,1,0,0,0,309,310,1,0,0,0,310,308,1,0,0,0,310,311,1,0,0,0,311,
		315,1,0,0,0,312,314,3,4,2,0,313,312,1,0,0,0,314,317,1,0,0,0,315,313,1,
		0,0,0,315,316,1,0,0,0,316,37,1,0,0,0,317,315,1,0,0,0,318,320,5,21,0,0,
		319,321,5,69,0,0,320,319,1,0,0,0,320,321,1,0,0,0,321,325,1,0,0,0,322,324,
		3,40,20,0,323,322,1,0,0,0,324,327,1,0,0,0,325,323,1,0,0,0,325,326,1,0,
		0,0,326,329,1,0,0,0,327,325,1,0,0,0,328,330,5,65,0,0,329,328,1,0,0,0,330,
		331,1,0,0,0,331,329,1,0,0,0,331,332,1,0,0,0,332,336,1,0,0,0,333,335,3,
		4,2,0,334,333,1,0,0,0,335,338,1,0,0,0,336,334,1,0,0,0,336,337,1,0,0,0,
		337,39,1,0,0,0,338,336,1,0,0,0,339,341,5,67,0,0,340,339,1,0,0,0,341,342,
		1,0,0,0,342,340,1,0,0,0,342,343,1,0,0,0,343,41,1,0,0,0,344,345,5,72,0,
		0,345,43,1,0,0,0,346,348,3,46,23,0,347,349,5,67,0,0,348,347,1,0,0,0,348,
		349,1,0,0,0,349,45,1,0,0,0,350,352,8,0,0,0,351,350,1,0,0,0,352,353,1,0,
		0,0,353,351,1,0,0,0,353,354,1,0,0,0,354,47,1,0,0,0,355,357,5,69,0,0,356,
		355,1,0,0,0,356,357,1,0,0,0,357,358,1,0,0,0,358,359,5,4,0,0,359,360,5,
		69,0,0,360,361,3,54,27,0,361,362,5,69,0,0,362,364,3,58,29,0,363,365,5,
		69,0,0,364,363,1,0,0,0,364,365,1,0,0,0,365,367,1,0,0,0,366,368,5,65,0,
		0,367,366,1,0,0,0,368,369,1,0,0,0,369,367,1,0,0,0,369,370,1,0,0,0,370,
		372,1,0,0,0,371,373,3,50,25,0,372,371,1,0,0,0,373,374,1,0,0,0,374,372,
		1,0,0,0,374,375,1,0,0,0,375,376,1,0,0,0,376,380,5,11,0,0,377,379,5,65,
		0,0,378,377,1,0,0,0,379,382,1,0,0,0,380,378,1,0,0,0,380,381,1,0,0,0,381,
		49,1,0,0,0,382,380,1,0,0,0,383,386,3,52,26,0,384,386,3,48,24,0,385,383,
		1,0,0,0,385,384,1,0,0,0,386,51,1,0,0,0,387,389,5,69,0,0,388,387,1,0,0,
		0,388,389,1,0,0,0,389,390,1,0,0,0,390,391,5,5,0,0,391,392,5,69,0,0,392,
		396,3,26,13,0,393,394,5,45,0,0,394,395,5,60,0,0,395,397,5,53,0,0,396,393,
		1,0,0,0,396,397,1,0,0,0,397,400,1,0,0,0,398,399,5,69,0,0,399,401,5,64,
		0,0,400,398,1,0,0,0,400,401,1,0,0,0,401,403,1,0,0,0,402,404,5,65,0,0,403,
		402,1,0,0,0,404,405,1,0,0,0,405,403,1,0,0,0,405,406,1,0,0,0,406,412,1,
		0,0,0,407,409,3,50,25,0,408,407,1,0,0,0,409,410,1,0,0,0,410,408,1,0,0,
		0,410,411,1,0,0,0,411,413,1,0,0,0,412,408,1,0,0,0,412,413,1,0,0,0,413,
		414,1,0,0,0,414,416,5,12,0,0,415,417,5,65,0,0,416,415,1,0,0,0,417,418,
		1,0,0,0,418,416,1,0,0,0,418,419,1,0,0,0,419,53,1,0,0,0,420,421,3,56,28,
		0,421,55,1,0,0,0,422,427,3,26,13,0,423,424,5,37,0,0,424,426,3,26,13,0,
		425,423,1,0,0,0,426,429,1,0,0,0,427,425,1,0,0,0,427,428,1,0,0,0,428,57,
		1,0,0,0,429,427,1,0,0,0,430,431,3,26,13,0,431,59,1,0,0,0,432,433,5,3,0,
		0,433,434,5,69,0,0,434,436,3,26,13,0,435,437,5,69,0,0,436,435,1,0,0,0,
		436,437,1,0,0,0,437,438,1,0,0,0,438,440,5,38,0,0,439,441,5,69,0,0,440,
		439,1,0,0,0,440,441,1,0,0,0,441,442,1,0,0,0,442,453,3,68,34,0,443,445,
		5,69,0,0,444,443,1,0,0,0,444,445,1,0,0,0,445,446,1,0,0,0,446,448,5,35,
		0,0,447,449,5,69,0,0,448,447,1,0,0,0,448,449,1,0,0,0,449,450,1,0,0,0,450,
		452,3,68,34,0,451,444,1,0,0,0,452,455,1,0,0,0,453,451,1,0,0,0,453,454,
		1,0,0,0,454,61,1,0,0,0,455,453,1,0,0,0,456,457,3,64,32,0,457,63,1,0,0,
		0,458,459,6,32,-1,0,459,510,3,68,34,0,460,510,3,26,13,0,461,463,5,45,0,
		0,462,464,5,69,0,0,463,462,1,0,0,0,463,464,1,0,0,0,464,465,1,0,0,0,465,
		476,3,64,32,0,466,468,5,69,0,0,467,466,1,0,0,0,467,468,1,0,0,0,468,469,
		1,0,0,0,469,471,5,35,0,0,470,472,5,69,0,0,471,470,1,0,0,0,471,472,1,0,
		0,0,472,473,1,0,0,0,473,475,3,64,32,0,474,467,1,0,0,0,475,478,1,0,0,0,
		476,474,1,0,0,0,476,477,1,0,0,0,477,480,1,0,0,0,478,476,1,0,0,0,479,481,
		5,69,0,0,480,479,1,0,0,0,480,481,1,0,0,0,481,482,1,0,0,0,482,483,5,53,
		0,0,483,510,1,0,0,0,484,486,5,50,0,0,485,487,5,69,0,0,486,485,1,0,0,0,
		486,487,1,0,0,0,487,488,1,0,0,0,488,510,3,64,32,20,489,491,5,47,0,0,490,
		492,5,69,0,0,491,490,1,0,0,0,491,492,1,0,0,0,492,493,1,0,0,0,493,510,3,
		64,32,19,494,507,5,24,0,0,495,496,5,69,0,0,496,508,3,64,32,0,497,499,5,
		45,0,0,498,500,5,69,0,0,499,498,1,0,0,0,499,500,1,0,0,0,500,501,1,0,0,
		0,501,503,3,64,32,0,502,504,5,69,0,0,503,502,1,0,0,0,503,504,1,0,0,0,504,
		505,1,0,0,0,505,506,5,53,0,0,506,508,1,0,0,0,507,495,1,0,0,0,507,497,1,
		0,0,0,508,510,1,0,0,0,509,458,1,0,0,0,509,460,1,0,0,0,509,461,1,0,0,0,
		509,484,1,0,0,0,509,489,1,0,0,0,509,494,1,0,0,0,510,658,1,0,0,0,511,513,
		10,18,0,0,512,514,5,69,0,0,513,512,1,0,0,0,513,514,1,0,0,0,514,515,1,0,
		0,0,515,517,5,51,0,0,516,518,5,69,0,0,517,516,1,0,0,0,517,518,1,0,0,0,
		518,519,1,0,0,0,519,657,3,64,32,19,520,522,10,17,0,0,521,523,5,69,0,0,
		522,521,1,0,0,0,522,523,1,0,0,0,523,524,1,0,0,0,524,526,7,1,0,0,525,527,
		5,69,0,0,526,525,1,0,0,0,526,527,1,0,0,0,527,528,1,0,0,0,528,657,3,64,
		32,18,529,531,10,16,0,0,530,532,5,69,0,0,531,530,1,0,0,0,531,532,1,0,0,
		0,532,533,1,0,0,0,533,535,7,2,0,0,534,536,5,69,0,0,535,534,1,0,0,0,535,
		536,1,0,0,0,536,537,1,0,0,0,537,657,3,64,32,17,538,540,10,15,0,0,539,541,
		5,69,0,0,540,539,1,0,0,0,540,541,1,0,0,0,541,542,1,0,0,0,542,544,5,33,
		0,0,543,545,5,69,0,0,544,543,1,0,0,0,544,545,1,0,0,0,545,546,1,0,0,0,546,
		657,3,64,32,16,547,549,10,14,0,0,548,550,5,69,0,0,549,548,1,0,0,0,549,
		550,1,0,0,0,550,551,1,0,0,0,551,553,5,38,0,0,552,554,5,69,0,0,553,552,
		1,0,0,0,553,554,1,0,0,0,554,555,1,0,0,0,555,657,3,64,32,15,556,558,10,
		13,0,0,557,559,5,69,0,0,558,557,1,0,0,0,558,559,1,0,0,0,559,560,1,0,0,
		0,560,562,5,49,0,0,561,563,5,69,0,0,562,561,1,0,0,0,562,563,1,0,0,0,563,
		564,1,0,0,0,564,657,3,64,32,14,565,567,10,12,0,0,566,568,5,69,0,0,567,
		566,1,0,0,0,567,568,1,0,0,0,568,569,1,0,0,0,569,571,5,46,0,0,570,572,5,
		69,0,0,571,570,1,0,0,0,571,572,1,0,0,0,572,573,1,0,0,0,573,657,3,64,32,
		13,574,576,10,11,0,0,575,577,5,69,0,0,576,575,1,0,0,0,576,577,1,0,0,0,
		577,578,1,0,0,0,578,580,5,41,0,0,579,581,5,69,0,0,580,579,1,0,0,0,580,
		581,1,0,0,0,581,582,1,0,0,0,582,657,3,64,32,12,583,585,10,10,0,0,584,586,
		5,69,0,0,585,584,1,0,0,0,585,586,1,0,0,0,586,587,1,0,0,0,587,589,5,43,
		0,0,588,590,5,69,0,0,589,588,1,0,0,0,589,590,1,0,0,0,590,591,1,0,0,0,591,
		657,3,64,32,11,592,594,10,9,0,0,593,595,5,69,0,0,594,593,1,0,0,0,594,595,
		1,0,0,0,595,596,1,0,0,0,596,598,5,40,0,0,597,599,5,69,0,0,598,597,1,0,
		0,0,598,599,1,0,0,0,599,600,1,0,0,0,600,657,3,64,32,10,601,603,10,8,0,
		0,602,604,5,69,0,0,603,602,1,0,0,0,603,604,1,0,0,0,604,605,1,0,0,0,605,
		607,5,2,0,0,606,608,5,69,0,0,607,606,1,0,0,0,607,608,1,0,0,0,608,609,1,
		0,0,0,609,657,3,64,32,9,610,612,10,7,0,0,611,613,5,69,0,0,612,611,1,0,
		0,0,612,613,1,0,0,0,613,614,1,0,0,0,614,616,5,28,0,0,615,617,5,69,0,0,
		616,615,1,0,0,0,616,617,1,0,0,0,617,618,1,0,0,0,618,657,3,64,32,8,619,
		621,10,6,0,0,620,622,5,69,0,0,621,620,1,0,0,0,621,622,1,0,0,0,622,623,
		1,0,0,0,623,625,5,32,0,0,624,626,5,69,0,0,625,624,1,0,0,0,625,626,1,0,
		0,0,626,627,1,0,0,0,627,657,3,64,32,7,628,630,10,5,0,0,629,631,5,69,0,
		0,630,629,1,0,0,0,630,631,1,0,0,0,631,632,1,0,0,0,632,634,5,13,0,0,633,
		635,5,69,0,0,634,633,1,0,0,0,634,635,1,0,0,0,635,636,1,0,0,0,636,657,3,
		64,32,6,637,639,10,4,0,0,638,640,5,69,0,0,639,638,1,0,0,0,639,640,1,0,
		0,0,640,641,1,0,0,0,641,643,5,16,0,0,642,644,5,69,0,0,643,642,1,0,0,0,
		643,644,1,0,0,0,644,645,1,0,0,0,645,657,3,64,32,5,646,647,10,3,0,0,647,
		648,5,69,0,0,648,649,5,18,0,0,649,650,5,69,0,0,650,657,3,64,32,4,651,652,
		10,2,0,0,652,653,5,69,0,0,653,654,5,17,0,0,654,655,5,69,0,0,655,657,3,
		64,32,3,656,511,1,0,0,0,656,520,1,0,0,0,656,529,1,0,0,0,656,538,1,0,0,
		0,656,547,1,0,0,0,656,556,1,0,0,0,656,565,1,0,0,0,656,574,1,0,0,0,656,
		583,1,0,0,0,656,592,1,0,0,0,656,601,1,0,0,0,656,610,1,0,0,0,656,619,1,
		0,0,0,656,628,1,0,0,0,656,637,1,0,0,0,656,646,1,0,0,0,656,651,1,0,0,0,
		657,660,1,0,0,0,658,656,1,0,0,0,658,659,1,0,0,0,659,65,1,0,0,0,660,658,
		1,0,0,0,661,666,5,59,0,0,662,666,5,63,0,0,663,666,5,58,0,0,664,666,5,57,
		0,0,665,661,1,0,0,0,665,662,1,0,0,0,665,663,1,0,0,0,665,664,1,0,0,0,666,
		67,1,0,0,0,667,673,5,61,0,0,668,673,3,66,33,0,669,673,5,60,0,0,670,673,
		5,30,0,0,671,673,5,14,0,0,672,667,1,0,0,0,672,668,1,0,0,0,672,669,1,0,
		0,0,672,670,1,0,0,0,672,671,1,0,0,0,673,69,1,0,0,0,674,675,8,3,0,0,675,
		71,1,0,0,0,117,75,81,85,90,94,101,106,111,114,121,126,129,134,138,143,
		147,152,156,161,167,171,175,180,183,188,200,206,211,217,221,225,233,239,
		242,248,252,258,262,274,279,285,290,299,304,310,315,320,325,331,336,342,
		348,353,356,364,369,374,380,385,388,396,400,405,410,412,418,427,436,440,
		444,448,453,463,467,471,476,480,486,491,499,503,507,509,513,517,522,526,
		531,535,540,544,549,553,558,562,567,571,576,580,585,589,594,598,603,607,
		612,616,621,625,630,634,639,643,656,658,665,672
	};

	public static readonly ATN _ATN =
		new ATNDeserializer().Deserialize(_serializedATN);


}
