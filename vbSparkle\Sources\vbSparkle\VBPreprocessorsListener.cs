//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     ANTLR Version: 4.13.1
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// Generated from VBPreprocessors.g4 by ANTLR 4.13.1

// Unreachable code detected
#pragma warning disable 0162
// The variable '...' is assigned but its value is never used
#pragma warning disable 0219
// Missing XML comment for publicly visible type or member '...'
#pragma warning disable 1591
// Ambiguous reference in cref attribute
#pragma warning disable 419

using Antlr4.Runtime.Misc;
using IParseTreeListener = Antlr4.Runtime.Tree.IParseTreeListener;
using IToken = Antlr4.Runtime.IToken;

/// <summary>
/// This interface defines a complete listener for a parse tree produced by
/// <see cref="VBPreprocessorsParser"/>.
/// </summary>
[System.CodeDom.Compiler.GeneratedCode("ANTLR", "4.13.1")]
[System.CLSCompliant(false)]
public interface IVBPreprocessorsListener : IParseTreeListener {
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.startRule"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterStartRule([NotNull] VBPreprocessorsParser.StartRuleContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.startRule"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitStartRule([NotNull] VBPreprocessorsParser.StartRuleContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.macroConst"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterMacroConst([NotNull] VBPreprocessorsParser.MacroConstContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.macroConst"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitMacroConst([NotNull] VBPreprocessorsParser.MacroConstContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.codeBlock"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCodeBlock([NotNull] VBPreprocessorsParser.CodeBlockContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.codeBlock"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCodeBlock([NotNull] VBPreprocessorsParser.CodeBlockContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vmacroIf</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.codeBlockBody"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVmacroIf([NotNull] VBPreprocessorsParser.VmacroIfContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vmacroIf</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.codeBlockBody"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVmacroIf([NotNull] VBPreprocessorsParser.VmacroIfContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vmacroConst</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.codeBlockBody"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVmacroConst([NotNull] VBPreprocessorsParser.VmacroConstContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vmacroConst</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.codeBlockBody"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVmacroConst([NotNull] VBPreprocessorsParser.VmacroConstContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vcommentBlock</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.codeBlockBody"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVcommentBlock([NotNull] VBPreprocessorsParser.VcommentBlockContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vcommentBlock</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.codeBlockBody"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVcommentBlock([NotNull] VBPreprocessorsParser.VcommentBlockContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vcodeBlock</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.codeBlockBody"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVcodeBlock([NotNull] VBPreprocessorsParser.VcodeBlockContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vcodeBlock</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.codeBlockBody"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVcodeBlock([NotNull] VBPreprocessorsParser.VcodeBlockContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vlineLabel</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.codeBlockBody"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVlineLabel([NotNull] VBPreprocessorsParser.VlineLabelContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vlineLabel</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.codeBlockBody"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVlineLabel([NotNull] VBPreprocessorsParser.VlineLabelContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.nonMacroCodeBlockX"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterNonMacroCodeBlockX([NotNull] VBPreprocessorsParser.NonMacroCodeBlockXContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.nonMacroCodeBlockX"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitNonMacroCodeBlockX([NotNull] VBPreprocessorsParser.NonMacroCodeBlockXContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.moduleInfo"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleInfo([NotNull] VBPreprocessorsParser.ModuleInfoContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.moduleInfo"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleInfo([NotNull] VBPreprocessorsParser.ModuleInfoContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.moduleReferences"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleReferences([NotNull] VBPreprocessorsParser.ModuleReferencesContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.moduleReferences"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleReferences([NotNull] VBPreprocessorsParser.ModuleReferencesContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.moduleReference"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleReference([NotNull] VBPreprocessorsParser.ModuleReferenceContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.moduleReference"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleReference([NotNull] VBPreprocessorsParser.ModuleReferenceContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.moduleReferenceValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleReferenceValue([NotNull] VBPreprocessorsParser.ModuleReferenceValueContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.moduleReferenceValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleReferenceValue([NotNull] VBPreprocessorsParser.ModuleReferenceValueContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.moduleReferenceComponent"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleReferenceComponent([NotNull] VBPreprocessorsParser.ModuleReferenceComponentContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.moduleReferenceComponent"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleReferenceComponent([NotNull] VBPreprocessorsParser.ModuleReferenceComponentContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.moduleHeader"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleHeader([NotNull] VBPreprocessorsParser.ModuleHeaderContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.moduleHeader"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleHeader([NotNull] VBPreprocessorsParser.ModuleHeaderContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.moduleConfig"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleConfig([NotNull] VBPreprocessorsParser.ModuleConfigContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.moduleConfig"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleConfig([NotNull] VBPreprocessorsParser.ModuleConfigContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.moduleConfigElement"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleConfigElement([NotNull] VBPreprocessorsParser.ModuleConfigElementContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.moduleConfigElement"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleConfigElement([NotNull] VBPreprocessorsParser.ModuleConfigElementContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.ambiguousIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterAmbiguousIdentifier([NotNull] VBPreprocessorsParser.AmbiguousIdentifierContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.ambiguousIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitAmbiguousIdentifier([NotNull] VBPreprocessorsParser.AmbiguousIdentifierContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.moduleAttributes"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleAttributes([NotNull] VBPreprocessorsParser.ModuleAttributesContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.moduleAttributes"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleAttributes([NotNull] VBPreprocessorsParser.ModuleAttributesContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.macroIfThenElseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterMacroIfThenElseStmt([NotNull] VBPreprocessorsParser.MacroIfThenElseStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.macroIfThenElseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitMacroIfThenElseStmt([NotNull] VBPreprocessorsParser.MacroIfThenElseStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.macroIfBlockCondStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterMacroIfBlockCondStmt([NotNull] VBPreprocessorsParser.MacroIfBlockCondStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.macroIfBlockCondStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitMacroIfBlockCondStmt([NotNull] VBPreprocessorsParser.MacroIfBlockCondStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.macroIfBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterMacroIfBlockStmt([NotNull] VBPreprocessorsParser.MacroIfBlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.macroIfBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitMacroIfBlockStmt([NotNull] VBPreprocessorsParser.MacroIfBlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.macroElseIfBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterMacroElseIfBlockStmt([NotNull] VBPreprocessorsParser.MacroElseIfBlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.macroElseIfBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitMacroElseIfBlockStmt([NotNull] VBPreprocessorsParser.MacroElseIfBlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.macroElseBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterMacroElseBlockStmt([NotNull] VBPreprocessorsParser.MacroElseBlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.macroElseBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitMacroElseBlockStmt([NotNull] VBPreprocessorsParser.MacroElseBlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.commentBlock"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCommentBlock([NotNull] VBPreprocessorsParser.CommentBlockContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.commentBlock"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCommentBlock([NotNull] VBPreprocessorsParser.CommentBlockContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.lineLabel"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLineLabel([NotNull] VBPreprocessorsParser.LineLabelContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.lineLabel"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLineLabel([NotNull] VBPreprocessorsParser.LineLabelContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.nonMacroCodeBlockLine"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterNonMacroCodeBlockLine([NotNull] VBPreprocessorsParser.NonMacroCodeBlockLineContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.nonMacroCodeBlockLine"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitNonMacroCodeBlockLine([NotNull] VBPreprocessorsParser.NonMacroCodeBlockLineContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.nonMacroCodeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterNonMacroCodeStmt([NotNull] VBPreprocessorsParser.NonMacroCodeStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.nonMacroCodeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitNonMacroCodeStmt([NotNull] VBPreprocessorsParser.NonMacroCodeStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.controlProperties"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterControlProperties([NotNull] VBPreprocessorsParser.ControlPropertiesContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.controlProperties"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitControlProperties([NotNull] VBPreprocessorsParser.ControlPropertiesContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.cp_Properties"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCp_Properties([NotNull] VBPreprocessorsParser.Cp_PropertiesContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.cp_Properties"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCp_Properties([NotNull] VBPreprocessorsParser.Cp_PropertiesContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.cp_NestedProperty"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCp_NestedProperty([NotNull] VBPreprocessorsParser.Cp_NestedPropertyContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.cp_NestedProperty"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCp_NestedProperty([NotNull] VBPreprocessorsParser.Cp_NestedPropertyContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.cp_ControlType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCp_ControlType([NotNull] VBPreprocessorsParser.Cp_ControlTypeContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.cp_ControlType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCp_ControlType([NotNull] VBPreprocessorsParser.Cp_ControlTypeContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.complexType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterComplexType([NotNull] VBPreprocessorsParser.ComplexTypeContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.complexType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitComplexType([NotNull] VBPreprocessorsParser.ComplexTypeContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.cp_ControlIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCp_ControlIdentifier([NotNull] VBPreprocessorsParser.Cp_ControlIdentifierContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.cp_ControlIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCp_ControlIdentifier([NotNull] VBPreprocessorsParser.Cp_ControlIdentifierContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.attributeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterAttributeStmt([NotNull] VBPreprocessorsParser.AttributeStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.attributeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitAttributeStmt([NotNull] VBPreprocessorsParser.AttributeStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.ifConditionStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterIfConditionStmt([NotNull] VBPreprocessorsParser.IfConditionStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.ifConditionStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitIfConditionStmt([NotNull] VBPreprocessorsParser.IfConditionStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsStruct</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsStruct([NotNull] VBPreprocessorsParser.VsStructContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsStruct</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsStruct([NotNull] VBPreprocessorsParser.VsStructContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsLiteral</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsLiteral([NotNull] VBPreprocessorsParser.VsLiteralContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsLiteral</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsLiteral([NotNull] VBPreprocessorsParser.VsLiteralContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsConstant</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsConstant([NotNull] VBPreprocessorsParser.VsConstantContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsConstant</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsConstant([NotNull] VBPreprocessorsParser.VsConstantContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsUnaryOperation</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsUnaryOperation([NotNull] VBPreprocessorsParser.VsUnaryOperationContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsUnaryOperation</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsUnaryOperation([NotNull] VBPreprocessorsParser.VsUnaryOperationContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsDualOperation</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsDualOperation([NotNull] VBPreprocessorsParser.VsDualOperationContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsDualOperation</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsDualOperation([NotNull] VBPreprocessorsParser.VsDualOperationContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>ltColor</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.delimitedLiteral"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLtColor([NotNull] VBPreprocessorsParser.LtColorContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>ltColor</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.delimitedLiteral"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLtColor([NotNull] VBPreprocessorsParser.LtColorContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>ltOctal</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.delimitedLiteral"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLtOctal([NotNull] VBPreprocessorsParser.LtOctalContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>ltOctal</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.delimitedLiteral"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLtOctal([NotNull] VBPreprocessorsParser.LtOctalContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>ltDate</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.delimitedLiteral"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLtDate([NotNull] VBPreprocessorsParser.LtDateContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>ltDate</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.delimitedLiteral"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLtDate([NotNull] VBPreprocessorsParser.LtDateContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>ltString</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.delimitedLiteral"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLtString([NotNull] VBPreprocessorsParser.LtStringContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>ltString</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.delimitedLiteral"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLtString([NotNull] VBPreprocessorsParser.LtStringContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>ltDouble</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLtDouble([NotNull] VBPreprocessorsParser.LtDoubleContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>ltDouble</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLtDouble([NotNull] VBPreprocessorsParser.LtDoubleContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>ltDelimited</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLtDelimited([NotNull] VBPreprocessorsParser.LtDelimitedContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>ltDelimited</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLtDelimited([NotNull] VBPreprocessorsParser.LtDelimitedContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>ltInteger</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLtInteger([NotNull] VBPreprocessorsParser.LtIntegerContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>ltInteger</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLtInteger([NotNull] VBPreprocessorsParser.LtIntegerContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>ltBoolean</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLtBoolean([NotNull] VBPreprocessorsParser.LtBooleanContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>ltBoolean</c>
	/// labeled alternative in <see cref="VBPreprocessorsParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLtBoolean([NotNull] VBPreprocessorsParser.LtBooleanContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBPreprocessorsParser.anytoken"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterAnytoken([NotNull] VBPreprocessorsParser.AnytokenContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBPreprocessorsParser.anytoken"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitAnytoken([NotNull] VBPreprocessorsParser.AnytokenContext context);
}
