# VBA Abstract Syntax Tree (AST) System

This directory contains a complete AST implementation for the VBA language, built on top of the existing ANTLR-based CST (Concrete Syntax Tree) parser from the Rubberduck project.

## Overview

The AST system provides a higher-level, more semantic representation of VBA code compared to the raw parse tree generated by ANTLR. This makes it easier to perform code analysis, transformation, and generation tasks such as obfuscation and deobfuscation.

## Key Components

### Core Interfaces and Base Classes

- **`IASTNode.cs`** - Base interface for all AST nodes with common functionality
- **`IASTVisitor.cs`** - Visitor pattern interface and base implementation for AST traversal
- **`ASTNodeBase.cs`** - Base implementation providing common AST node functionality

### Node Types

- **`ExpressionNodes.cs`** - Expression nodes (binary ops, unary ops, calls, member access, etc.)
- **`LiteralNodes.cs`** - Literal value nodes (strings, numbers, booleans, dates, etc.)
- **`StatementNodes.cs`** - Statement nodes (assignments, control flow, loops, etc.)
- **`DeclarationNodes.cs`** - Declaration nodes (functions, subs, variables, constants, etc.)
- **`ModuleNodes.cs`** - Top-level module structure nodes
- **`SupportingNodes.cs`** - Supporting nodes (parameters, arguments, types, etc.)

### Utilities and Tools

- **`CSTToASTVisitor.cs`** - Converts ANTLR CST to custom AST
- **`ASTUtilities.cs`** - Utility functions for AST analysis and manipulation
- **`VBACodeGenerator.cs`** - Generates VBA code from AST nodes
- **`ASTExample.cs`** - Example usage and demonstrations

## Usage

### Basic Parsing

```csharp
using VbDeobf.AST;

// Parse VBA code into AST
string vbaCode = @"
Public Function Add(x As Integer, y As Integer) As Integer
    Add = x + y
End Function
";

var ast = ASTExample.ParseVBACode(vbaCode, "example.bas");
```

### AST Analysis

```csharp
// Get statistics about the AST
var stats = ASTUtilities.GetStatistics(ast);
Console.WriteLine(stats.ToString());

// Find all functions
var functions = ASTUtilities.FindNodesOfType<FunctionDeclarationNode>(ast);
foreach (var func in functions)
{
    Console.WriteLine($"Function: {func.Name}");
}

// Find all variables
var variables = ASTUtilities.GetAllVariables(ast);
foreach (var var in variables)
{
    Console.WriteLine($"Variable: {var.Name}");
}
```

### Code Generation

```csharp
// Generate VBA code from AST
var codeGenerator = new VBACodeGenerator();
string generatedCode = ast.Accept(codeGenerator);
Console.WriteLine(generatedCode);
```

### Identifier Renaming (Basic Obfuscation)

```csharp
// Rename identifiers for obfuscation
int renamedCount = ASTUtilities.ReplaceIdentifier(ast, "Add", "Func1");
renamedCount += ASTUtilities.ReplaceIdentifier(ast, "x", "param1");
renamedCount += ASTUtilities.ReplaceIdentifier(ast, "y", "param2");

Console.WriteLine($"Renamed {renamedCount} identifiers");
```

### Custom AST Traversal

```csharp
// Create a custom visitor
public class MyVisitor : BaseASTVisitor<string>
{
    public override string VisitFunctionDeclaration(FunctionDeclarationNode node)
    {
        Console.WriteLine($"Found function: {node.Name}");
        return base.VisitFunctionDeclaration(node);
    }
}

var visitor = new MyVisitor();
ast.Accept(visitor);
```

## Running Examples

To run the built-in examples:

```bash
dotnet run ast
```

This will run all the example scenarios including:
- Basic AST parsing and analysis
- Identifier renaming
- AST validation
- Pattern searching

## AST Node Hierarchy

```
IASTNode
├── ExpressionNode
│   ├── LiteralExpressionNode
│   ├── IdentifierExpressionNode
│   ├── BinaryExpressionNode
│   ├── UnaryExpressionNode
│   ├── MemberAccessExpressionNode
│   ├── IndexExpressionNode
│   ├── CallExpressionNode
│   └── ...
├── StatementNode
│   ├── AssignmentStatementNode
│   ├── CallStatementNode
│   ├── IfStatementNode
│   ├── ForStatementNode
│   ├── WhileStatementNode
│   └── ...
├── DeclarationNode
│   ├── FunctionDeclarationNode
│   ├── SubroutineDeclarationNode
│   ├── VariableDeclarationNode
│   ├── ConstantDeclarationNode
│   └── ...
└── Other specialized nodes
```

## Features

### Supported VBA Constructs

- ✅ Functions and Subroutines
- ✅ Variables and Constants
- ✅ Properties (Get, Let, Set)
- ✅ Control Flow (If, Select Case, For, While, Do Loop)
- ✅ Expressions (Binary, Unary, Calls, Member Access)
- ✅ Literals (String, Number, Boolean, Date, Nothing, Empty, Null)
- ✅ Parameters and Arguments
- ✅ Module structure and attributes
- ✅ Type declarations and enums
- ⚠️ Some advanced VBA features may need additional implementation

### Limitations

- The CST to AST conversion handles most common VBA constructs but may need extension for very advanced or obscure VBA features
- Error handling in the visitor could be more robust
- Some binary/unary operator detection is simplified and may need refinement
- Node cloning is not yet implemented

## Use Cases for Obfuscation/Deobfuscation

This AST system is particularly useful for:

1. **Identifier Renaming** - Systematically rename variables, functions, and other identifiers
2. **Control Flow Analysis** - Understand and potentially restructure control flow
3. **Dead Code Detection** - Find unused variables and functions
4. **Code Metrics** - Calculate complexity metrics and statistics
5. **Pattern Matching** - Find specific code patterns that may indicate obfuscation
6. **Code Transformation** - Apply systematic transformations to simplify obfuscated code
7. **Semantic Analysis** - Understand the meaning and structure of code beyond syntax

## Contributing

When extending the AST system:

1. Add new node types to the appropriate files
2. Update the visitor interfaces and base classes
3. Extend the CST to AST visitor to handle new constructs
4. Add code generation support for new nodes
5. Update examples and tests

## Testing

The system includes basic examples and validation. For more comprehensive testing, consider:

1. Testing with various VBA code samples
2. Validating round-trip conversion (code → AST → code)
3. Performance testing with large VBA files
4. Edge case testing with unusual VBA constructs
