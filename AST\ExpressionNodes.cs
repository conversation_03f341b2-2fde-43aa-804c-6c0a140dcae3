using System.Collections.Generic;

namespace VbDeobf.AST
{
    /// <summary>
    /// Literal expression node
    /// </summary>
    public class LiteralExpressionNode : ExpressionNode
    {
        public override ASTNodeType NodeType => ASTNodeType.LiteralExpression;
        public object? Value { get; set; }
        public LiteralType LiteralType { get; set; }
        
        public LiteralExpressionNode(object? value, LiteralType literalType, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            Value = value;
            LiteralType = literalType;
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitLiteralExpression(this);
    }
    
    /// <summary>
    /// Identifier expression node
    /// </summary>
    public class IdentifierExpressionNode : ExpressionNode
    {
        public override ASTNodeType NodeType => ASTNodeType.IdentifierExpression;
        public string Name { get; set; }
        
        public IdentifierExpressionNode(string name, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            Name = name;
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitIdentifierExpression(this);
    }
    
    /// <summary>
    /// Binary expression node
    /// </summary>
    public class BinaryExpressionNode : ExpressionNode
    {
        public override ASTNodeType NodeType => ASTNodeType.BinaryExpression;
        public ExpressionNode Left { get; set; }
        public BinaryOperator Operator { get; set; }
        public ExpressionNode Right { get; set; }
        
        public BinaryExpressionNode(ExpressionNode left, BinaryOperator op, ExpressionNode right, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            Left = left;
            Operator = op;
            Right = right;
            AddChild(left);
            AddChild(right);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitBinaryExpression(this);
    }
    
    /// <summary>
    /// Unary expression node
    /// </summary>
    public class UnaryExpressionNode : ExpressionNode
    {
        public override ASTNodeType NodeType => ASTNodeType.UnaryExpression;
        public UnaryOperator Operator { get; set; }
        public ExpressionNode Operand { get; set; }
        
        public UnaryExpressionNode(UnaryOperator op, ExpressionNode operand, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            Operator = op;
            Operand = operand;
            AddChild(operand);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitUnaryExpression(this);
    }
    
    /// <summary>
    /// Member access expression node (e.g., obj.member)
    /// </summary>
    public class MemberAccessExpressionNode : ExpressionNode
    {
        public override ASTNodeType NodeType => ASTNodeType.MemberAccessExpression;
        public ExpressionNode Object { get; set; }
        public string MemberName { get; set; }
        public bool IsDictionaryAccess { get; set; } // For obj!member syntax
        
        public MemberAccessExpressionNode(ExpressionNode obj, string memberName, bool isDictionaryAccess = false, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            Object = obj;
            MemberName = memberName;
            IsDictionaryAccess = isDictionaryAccess;
            AddChild(obj);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitMemberAccessExpression(this);
    }
    
    /// <summary>
    /// Index expression node (e.g., arr(1, 2))
    /// </summary>
    public class IndexExpressionNode : ExpressionNode
    {
        public override ASTNodeType NodeType => ASTNodeType.IndexExpression;
        public ExpressionNode Object { get; set; }
        public ArgumentListNode Arguments { get; set; }
        
        public IndexExpressionNode(ExpressionNode obj, ArgumentListNode arguments, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            Object = obj;
            Arguments = arguments;
            AddChild(obj);
            AddChild(arguments);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitIndexExpression(this);
    }
    
    /// <summary>
    /// Call expression node (e.g., func(arg1, arg2))
    /// </summary>
    public class CallExpressionNode : ExpressionNode
    {
        public override ASTNodeType NodeType => ASTNodeType.CallExpression;
        public ExpressionNode Function { get; set; }
        public ArgumentListNode Arguments { get; set; }
        
        public CallExpressionNode(ExpressionNode function, ArgumentListNode arguments, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            Function = function;
            Arguments = arguments;
            AddChild(function);
            AddChild(arguments);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitCallExpression(this);
    }
    
    /// <summary>
    /// Parenthesized expression node
    /// </summary>
    public class ParenthesizedExpressionNode : ExpressionNode
    {
        public override ASTNodeType NodeType => ASTNodeType.ParenthesizedExpression;
        public ExpressionNode Expression { get; set; }
        
        public ParenthesizedExpressionNode(ExpressionNode expression, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            Expression = expression;
            AddChild(expression);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitParenthesizedExpression(this);
    }
    
    /// <summary>
    /// TypeOf expression node
    /// </summary>
    public class TypeOfExpressionNode : ExpressionNode
    {
        public override ASTNodeType NodeType => ASTNodeType.TypeOfExpression;
        public ExpressionNode Expression { get; set; }
        
        public TypeOfExpressionNode(ExpressionNode expression, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            Expression = expression;
            AddChild(expression);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitTypeOfExpression(this);
    }
    
    /// <summary>
    /// New expression node
    /// </summary>
    public class NewExpressionNode : ExpressionNode
    {
        public override ASTNodeType NodeType => ASTNodeType.NewExpression;
        public ExpressionNode Type { get; set; }
        
        public NewExpressionNode(ExpressionNode type, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            Type = type;
            AddChild(type);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitNewExpression(this);
    }
    
    /// <summary>
    /// AddressOf expression node
    /// </summary>
    public class AddressOfExpressionNode : ExpressionNode
    {
        public override ASTNodeType NodeType => ASTNodeType.AddressOfExpression;
        public ExpressionNode Expression { get; set; }
        
        public AddressOfExpressionNode(ExpressionNode expression, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            Expression = expression;
            AddChild(expression);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitAddressOfExpression(this);
    }
    
    /// <summary>
    /// Types of literals
    /// </summary>
    public enum LiteralType
    {
        String,
        Number,
        Boolean,
        Date,
        Nothing,
        Empty,
        Null
    }
}
