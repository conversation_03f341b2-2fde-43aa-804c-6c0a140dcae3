//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     ANTLR Version: 4.13.1
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// Generated from VBScript.g4 by ANTLR 4.13.1

// Unreachable code detected
#pragma warning disable 0162
// The variable '...' is assigned but its value is never used
#pragma warning disable 0219
// Missing XML comment for publicly visible type or member '...'
#pragma warning disable 1591
// Ambiguous reference in cref attribute
#pragma warning disable 419


using Antlr4.Runtime.Misc;
using IErrorNode = Antlr4.Runtime.Tree.IErrorNode;
using ITerminalNode = Antlr4.Runtime.Tree.ITerminalNode;
using IToken = Antlr4.Runtime.IToken;
using ParserRuleContext = Antlr4.Runtime.ParserRuleContext;

/// <summary>
/// This class provides an empty implementation of <see cref="IVBScriptListener"/>,
/// which can be extended to create a listener which only needs to handle a subset
/// of the available methods.
/// </summary>
[System.CodeDom.Compiler.GeneratedCode("ANTLR", "4.13.1")]
[System.Diagnostics.DebuggerNonUserCode]
[System.CLSCompliant(false)]
public partial class VBScriptBaseListener : IVBScriptListener {
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.startRule"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterStartRule([NotNull] VBScriptParser.StartRuleContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.startRule"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitStartRule([NotNull] VBScriptParser.StartRuleContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.module"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModule([NotNull] VBScriptParser.ModuleContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.module"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModule([NotNull] VBScriptParser.ModuleContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleReferences"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleReferences([NotNull] VBScriptParser.ModuleReferencesContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleReferences"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleReferences([NotNull] VBScriptParser.ModuleReferencesContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleReference"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleReference([NotNull] VBScriptParser.ModuleReferenceContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleReference"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleReference([NotNull] VBScriptParser.ModuleReferenceContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleReferenceValue"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleReferenceValue([NotNull] VBScriptParser.ModuleReferenceValueContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleReferenceValue"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleReferenceValue([NotNull] VBScriptParser.ModuleReferenceValueContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleReferenceComponent"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleReferenceComponent([NotNull] VBScriptParser.ModuleReferenceComponentContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleReferenceComponent"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleReferenceComponent([NotNull] VBScriptParser.ModuleReferenceComponentContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleHeader"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleHeader([NotNull] VBScriptParser.ModuleHeaderContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleHeader"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleHeader([NotNull] VBScriptParser.ModuleHeaderContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleConfig"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleConfig([NotNull] VBScriptParser.ModuleConfigContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleConfig"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleConfig([NotNull] VBScriptParser.ModuleConfigContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleConfigElement"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleConfigElement([NotNull] VBScriptParser.ModuleConfigElementContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleConfigElement"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleConfigElement([NotNull] VBScriptParser.ModuleConfigElementContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleAttributes"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleAttributes([NotNull] VBScriptParser.ModuleAttributesContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleAttributes"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleAttributes([NotNull] VBScriptParser.ModuleAttributesContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleOptions"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleOptions([NotNull] VBScriptParser.ModuleOptionsContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleOptions"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleOptions([NotNull] VBScriptParser.ModuleOptionsContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>optionBaseStmt</c>
	/// labeled alternative in <see cref="VBScriptParser.moduleOption"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterOptionBaseStmt([NotNull] VBScriptParser.OptionBaseStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>optionBaseStmt</c>
	/// labeled alternative in <see cref="VBScriptParser.moduleOption"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitOptionBaseStmt([NotNull] VBScriptParser.OptionBaseStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>optionCompareStmt</c>
	/// labeled alternative in <see cref="VBScriptParser.moduleOption"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterOptionCompareStmt([NotNull] VBScriptParser.OptionCompareStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>optionCompareStmt</c>
	/// labeled alternative in <see cref="VBScriptParser.moduleOption"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitOptionCompareStmt([NotNull] VBScriptParser.OptionCompareStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>optionExplicitStmt</c>
	/// labeled alternative in <see cref="VBScriptParser.moduleOption"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterOptionExplicitStmt([NotNull] VBScriptParser.OptionExplicitStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>optionExplicitStmt</c>
	/// labeled alternative in <see cref="VBScriptParser.moduleOption"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitOptionExplicitStmt([NotNull] VBScriptParser.OptionExplicitStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>optionPrivateModuleStmt</c>
	/// labeled alternative in <see cref="VBScriptParser.moduleOption"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterOptionPrivateModuleStmt([NotNull] VBScriptParser.OptionPrivateModuleStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>optionPrivateModuleStmt</c>
	/// labeled alternative in <see cref="VBScriptParser.moduleOption"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitOptionPrivateModuleStmt([NotNull] VBScriptParser.OptionPrivateModuleStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleBody"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleBody([NotNull] VBScriptParser.ModuleBodyContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleBody"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleBody([NotNull] VBScriptParser.ModuleBodyContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleBodyElement"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleBodyElement([NotNull] VBScriptParser.ModuleBodyElementContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleBodyElement"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleBodyElement([NotNull] VBScriptParser.ModuleBodyElementContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.controlProperties"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterControlProperties([NotNull] VBScriptParser.ControlPropertiesContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.controlProperties"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitControlProperties([NotNull] VBScriptParser.ControlPropertiesContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.cp_Properties"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCp_Properties([NotNull] VBScriptParser.Cp_PropertiesContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.cp_Properties"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCp_Properties([NotNull] VBScriptParser.Cp_PropertiesContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.cp_PropertyName"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCp_PropertyName([NotNull] VBScriptParser.Cp_PropertyNameContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.cp_PropertyName"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCp_PropertyName([NotNull] VBScriptParser.Cp_PropertyNameContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.cp_PropertyValue"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCp_PropertyValue([NotNull] VBScriptParser.Cp_PropertyValueContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.cp_PropertyValue"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCp_PropertyValue([NotNull] VBScriptParser.Cp_PropertyValueContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.cp_NestedProperty"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCp_NestedProperty([NotNull] VBScriptParser.Cp_NestedPropertyContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.cp_NestedProperty"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCp_NestedProperty([NotNull] VBScriptParser.Cp_NestedPropertyContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.cp_ControlType"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCp_ControlType([NotNull] VBScriptParser.Cp_ControlTypeContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.cp_ControlType"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCp_ControlType([NotNull] VBScriptParser.Cp_ControlTypeContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.cp_ControlIdentifier"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCp_ControlIdentifier([NotNull] VBScriptParser.Cp_ControlIdentifierContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.cp_ControlIdentifier"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCp_ControlIdentifier([NotNull] VBScriptParser.Cp_ControlIdentifierContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.blockSwitch"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterBlockSwitch([NotNull] VBScriptParser.BlockSwitchContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.blockSwitch"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitBlockSwitch([NotNull] VBScriptParser.BlockSwitchContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.block"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterBlock([NotNull] VBScriptParser.BlockContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.block"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitBlock([NotNull] VBScriptParser.BlockContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleBlock"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleBlock([NotNull] VBScriptParser.ModuleBlockContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleBlock"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleBlock([NotNull] VBScriptParser.ModuleBlockContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.attributeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterAttributeStmt([NotNull] VBScriptParser.AttributeStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.attributeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitAttributeStmt([NotNull] VBScriptParser.AttributeStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.lineLabel"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLineLabel([NotNull] VBScriptParser.LineLabelContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.lineLabel"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLineLabel([NotNull] VBScriptParser.LineLabelContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.inlineBlock"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterInlineBlock([NotNull] VBScriptParser.InlineBlockContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.inlineBlock"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitInlineBlock([NotNull] VBScriptParser.InlineBlockContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.inlineBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterInlineBlockStmt([NotNull] VBScriptParser.InlineBlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.inlineBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitInlineBlockStmt([NotNull] VBScriptParser.InlineBlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.blockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterBlockStmt([NotNull] VBScriptParser.BlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.blockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitBlockStmt([NotNull] VBScriptParser.BlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.appActivateStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterAppActivateStmt([NotNull] VBScriptParser.AppActivateStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.appActivateStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitAppActivateStmt([NotNull] VBScriptParser.AppActivateStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.beepStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterBeepStmt([NotNull] VBScriptParser.BeepStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.beepStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitBeepStmt([NotNull] VBScriptParser.BeepStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.chDirStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterChDirStmt([NotNull] VBScriptParser.ChDirStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.chDirStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitChDirStmt([NotNull] VBScriptParser.ChDirStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.chDriveStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterChDriveStmt([NotNull] VBScriptParser.ChDriveStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.chDriveStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitChDriveStmt([NotNull] VBScriptParser.ChDriveStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.closeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCloseStmt([NotNull] VBScriptParser.CloseStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.closeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCloseStmt([NotNull] VBScriptParser.CloseStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.constStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterConstStmt([NotNull] VBScriptParser.ConstStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.constStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitConstStmt([NotNull] VBScriptParser.ConstStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.constSubStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterConstSubStmt([NotNull] VBScriptParser.ConstSubStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.constSubStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitConstSubStmt([NotNull] VBScriptParser.ConstSubStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.dateStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterDateStmt([NotNull] VBScriptParser.DateStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.dateStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitDateStmt([NotNull] VBScriptParser.DateStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.declareStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterDeclareStmt([NotNull] VBScriptParser.DeclareStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.declareStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitDeclareStmt([NotNull] VBScriptParser.DeclareStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.deftypeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterDeftypeStmt([NotNull] VBScriptParser.DeftypeStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.deftypeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitDeftypeStmt([NotNull] VBScriptParser.DeftypeStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.deleteSettingStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterDeleteSettingStmt([NotNull] VBScriptParser.DeleteSettingStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.deleteSettingStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitDeleteSettingStmt([NotNull] VBScriptParser.DeleteSettingStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>dlStatement</c>
	/// labeled alternative in <see cref="VBScriptParser.doLoopStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterDlStatement([NotNull] VBScriptParser.DlStatementContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>dlStatement</c>
	/// labeled alternative in <see cref="VBScriptParser.doLoopStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitDlStatement([NotNull] VBScriptParser.DlStatementContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>dwlStatement</c>
	/// labeled alternative in <see cref="VBScriptParser.doLoopStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterDwlStatement([NotNull] VBScriptParser.DwlStatementContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>dwlStatement</c>
	/// labeled alternative in <see cref="VBScriptParser.doLoopStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitDwlStatement([NotNull] VBScriptParser.DwlStatementContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>dlwStatement</c>
	/// labeled alternative in <see cref="VBScriptParser.doLoopStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterDlwStatement([NotNull] VBScriptParser.DlwStatementContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>dlwStatement</c>
	/// labeled alternative in <see cref="VBScriptParser.doLoopStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitDlwStatement([NotNull] VBScriptParser.DlwStatementContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.endStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterEndStmt([NotNull] VBScriptParser.EndStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.endStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitEndStmt([NotNull] VBScriptParser.EndStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.enumerationStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterEnumerationStmt([NotNull] VBScriptParser.EnumerationStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.enumerationStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitEnumerationStmt([NotNull] VBScriptParser.EnumerationStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.classStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterClassStmt([NotNull] VBScriptParser.ClassStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.classStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitClassStmt([NotNull] VBScriptParser.ClassStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.enumerationStmt_Constant"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterEnumerationStmt_Constant([NotNull] VBScriptParser.EnumerationStmt_ConstantContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.enumerationStmt_Constant"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitEnumerationStmt_Constant([NotNull] VBScriptParser.EnumerationStmt_ConstantContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.eraseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterEraseStmt([NotNull] VBScriptParser.EraseStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.eraseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitEraseStmt([NotNull] VBScriptParser.EraseStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.errorStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterErrorStmt([NotNull] VBScriptParser.ErrorStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.errorStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitErrorStmt([NotNull] VBScriptParser.ErrorStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.eventStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterEventStmt([NotNull] VBScriptParser.EventStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.eventStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitEventStmt([NotNull] VBScriptParser.EventStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.exitStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterExitStmt([NotNull] VBScriptParser.ExitStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.exitStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitExitStmt([NotNull] VBScriptParser.ExitStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.filecopyStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterFilecopyStmt([NotNull] VBScriptParser.FilecopyStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.filecopyStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitFilecopyStmt([NotNull] VBScriptParser.FilecopyStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.forEachStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterForEachStmt([NotNull] VBScriptParser.ForEachStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.forEachStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitForEachStmt([NotNull] VBScriptParser.ForEachStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.forNextStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterForNextStmt([NotNull] VBScriptParser.ForNextStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.forNextStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitForNextStmt([NotNull] VBScriptParser.ForNextStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.functionStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterFunctionStmt([NotNull] VBScriptParser.FunctionStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.functionStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitFunctionStmt([NotNull] VBScriptParser.FunctionStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.getStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterGetStmt([NotNull] VBScriptParser.GetStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.getStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitGetStmt([NotNull] VBScriptParser.GetStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.goSubStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterGoSubStmt([NotNull] VBScriptParser.GoSubStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.goSubStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitGoSubStmt([NotNull] VBScriptParser.GoSubStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.goToStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterGoToStmt([NotNull] VBScriptParser.GoToStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.goToStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitGoToStmt([NotNull] VBScriptParser.GoToStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>blockIfThenElse</c>
	/// labeled alternative in <see cref="VBScriptParser.ifThenElseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterBlockIfThenElse([NotNull] VBScriptParser.BlockIfThenElseContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>blockIfThenElse</c>
	/// labeled alternative in <see cref="VBScriptParser.ifThenElseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitBlockIfThenElse([NotNull] VBScriptParser.BlockIfThenElseContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>inlineIfThenElse</c>
	/// labeled alternative in <see cref="VBScriptParser.inlineIfThenElseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterInlineIfThenElse([NotNull] VBScriptParser.InlineIfThenElseContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>inlineIfThenElse</c>
	/// labeled alternative in <see cref="VBScriptParser.inlineIfThenElseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitInlineIfThenElse([NotNull] VBScriptParser.InlineIfThenElseContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.inlineIfBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterInlineIfBlockStmt([NotNull] VBScriptParser.InlineIfBlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.inlineIfBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitInlineIfBlockStmt([NotNull] VBScriptParser.InlineIfBlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.inlineElseBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterInlineElseBlockStmt([NotNull] VBScriptParser.InlineElseBlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.inlineElseBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitInlineElseBlockStmt([NotNull] VBScriptParser.InlineElseBlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.ifBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterIfBlockStmt([NotNull] VBScriptParser.IfBlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.ifBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitIfBlockStmt([NotNull] VBScriptParser.IfBlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.ifConditionStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterIfConditionStmt([NotNull] VBScriptParser.IfConditionStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.ifConditionStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitIfConditionStmt([NotNull] VBScriptParser.IfConditionStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.ifElseIfBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterIfElseIfBlockStmt([NotNull] VBScriptParser.IfElseIfBlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.ifElseIfBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitIfElseIfBlockStmt([NotNull] VBScriptParser.IfElseIfBlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.ifElseBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterIfElseBlockStmt([NotNull] VBScriptParser.IfElseBlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.ifElseBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitIfElseBlockStmt([NotNull] VBScriptParser.IfElseBlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.implementsStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterImplementsStmt([NotNull] VBScriptParser.ImplementsStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.implementsStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitImplementsStmt([NotNull] VBScriptParser.ImplementsStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.inputStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterInputStmt([NotNull] VBScriptParser.InputStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.inputStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitInputStmt([NotNull] VBScriptParser.InputStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.killStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterKillStmt([NotNull] VBScriptParser.KillStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.killStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitKillStmt([NotNull] VBScriptParser.KillStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.midStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterMidStmt([NotNull] VBScriptParser.MidStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.midStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitMidStmt([NotNull] VBScriptParser.MidStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.letStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLetStmt([NotNull] VBScriptParser.LetStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.letStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLetStmt([NotNull] VBScriptParser.LetStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.lineInputStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLineInputStmt([NotNull] VBScriptParser.LineInputStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.lineInputStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLineInputStmt([NotNull] VBScriptParser.LineInputStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.loadStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLoadStmt([NotNull] VBScriptParser.LoadStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.loadStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLoadStmt([NotNull] VBScriptParser.LoadStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.lockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLockStmt([NotNull] VBScriptParser.LockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.lockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLockStmt([NotNull] VBScriptParser.LockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.lsetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLsetStmt([NotNull] VBScriptParser.LsetStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.lsetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLsetStmt([NotNull] VBScriptParser.LsetStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.macroIfThenElseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterMacroIfThenElseStmt([NotNull] VBScriptParser.MacroIfThenElseStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.macroIfThenElseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitMacroIfThenElseStmt([NotNull] VBScriptParser.MacroIfThenElseStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.macroIfBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterMacroIfBlockStmt([NotNull] VBScriptParser.MacroIfBlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.macroIfBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitMacroIfBlockStmt([NotNull] VBScriptParser.MacroIfBlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.macroElseIfBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterMacroElseIfBlockStmt([NotNull] VBScriptParser.MacroElseIfBlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.macroElseIfBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitMacroElseIfBlockStmt([NotNull] VBScriptParser.MacroElseIfBlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.macroElseBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterMacroElseBlockStmt([NotNull] VBScriptParser.MacroElseBlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.macroElseBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitMacroElseBlockStmt([NotNull] VBScriptParser.MacroElseBlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.mkdirStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterMkdirStmt([NotNull] VBScriptParser.MkdirStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.mkdirStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitMkdirStmt([NotNull] VBScriptParser.MkdirStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.nameStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterNameStmt([NotNull] VBScriptParser.NameStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.nameStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitNameStmt([NotNull] VBScriptParser.NameStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.onErrorStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterOnErrorStmt([NotNull] VBScriptParser.OnErrorStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.onErrorStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitOnErrorStmt([NotNull] VBScriptParser.OnErrorStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.onGoToStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterOnGoToStmt([NotNull] VBScriptParser.OnGoToStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.onGoToStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitOnGoToStmt([NotNull] VBScriptParser.OnGoToStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.onGoSubStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterOnGoSubStmt([NotNull] VBScriptParser.OnGoSubStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.onGoSubStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitOnGoSubStmt([NotNull] VBScriptParser.OnGoSubStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.openStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterOpenStmt([NotNull] VBScriptParser.OpenStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.openStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitOpenStmt([NotNull] VBScriptParser.OpenStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.outputList"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterOutputList([NotNull] VBScriptParser.OutputListContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.outputList"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitOutputList([NotNull] VBScriptParser.OutputListContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.outputList_Expression"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterOutputList_Expression([NotNull] VBScriptParser.OutputList_ExpressionContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.outputList_Expression"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitOutputList_Expression([NotNull] VBScriptParser.OutputList_ExpressionContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.printStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterPrintStmt([NotNull] VBScriptParser.PrintStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.printStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitPrintStmt([NotNull] VBScriptParser.PrintStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.propertyGetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterPropertyGetStmt([NotNull] VBScriptParser.PropertyGetStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.propertyGetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitPropertyGetStmt([NotNull] VBScriptParser.PropertyGetStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.propertySetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterPropertySetStmt([NotNull] VBScriptParser.PropertySetStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.propertySetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitPropertySetStmt([NotNull] VBScriptParser.PropertySetStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.propertyLetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterPropertyLetStmt([NotNull] VBScriptParser.PropertyLetStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.propertyLetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitPropertyLetStmt([NotNull] VBScriptParser.PropertyLetStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.putStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterPutStmt([NotNull] VBScriptParser.PutStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.putStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitPutStmt([NotNull] VBScriptParser.PutStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.raiseEventStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterRaiseEventStmt([NotNull] VBScriptParser.RaiseEventStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.raiseEventStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitRaiseEventStmt([NotNull] VBScriptParser.RaiseEventStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.randomizeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterRandomizeStmt([NotNull] VBScriptParser.RandomizeStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.randomizeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitRandomizeStmt([NotNull] VBScriptParser.RandomizeStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.redimStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterRedimStmt([NotNull] VBScriptParser.RedimStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.redimStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitRedimStmt([NotNull] VBScriptParser.RedimStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.redimSubStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterRedimSubStmt([NotNull] VBScriptParser.RedimSubStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.redimSubStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitRedimSubStmt([NotNull] VBScriptParser.RedimSubStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.resetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterResetStmt([NotNull] VBScriptParser.ResetStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.resetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitResetStmt([NotNull] VBScriptParser.ResetStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.resumeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterResumeStmt([NotNull] VBScriptParser.ResumeStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.resumeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitResumeStmt([NotNull] VBScriptParser.ResumeStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.returnStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterReturnStmt([NotNull] VBScriptParser.ReturnStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.returnStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitReturnStmt([NotNull] VBScriptParser.ReturnStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.rmdirStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterRmdirStmt([NotNull] VBScriptParser.RmdirStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.rmdirStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitRmdirStmt([NotNull] VBScriptParser.RmdirStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.rsetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterRsetStmt([NotNull] VBScriptParser.RsetStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.rsetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitRsetStmt([NotNull] VBScriptParser.RsetStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.savepictureStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSavepictureStmt([NotNull] VBScriptParser.SavepictureStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.savepictureStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSavepictureStmt([NotNull] VBScriptParser.SavepictureStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.saveSettingStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSaveSettingStmt([NotNull] VBScriptParser.SaveSettingStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.saveSettingStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSaveSettingStmt([NotNull] VBScriptParser.SaveSettingStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.seekStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSeekStmt([NotNull] VBScriptParser.SeekStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.seekStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSeekStmt([NotNull] VBScriptParser.SeekStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.selectCaseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSelectCaseStmt([NotNull] VBScriptParser.SelectCaseStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.selectCaseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSelectCaseStmt([NotNull] VBScriptParser.SelectCaseStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.sC_Case"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSC_Case([NotNull] VBScriptParser.SC_CaseContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.sC_Case"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSC_Case([NotNull] VBScriptParser.SC_CaseContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>caseCondElse</c>
	/// labeled alternative in <see cref="VBScriptParser.sC_Cond"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCaseCondElse([NotNull] VBScriptParser.CaseCondElseContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>caseCondElse</c>
	/// labeled alternative in <see cref="VBScriptParser.sC_Cond"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCaseCondElse([NotNull] VBScriptParser.CaseCondElseContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>caseCondExpr</c>
	/// labeled alternative in <see cref="VBScriptParser.sC_Cond"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCaseCondExpr([NotNull] VBScriptParser.CaseCondExprContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>caseCondExpr</c>
	/// labeled alternative in <see cref="VBScriptParser.sC_Cond"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCaseCondExpr([NotNull] VBScriptParser.CaseCondExprContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>caseCondExprIs</c>
	/// labeled alternative in <see cref="VBScriptParser.sC_CondExpr"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCaseCondExprIs([NotNull] VBScriptParser.CaseCondExprIsContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>caseCondExprIs</c>
	/// labeled alternative in <see cref="VBScriptParser.sC_CondExpr"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCaseCondExprIs([NotNull] VBScriptParser.CaseCondExprIsContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>caseCondExprValue</c>
	/// labeled alternative in <see cref="VBScriptParser.sC_CondExpr"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCaseCondExprValue([NotNull] VBScriptParser.CaseCondExprValueContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>caseCondExprValue</c>
	/// labeled alternative in <see cref="VBScriptParser.sC_CondExpr"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCaseCondExprValue([NotNull] VBScriptParser.CaseCondExprValueContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>caseCondExprTo</c>
	/// labeled alternative in <see cref="VBScriptParser.sC_CondExpr"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCaseCondExprTo([NotNull] VBScriptParser.CaseCondExprToContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>caseCondExprTo</c>
	/// labeled alternative in <see cref="VBScriptParser.sC_CondExpr"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCaseCondExprTo([NotNull] VBScriptParser.CaseCondExprToContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.sendkeysStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSendkeysStmt([NotNull] VBScriptParser.SendkeysStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.sendkeysStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSendkeysStmt([NotNull] VBScriptParser.SendkeysStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.setattrStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSetattrStmt([NotNull] VBScriptParser.SetattrStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.setattrStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSetattrStmt([NotNull] VBScriptParser.SetattrStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.setStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSetStmt([NotNull] VBScriptParser.SetStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.setStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSetStmt([NotNull] VBScriptParser.SetStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.stopStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterStopStmt([NotNull] VBScriptParser.StopStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.stopStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitStopStmt([NotNull] VBScriptParser.StopStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.subStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSubStmt([NotNull] VBScriptParser.SubStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.subStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSubStmt([NotNull] VBScriptParser.SubStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.timeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterTimeStmt([NotNull] VBScriptParser.TimeStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.timeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitTimeStmt([NotNull] VBScriptParser.TimeStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.typeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterTypeStmt([NotNull] VBScriptParser.TypeStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.typeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitTypeStmt([NotNull] VBScriptParser.TypeStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.typeStmt_Element"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterTypeStmt_Element([NotNull] VBScriptParser.TypeStmt_ElementContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.typeStmt_Element"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitTypeStmt_Element([NotNull] VBScriptParser.TypeStmt_ElementContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.typeOfStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterTypeOfStmt([NotNull] VBScriptParser.TypeOfStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.typeOfStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitTypeOfStmt([NotNull] VBScriptParser.TypeOfStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.unloadStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterUnloadStmt([NotNull] VBScriptParser.UnloadStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.unloadStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitUnloadStmt([NotNull] VBScriptParser.UnloadStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.unlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterUnlockStmt([NotNull] VBScriptParser.UnlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.unlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitUnlockStmt([NotNull] VBScriptParser.UnlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsAssign</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsAssign([NotNull] VBScriptParser.VsAssignContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsAssign</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsAssign([NotNull] VBScriptParser.VsAssignContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsStruct</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsStruct([NotNull] VBScriptParser.VsStructContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsStruct</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsStruct([NotNull] VBScriptParser.VsStructContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsAddressOf</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsAddressOf([NotNull] VBScriptParser.VsAddressOfContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsAddressOf</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsAddressOf([NotNull] VBScriptParser.VsAddressOfContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsTypeOf</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsTypeOf([NotNull] VBScriptParser.VsTypeOfContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsTypeOf</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsTypeOf([NotNull] VBScriptParser.VsTypeOfContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsNew</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsNew([NotNull] VBScriptParser.VsNewContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsNew</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsNew([NotNull] VBScriptParser.VsNewContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsICS</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsICS([NotNull] VBScriptParser.VsICSContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsICS</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsICS([NotNull] VBScriptParser.VsICSContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsLiteral</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsLiteral([NotNull] VBScriptParser.VsLiteralContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsLiteral</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsLiteral([NotNull] VBScriptParser.VsLiteralContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsUnaryOperation</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsUnaryOperation([NotNull] VBScriptParser.VsUnaryOperationContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsUnaryOperation</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsUnaryOperation([NotNull] VBScriptParser.VsUnaryOperationContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsDualOperation</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsDualOperation([NotNull] VBScriptParser.VsDualOperationContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsDualOperation</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsDualOperation([NotNull] VBScriptParser.VsDualOperationContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsMid</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsMid([NotNull] VBScriptParser.VsMidContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsMid</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsMid([NotNull] VBScriptParser.VsMidContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.variableStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVariableStmt([NotNull] VBScriptParser.VariableStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.variableStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVariableStmt([NotNull] VBScriptParser.VariableStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.variableListStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVariableListStmt([NotNull] VBScriptParser.VariableListStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.variableListStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVariableListStmt([NotNull] VBScriptParser.VariableListStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.variableSubStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVariableSubStmt([NotNull] VBScriptParser.VariableSubStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.variableSubStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVariableSubStmt([NotNull] VBScriptParser.VariableSubStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.whileWendStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterWhileWendStmt([NotNull] VBScriptParser.WhileWendStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.whileWendStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitWhileWendStmt([NotNull] VBScriptParser.WhileWendStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.widthStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterWidthStmt([NotNull] VBScriptParser.WidthStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.widthStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitWidthStmt([NotNull] VBScriptParser.WidthStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.withStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterWithStmt([NotNull] VBScriptParser.WithStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.withStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitWithStmt([NotNull] VBScriptParser.WithStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.writeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterWriteStmt([NotNull] VBScriptParser.WriteStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.writeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitWriteStmt([NotNull] VBScriptParser.WriteStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.explicitCallStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterExplicitCallStmt([NotNull] VBScriptParser.ExplicitCallStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.explicitCallStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitExplicitCallStmt([NotNull] VBScriptParser.ExplicitCallStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.eCS_ProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterECS_ProcedureCall([NotNull] VBScriptParser.ECS_ProcedureCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.eCS_ProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitECS_ProcedureCall([NotNull] VBScriptParser.ECS_ProcedureCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.eCS_MemberProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterECS_MemberProcedureCall([NotNull] VBScriptParser.ECS_MemberProcedureCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.eCS_MemberProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitECS_MemberProcedureCall([NotNull] VBScriptParser.ECS_MemberProcedureCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.implicitCallStmt_InBlock"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterImplicitCallStmt_InBlock([NotNull] VBScriptParser.ImplicitCallStmt_InBlockContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.implicitCallStmt_InBlock"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitImplicitCallStmt_InBlock([NotNull] VBScriptParser.ImplicitCallStmt_InBlockContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.noParenthesisArgs"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterNoParenthesisArgs([NotNull] VBScriptParser.NoParenthesisArgsContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.noParenthesisArgs"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitNoParenthesisArgs([NotNull] VBScriptParser.NoParenthesisArgsContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.iCS_B_ProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterICS_B_ProcedureCall([NotNull] VBScriptParser.ICS_B_ProcedureCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.iCS_B_ProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitICS_B_ProcedureCall([NotNull] VBScriptParser.ICS_B_ProcedureCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.iCS_B_MemberProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterICS_B_MemberProcedureCall([NotNull] VBScriptParser.ICS_B_MemberProcedureCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.iCS_B_MemberProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitICS_B_MemberProcedureCall([NotNull] VBScriptParser.ICS_B_MemberProcedureCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.implicitCallStmt_InStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterImplicitCallStmt_InStmt([NotNull] VBScriptParser.ImplicitCallStmt_InStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.implicitCallStmt_InStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitImplicitCallStmt_InStmt([NotNull] VBScriptParser.ImplicitCallStmt_InStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.iCS_S_VariableOrProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterICS_S_VariableOrProcedureCall([NotNull] VBScriptParser.ICS_S_VariableOrProcedureCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.iCS_S_VariableOrProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitICS_S_VariableOrProcedureCall([NotNull] VBScriptParser.ICS_S_VariableOrProcedureCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.iCS_S_ProcedureOrArrayCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterICS_S_ProcedureOrArrayCall([NotNull] VBScriptParser.ICS_S_ProcedureOrArrayCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.iCS_S_ProcedureOrArrayCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitICS_S_ProcedureOrArrayCall([NotNull] VBScriptParser.ICS_S_ProcedureOrArrayCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.iCS_S_NestedProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterICS_S_NestedProcedureCall([NotNull] VBScriptParser.ICS_S_NestedProcedureCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.iCS_S_NestedProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitICS_S_NestedProcedureCall([NotNull] VBScriptParser.ICS_S_NestedProcedureCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.iCS_S_MembersCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterICS_S_MembersCall([NotNull] VBScriptParser.ICS_S_MembersCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.iCS_S_MembersCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitICS_S_MembersCall([NotNull] VBScriptParser.ICS_S_MembersCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.iCS_S_MemberCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterICS_S_MemberCall([NotNull] VBScriptParser.ICS_S_MemberCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.iCS_S_MemberCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitICS_S_MemberCall([NotNull] VBScriptParser.ICS_S_MemberCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.iCS_S_DefaultMemberAccess"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterICS_S_DefaultMemberAccess([NotNull] VBScriptParser.ICS_S_DefaultMemberAccessContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.iCS_S_DefaultMemberAccess"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitICS_S_DefaultMemberAccess([NotNull] VBScriptParser.ICS_S_DefaultMemberAccessContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.argsCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterArgsCall([NotNull] VBScriptParser.ArgsCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.argsCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitArgsCall([NotNull] VBScriptParser.ArgsCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.argCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterArgCall([NotNull] VBScriptParser.ArgCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.argCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitArgCall([NotNull] VBScriptParser.ArgCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.defaultMemberAccess"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterDefaultMemberAccess([NotNull] VBScriptParser.DefaultMemberAccessContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.defaultMemberAccess"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitDefaultMemberAccess([NotNull] VBScriptParser.DefaultMemberAccessContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.argList"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterArgList([NotNull] VBScriptParser.ArgListContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.argList"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitArgList([NotNull] VBScriptParser.ArgListContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.arg"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterArg([NotNull] VBScriptParser.ArgContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.arg"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitArg([NotNull] VBScriptParser.ArgContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.argDefaultValue"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterArgDefaultValue([NotNull] VBScriptParser.ArgDefaultValueContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.argDefaultValue"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitArgDefaultValue([NotNull] VBScriptParser.ArgDefaultValueContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.subscripts"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSubscripts([NotNull] VBScriptParser.SubscriptsContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.subscripts"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSubscripts([NotNull] VBScriptParser.SubscriptsContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.subscript"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSubscript([NotNull] VBScriptParser.SubscriptContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.subscript"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSubscript([NotNull] VBScriptParser.SubscriptContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.ambiguousIdentifier"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterAmbiguousIdentifier([NotNull] VBScriptParser.AmbiguousIdentifierContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.ambiguousIdentifier"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitAmbiguousIdentifier([NotNull] VBScriptParser.AmbiguousIdentifierContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.asTypeClause"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterAsTypeClause([NotNull] VBScriptParser.AsTypeClauseContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.asTypeClause"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitAsTypeClause([NotNull] VBScriptParser.AsTypeClauseContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.baseType"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterBaseType([NotNull] VBScriptParser.BaseTypeContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.baseType"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitBaseType([NotNull] VBScriptParser.BaseTypeContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.certainIdentifier"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCertainIdentifier([NotNull] VBScriptParser.CertainIdentifierContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.certainIdentifier"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCertainIdentifier([NotNull] VBScriptParser.CertainIdentifierContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.comparisonOperator"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterComparisonOperator([NotNull] VBScriptParser.ComparisonOperatorContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.comparisonOperator"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitComparisonOperator([NotNull] VBScriptParser.ComparisonOperatorContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.complexType"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterComplexType([NotNull] VBScriptParser.ComplexTypeContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.complexType"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitComplexType([NotNull] VBScriptParser.ComplexTypeContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.fieldLength"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterFieldLength([NotNull] VBScriptParser.FieldLengthContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.fieldLength"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitFieldLength([NotNull] VBScriptParser.FieldLengthContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.letterrange"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLetterrange([NotNull] VBScriptParser.LetterrangeContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.letterrange"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLetterrange([NotNull] VBScriptParser.LetterrangeContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>ltColor</c>
	/// labeled alternative in <see cref="VBScriptParser.delimitedLiteral"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLtColor([NotNull] VBScriptParser.LtColorContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>ltColor</c>
	/// labeled alternative in <see cref="VBScriptParser.delimitedLiteral"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLtColor([NotNull] VBScriptParser.LtColorContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>ltOctal</c>
	/// labeled alternative in <see cref="VBScriptParser.delimitedLiteral"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLtOctal([NotNull] VBScriptParser.LtOctalContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>ltOctal</c>
	/// labeled alternative in <see cref="VBScriptParser.delimitedLiteral"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLtOctal([NotNull] VBScriptParser.LtOctalContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>ltDate</c>
	/// labeled alternative in <see cref="VBScriptParser.delimitedLiteral"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLtDate([NotNull] VBScriptParser.LtDateContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>ltDate</c>
	/// labeled alternative in <see cref="VBScriptParser.delimitedLiteral"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLtDate([NotNull] VBScriptParser.LtDateContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>ltString</c>
	/// labeled alternative in <see cref="VBScriptParser.delimitedLiteral"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLtString([NotNull] VBScriptParser.LtStringContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>ltString</c>
	/// labeled alternative in <see cref="VBScriptParser.delimitedLiteral"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLtString([NotNull] VBScriptParser.LtStringContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>ltDouble</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLtDouble([NotNull] VBScriptParser.LtDoubleContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>ltDouble</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLtDouble([NotNull] VBScriptParser.LtDoubleContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>ltDelimited</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLtDelimited([NotNull] VBScriptParser.LtDelimitedContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>ltDelimited</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLtDelimited([NotNull] VBScriptParser.LtDelimitedContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>ltFilenumber</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLtFilenumber([NotNull] VBScriptParser.LtFilenumberContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>ltFilenumber</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLtFilenumber([NotNull] VBScriptParser.LtFilenumberContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>ltInteger</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLtInteger([NotNull] VBScriptParser.LtIntegerContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>ltInteger</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLtInteger([NotNull] VBScriptParser.LtIntegerContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>ltBoolean</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLtBoolean([NotNull] VBScriptParser.LtBooleanContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>ltBoolean</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLtBoolean([NotNull] VBScriptParser.LtBooleanContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>ltNothing</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLtNothing([NotNull] VBScriptParser.LtNothingContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>ltNothing</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLtNothing([NotNull] VBScriptParser.LtNothingContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>ltNull</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLtNull([NotNull] VBScriptParser.LtNullContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>ltNull</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLtNull([NotNull] VBScriptParser.LtNullContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.publicPrivateVisibility"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterPublicPrivateVisibility([NotNull] VBScriptParser.PublicPrivateVisibilityContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.publicPrivateVisibility"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitPublicPrivateVisibility([NotNull] VBScriptParser.PublicPrivateVisibilityContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.publicPrivateGlobalVisibility"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterPublicPrivateGlobalVisibility([NotNull] VBScriptParser.PublicPrivateGlobalVisibilityContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.publicPrivateGlobalVisibility"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitPublicPrivateGlobalVisibility([NotNull] VBScriptParser.PublicPrivateGlobalVisibilityContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.type"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterType([NotNull] VBScriptParser.TypeContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.type"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitType([NotNull] VBScriptParser.TypeContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.typeHint"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterTypeHint([NotNull] VBScriptParser.TypeHintContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.typeHint"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitTypeHint([NotNull] VBScriptParser.TypeHintContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.visibility"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVisibility([NotNull] VBScriptParser.VisibilityContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.visibility"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVisibility([NotNull] VBScriptParser.VisibilityContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.ambiguousKeyword"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterAmbiguousKeyword([NotNull] VBScriptParser.AmbiguousKeywordContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.ambiguousKeyword"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitAmbiguousKeyword([NotNull] VBScriptParser.AmbiguousKeywordContext context) { }

	/// <inheritdoc/>
	/// <remarks>The default implementation does nothing.</remarks>
	public virtual void EnterEveryRule([NotNull] ParserRuleContext context) { }
	/// <inheritdoc/>
	/// <remarks>The default implementation does nothing.</remarks>
	public virtual void ExitEveryRule([NotNull] ParserRuleContext context) { }
	/// <inheritdoc/>
	/// <remarks>The default implementation does nothing.</remarks>
	public virtual void VisitTerminal([NotNull] ITerminalNode node) { }
	/// <inheritdoc/>
	/// <remarks>The default implementation does nothing.</remarks>
	public virtual void VisitErrorNode([NotNull] IErrorNode node) { }
}
