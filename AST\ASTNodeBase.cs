using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace VbDeobf.AST
{
    /// <summary>
    /// Base implementation for all AST nodes
    /// </summary>
    public abstract class ASTNodeBase : IASTNode
    {
        private readonly List<IASTNode> _children = new List<IASTNode>();
        
        public abstract ASTNodeType NodeType { get; }
        public IASTNode? Parent { get; set; }
        public IReadOnlyList<IASTNode> Children => _children.AsReadOnly();
        public SourceLocation? SourceLocation { get; set; }
        
        protected ASTNodeBase(SourceLocation? sourceLocation = null)
        {
            SourceLocation = sourceLocation;
        }
        
        public abstract T Accept<T>(IASTVisitor<T> visitor);
        
        public virtual void AddChild(IASTNode child)
        {
            if (child == null) return;
            
            child.Parent = this;
            _children.Add(child);
        }
        
        public virtual bool RemoveChild(IASTNode child)
        {
            if (child == null) return false;
            
            var removed = _children.Remove(child);
            if (removed)
            {
                child.Parent = null;
            }
            return removed;
        }
        
        public virtual bool ReplaceChild(IASTNode oldChild, IASTNode newChild)
        {
            if (oldChild == null || newChild == null) return false;
            
            var index = _children.IndexOf(oldChild);
            if (index == -1) return false;
            
            oldChild.Parent = null;
            newChild.Parent = this;
            _children[index] = newChild;
            return true;
        }
        
        public virtual string ToDebugString()
        {
            var sb = new StringBuilder();
            ToDebugString(sb, 0);
            return sb.ToString();
        }
        
        protected virtual void ToDebugString(StringBuilder sb, int indent)
        {
            var indentStr = new string(' ', indent * 2);
            sb.AppendLine($"{indentStr}{GetType().Name}");
            
            foreach (var child in Children)
            {
                if (child is ASTNodeBase astChild)
                {
                    astChild.ToDebugString(sb, indent + 1);
                }
                else
                {
                    sb.AppendLine($"{indentStr}  {child.GetType().Name}");
                }
            }
        }
        
        /// <summary>
        /// Find the first ancestor of the specified type
        /// </summary>
        /// <typeparam name="T">Type of ancestor to find</typeparam>
        /// <returns>Ancestor of specified type, or null if not found</returns>
        public T? FindAncestor<T>() where T : class, IASTNode
        {
            var current = Parent;
            while (current != null)
            {
                if (current is T result)
                    return result;
                current = current.Parent;
            }
            return null;
        }
        
        /// <summary>
        /// Find all descendants of the specified type
        /// </summary>
        /// <typeparam name="T">Type of descendants to find</typeparam>
        /// <returns>All descendants of specified type</returns>
        public IEnumerable<T> FindDescendants<T>() where T : class, IASTNode
        {
            foreach (var child in Children)
            {
                if (child is T result)
                    yield return result;
                
                if (child is ASTNodeBase astChild)
                {
                    foreach (var descendant in astChild.FindDescendants<T>())
                        yield return descendant;
                }
            }
        }
        
        /// <summary>
        /// Find the first descendant of the specified type
        /// </summary>
        /// <typeparam name="T">Type of descendant to find</typeparam>
        /// <returns>First descendant of specified type, or null if not found</returns>
        public T? FindDescendant<T>() where T : class, IASTNode
        {
            return FindDescendants<T>().FirstOrDefault();
        }
    }
    
    /// <summary>
    /// Base class for expression nodes
    /// </summary>
    public abstract class ExpressionNode : ASTNodeBase
    {
        protected ExpressionNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }
        
        /// <summary>
        /// The data type of this expression (if known)
        /// </summary>
        public VBAType? ExpressionType { get; set; }
    }
    
    /// <summary>
    /// Base class for statement nodes
    /// </summary>
    public abstract class StatementNode : ASTNodeBase
    {
        protected StatementNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }
    }
    
    /// <summary>
    /// Base class for declaration nodes
    /// </summary>
    public abstract class DeclarationNode : ASTNodeBase
    {
        protected DeclarationNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }
        
        /// <summary>
        /// The name being declared
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// Visibility modifier (Public, Private, Friend, etc.)
        /// </summary>
        public VisibilityModifier Visibility { get; set; } = VisibilityModifier.Public;
    }
    
    /// <summary>
    /// Represents VBA data types
    /// </summary>
    public enum VBAType
    {
        Unknown,
        Boolean,
        Byte,
        Integer,
        Long,
        LongLong,
        Single,
        Double,
        Currency,
        Date,
        String,
        Object,
        Variant,
        UserDefined
    }
    
    /// <summary>
    /// Visibility modifiers for declarations
    /// </summary>
    public enum VisibilityModifier
    {
        Public,
        Private,
        Friend,
        Global
    }
    
    /// <summary>
    /// Binary operators
    /// </summary>
    public enum BinaryOperator
    {
        // Arithmetic
        Add,
        Subtract,
        Multiply,
        Divide,
        IntegerDivide,
        Modulo,
        Power,
        
        // String
        Concatenate,
        
        // Comparison
        Equal,
        NotEqual,
        LessThan,
        LessThanOrEqual,
        GreaterThan,
        GreaterThanOrEqual,
        Like,
        Is,
        
        // Logical
        And,
        Or,
        Xor,
        Eqv,
        Imp
    }
    
    /// <summary>
    /// Unary operators
    /// </summary>
    public enum UnaryOperator
    {
        Plus,
        Minus,
        Not
    }
}
