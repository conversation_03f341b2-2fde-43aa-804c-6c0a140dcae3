//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     ANTLR Version: 4.13.1
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// Generated from VBScript.g4 by ANTLR 4.13.1

// Unreachable code detected
#pragma warning disable 0162
// The variable '...' is assigned but its value is never used
#pragma warning disable 0219
// Missing XML comment for publicly visible type or member '...'
#pragma warning disable 1591
// Ambiguous reference in cref attribute
#pragma warning disable 419

using Antlr4.Runtime.Misc;
using IParseTreeListener = Antlr4.Runtime.Tree.IParseTreeListener;
using IToken = Antlr4.Runtime.IToken;

/// <summary>
/// This interface defines a complete listener for a parse tree produced by
/// <see cref="VBScriptParser"/>.
/// </summary>
[System.CodeDom.Compiler.GeneratedCode("ANTLR", "4.13.1")]
[System.CLSCompliant(false)]
public interface IVBScriptListener : IParseTreeListener {
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.startRule"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterStartRule([NotNull] VBScriptParser.StartRuleContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.startRule"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitStartRule([NotNull] VBScriptParser.StartRuleContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.module"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModule([NotNull] VBScriptParser.ModuleContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.module"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModule([NotNull] VBScriptParser.ModuleContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleReferences"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleReferences([NotNull] VBScriptParser.ModuleReferencesContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleReferences"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleReferences([NotNull] VBScriptParser.ModuleReferencesContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleReference"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleReference([NotNull] VBScriptParser.ModuleReferenceContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleReference"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleReference([NotNull] VBScriptParser.ModuleReferenceContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleReferenceValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleReferenceValue([NotNull] VBScriptParser.ModuleReferenceValueContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleReferenceValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleReferenceValue([NotNull] VBScriptParser.ModuleReferenceValueContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleReferenceComponent"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleReferenceComponent([NotNull] VBScriptParser.ModuleReferenceComponentContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleReferenceComponent"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleReferenceComponent([NotNull] VBScriptParser.ModuleReferenceComponentContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleHeader"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleHeader([NotNull] VBScriptParser.ModuleHeaderContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleHeader"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleHeader([NotNull] VBScriptParser.ModuleHeaderContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleConfig"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleConfig([NotNull] VBScriptParser.ModuleConfigContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleConfig"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleConfig([NotNull] VBScriptParser.ModuleConfigContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleConfigElement"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleConfigElement([NotNull] VBScriptParser.ModuleConfigElementContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleConfigElement"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleConfigElement([NotNull] VBScriptParser.ModuleConfigElementContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleAttributes"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleAttributes([NotNull] VBScriptParser.ModuleAttributesContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleAttributes"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleAttributes([NotNull] VBScriptParser.ModuleAttributesContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleOptions"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleOptions([NotNull] VBScriptParser.ModuleOptionsContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleOptions"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleOptions([NotNull] VBScriptParser.ModuleOptionsContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>optionBaseStmt</c>
	/// labeled alternative in <see cref="VBScriptParser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterOptionBaseStmt([NotNull] VBScriptParser.OptionBaseStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>optionBaseStmt</c>
	/// labeled alternative in <see cref="VBScriptParser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitOptionBaseStmt([NotNull] VBScriptParser.OptionBaseStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>optionCompareStmt</c>
	/// labeled alternative in <see cref="VBScriptParser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterOptionCompareStmt([NotNull] VBScriptParser.OptionCompareStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>optionCompareStmt</c>
	/// labeled alternative in <see cref="VBScriptParser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitOptionCompareStmt([NotNull] VBScriptParser.OptionCompareStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>optionExplicitStmt</c>
	/// labeled alternative in <see cref="VBScriptParser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterOptionExplicitStmt([NotNull] VBScriptParser.OptionExplicitStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>optionExplicitStmt</c>
	/// labeled alternative in <see cref="VBScriptParser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitOptionExplicitStmt([NotNull] VBScriptParser.OptionExplicitStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>optionPrivateModuleStmt</c>
	/// labeled alternative in <see cref="VBScriptParser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterOptionPrivateModuleStmt([NotNull] VBScriptParser.OptionPrivateModuleStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>optionPrivateModuleStmt</c>
	/// labeled alternative in <see cref="VBScriptParser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitOptionPrivateModuleStmt([NotNull] VBScriptParser.OptionPrivateModuleStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleBody"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleBody([NotNull] VBScriptParser.ModuleBodyContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleBody"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleBody([NotNull] VBScriptParser.ModuleBodyContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleBodyElement"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleBodyElement([NotNull] VBScriptParser.ModuleBodyElementContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleBodyElement"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleBodyElement([NotNull] VBScriptParser.ModuleBodyElementContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.controlProperties"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterControlProperties([NotNull] VBScriptParser.ControlPropertiesContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.controlProperties"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitControlProperties([NotNull] VBScriptParser.ControlPropertiesContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.cp_Properties"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCp_Properties([NotNull] VBScriptParser.Cp_PropertiesContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.cp_Properties"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCp_Properties([NotNull] VBScriptParser.Cp_PropertiesContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.cp_PropertyName"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCp_PropertyName([NotNull] VBScriptParser.Cp_PropertyNameContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.cp_PropertyName"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCp_PropertyName([NotNull] VBScriptParser.Cp_PropertyNameContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.cp_PropertyValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCp_PropertyValue([NotNull] VBScriptParser.Cp_PropertyValueContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.cp_PropertyValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCp_PropertyValue([NotNull] VBScriptParser.Cp_PropertyValueContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.cp_NestedProperty"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCp_NestedProperty([NotNull] VBScriptParser.Cp_NestedPropertyContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.cp_NestedProperty"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCp_NestedProperty([NotNull] VBScriptParser.Cp_NestedPropertyContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.cp_ControlType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCp_ControlType([NotNull] VBScriptParser.Cp_ControlTypeContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.cp_ControlType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCp_ControlType([NotNull] VBScriptParser.Cp_ControlTypeContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.cp_ControlIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCp_ControlIdentifier([NotNull] VBScriptParser.Cp_ControlIdentifierContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.cp_ControlIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCp_ControlIdentifier([NotNull] VBScriptParser.Cp_ControlIdentifierContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.blockSwitch"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterBlockSwitch([NotNull] VBScriptParser.BlockSwitchContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.blockSwitch"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitBlockSwitch([NotNull] VBScriptParser.BlockSwitchContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.block"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterBlock([NotNull] VBScriptParser.BlockContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.block"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitBlock([NotNull] VBScriptParser.BlockContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.moduleBlock"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleBlock([NotNull] VBScriptParser.ModuleBlockContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.moduleBlock"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleBlock([NotNull] VBScriptParser.ModuleBlockContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.attributeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterAttributeStmt([NotNull] VBScriptParser.AttributeStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.attributeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitAttributeStmt([NotNull] VBScriptParser.AttributeStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.lineLabel"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLineLabel([NotNull] VBScriptParser.LineLabelContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.lineLabel"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLineLabel([NotNull] VBScriptParser.LineLabelContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.inlineBlock"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterInlineBlock([NotNull] VBScriptParser.InlineBlockContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.inlineBlock"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitInlineBlock([NotNull] VBScriptParser.InlineBlockContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.inlineBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterInlineBlockStmt([NotNull] VBScriptParser.InlineBlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.inlineBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitInlineBlockStmt([NotNull] VBScriptParser.InlineBlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.blockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterBlockStmt([NotNull] VBScriptParser.BlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.blockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitBlockStmt([NotNull] VBScriptParser.BlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.appActivateStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterAppActivateStmt([NotNull] VBScriptParser.AppActivateStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.appActivateStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitAppActivateStmt([NotNull] VBScriptParser.AppActivateStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.beepStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterBeepStmt([NotNull] VBScriptParser.BeepStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.beepStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitBeepStmt([NotNull] VBScriptParser.BeepStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.chDirStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterChDirStmt([NotNull] VBScriptParser.ChDirStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.chDirStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitChDirStmt([NotNull] VBScriptParser.ChDirStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.chDriveStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterChDriveStmt([NotNull] VBScriptParser.ChDriveStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.chDriveStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitChDriveStmt([NotNull] VBScriptParser.ChDriveStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.closeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCloseStmt([NotNull] VBScriptParser.CloseStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.closeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCloseStmt([NotNull] VBScriptParser.CloseStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.constStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterConstStmt([NotNull] VBScriptParser.ConstStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.constStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitConstStmt([NotNull] VBScriptParser.ConstStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.constSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterConstSubStmt([NotNull] VBScriptParser.ConstSubStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.constSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitConstSubStmt([NotNull] VBScriptParser.ConstSubStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.dateStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterDateStmt([NotNull] VBScriptParser.DateStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.dateStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitDateStmt([NotNull] VBScriptParser.DateStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.declareStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterDeclareStmt([NotNull] VBScriptParser.DeclareStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.declareStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitDeclareStmt([NotNull] VBScriptParser.DeclareStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.deftypeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterDeftypeStmt([NotNull] VBScriptParser.DeftypeStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.deftypeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitDeftypeStmt([NotNull] VBScriptParser.DeftypeStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.deleteSettingStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterDeleteSettingStmt([NotNull] VBScriptParser.DeleteSettingStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.deleteSettingStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitDeleteSettingStmt([NotNull] VBScriptParser.DeleteSettingStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>dlStatement</c>
	/// labeled alternative in <see cref="VBScriptParser.doLoopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterDlStatement([NotNull] VBScriptParser.DlStatementContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>dlStatement</c>
	/// labeled alternative in <see cref="VBScriptParser.doLoopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitDlStatement([NotNull] VBScriptParser.DlStatementContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>dwlStatement</c>
	/// labeled alternative in <see cref="VBScriptParser.doLoopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterDwlStatement([NotNull] VBScriptParser.DwlStatementContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>dwlStatement</c>
	/// labeled alternative in <see cref="VBScriptParser.doLoopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitDwlStatement([NotNull] VBScriptParser.DwlStatementContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>dlwStatement</c>
	/// labeled alternative in <see cref="VBScriptParser.doLoopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterDlwStatement([NotNull] VBScriptParser.DlwStatementContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>dlwStatement</c>
	/// labeled alternative in <see cref="VBScriptParser.doLoopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitDlwStatement([NotNull] VBScriptParser.DlwStatementContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.endStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterEndStmt([NotNull] VBScriptParser.EndStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.endStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitEndStmt([NotNull] VBScriptParser.EndStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.enumerationStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterEnumerationStmt([NotNull] VBScriptParser.EnumerationStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.enumerationStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitEnumerationStmt([NotNull] VBScriptParser.EnumerationStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.classStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterClassStmt([NotNull] VBScriptParser.ClassStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.classStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitClassStmt([NotNull] VBScriptParser.ClassStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.enumerationStmt_Constant"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterEnumerationStmt_Constant([NotNull] VBScriptParser.EnumerationStmt_ConstantContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.enumerationStmt_Constant"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitEnumerationStmt_Constant([NotNull] VBScriptParser.EnumerationStmt_ConstantContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.eraseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterEraseStmt([NotNull] VBScriptParser.EraseStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.eraseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitEraseStmt([NotNull] VBScriptParser.EraseStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.errorStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterErrorStmt([NotNull] VBScriptParser.ErrorStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.errorStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitErrorStmt([NotNull] VBScriptParser.ErrorStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.eventStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterEventStmt([NotNull] VBScriptParser.EventStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.eventStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitEventStmt([NotNull] VBScriptParser.EventStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.exitStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterExitStmt([NotNull] VBScriptParser.ExitStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.exitStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitExitStmt([NotNull] VBScriptParser.ExitStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.filecopyStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterFilecopyStmt([NotNull] VBScriptParser.FilecopyStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.filecopyStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitFilecopyStmt([NotNull] VBScriptParser.FilecopyStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.forEachStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterForEachStmt([NotNull] VBScriptParser.ForEachStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.forEachStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitForEachStmt([NotNull] VBScriptParser.ForEachStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.forNextStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterForNextStmt([NotNull] VBScriptParser.ForNextStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.forNextStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitForNextStmt([NotNull] VBScriptParser.ForNextStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.functionStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterFunctionStmt([NotNull] VBScriptParser.FunctionStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.functionStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitFunctionStmt([NotNull] VBScriptParser.FunctionStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.getStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterGetStmt([NotNull] VBScriptParser.GetStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.getStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitGetStmt([NotNull] VBScriptParser.GetStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.goSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterGoSubStmt([NotNull] VBScriptParser.GoSubStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.goSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitGoSubStmt([NotNull] VBScriptParser.GoSubStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.goToStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterGoToStmt([NotNull] VBScriptParser.GoToStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.goToStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitGoToStmt([NotNull] VBScriptParser.GoToStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>blockIfThenElse</c>
	/// labeled alternative in <see cref="VBScriptParser.ifThenElseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterBlockIfThenElse([NotNull] VBScriptParser.BlockIfThenElseContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>blockIfThenElse</c>
	/// labeled alternative in <see cref="VBScriptParser.ifThenElseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitBlockIfThenElse([NotNull] VBScriptParser.BlockIfThenElseContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>inlineIfThenElse</c>
	/// labeled alternative in <see cref="VBScriptParser.inlineIfThenElseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterInlineIfThenElse([NotNull] VBScriptParser.InlineIfThenElseContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>inlineIfThenElse</c>
	/// labeled alternative in <see cref="VBScriptParser.inlineIfThenElseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitInlineIfThenElse([NotNull] VBScriptParser.InlineIfThenElseContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.inlineIfBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterInlineIfBlockStmt([NotNull] VBScriptParser.InlineIfBlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.inlineIfBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitInlineIfBlockStmt([NotNull] VBScriptParser.InlineIfBlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.inlineElseBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterInlineElseBlockStmt([NotNull] VBScriptParser.InlineElseBlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.inlineElseBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitInlineElseBlockStmt([NotNull] VBScriptParser.InlineElseBlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.ifBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterIfBlockStmt([NotNull] VBScriptParser.IfBlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.ifBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitIfBlockStmt([NotNull] VBScriptParser.IfBlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.ifConditionStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterIfConditionStmt([NotNull] VBScriptParser.IfConditionStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.ifConditionStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitIfConditionStmt([NotNull] VBScriptParser.IfConditionStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.ifElseIfBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterIfElseIfBlockStmt([NotNull] VBScriptParser.IfElseIfBlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.ifElseIfBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitIfElseIfBlockStmt([NotNull] VBScriptParser.IfElseIfBlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.ifElseBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterIfElseBlockStmt([NotNull] VBScriptParser.IfElseBlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.ifElseBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitIfElseBlockStmt([NotNull] VBScriptParser.IfElseBlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.implementsStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterImplementsStmt([NotNull] VBScriptParser.ImplementsStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.implementsStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitImplementsStmt([NotNull] VBScriptParser.ImplementsStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.inputStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterInputStmt([NotNull] VBScriptParser.InputStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.inputStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitInputStmt([NotNull] VBScriptParser.InputStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.killStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterKillStmt([NotNull] VBScriptParser.KillStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.killStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitKillStmt([NotNull] VBScriptParser.KillStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.midStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterMidStmt([NotNull] VBScriptParser.MidStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.midStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitMidStmt([NotNull] VBScriptParser.MidStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.letStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLetStmt([NotNull] VBScriptParser.LetStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.letStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLetStmt([NotNull] VBScriptParser.LetStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.lineInputStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLineInputStmt([NotNull] VBScriptParser.LineInputStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.lineInputStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLineInputStmt([NotNull] VBScriptParser.LineInputStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.loadStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLoadStmt([NotNull] VBScriptParser.LoadStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.loadStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLoadStmt([NotNull] VBScriptParser.LoadStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.lockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLockStmt([NotNull] VBScriptParser.LockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.lockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLockStmt([NotNull] VBScriptParser.LockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.lsetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLsetStmt([NotNull] VBScriptParser.LsetStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.lsetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLsetStmt([NotNull] VBScriptParser.LsetStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.macroIfThenElseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterMacroIfThenElseStmt([NotNull] VBScriptParser.MacroIfThenElseStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.macroIfThenElseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitMacroIfThenElseStmt([NotNull] VBScriptParser.MacroIfThenElseStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.macroIfBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterMacroIfBlockStmt([NotNull] VBScriptParser.MacroIfBlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.macroIfBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitMacroIfBlockStmt([NotNull] VBScriptParser.MacroIfBlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.macroElseIfBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterMacroElseIfBlockStmt([NotNull] VBScriptParser.MacroElseIfBlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.macroElseIfBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitMacroElseIfBlockStmt([NotNull] VBScriptParser.MacroElseIfBlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.macroElseBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterMacroElseBlockStmt([NotNull] VBScriptParser.MacroElseBlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.macroElseBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitMacroElseBlockStmt([NotNull] VBScriptParser.MacroElseBlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.mkdirStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterMkdirStmt([NotNull] VBScriptParser.MkdirStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.mkdirStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitMkdirStmt([NotNull] VBScriptParser.MkdirStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.nameStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterNameStmt([NotNull] VBScriptParser.NameStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.nameStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitNameStmt([NotNull] VBScriptParser.NameStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.onErrorStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterOnErrorStmt([NotNull] VBScriptParser.OnErrorStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.onErrorStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitOnErrorStmt([NotNull] VBScriptParser.OnErrorStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.onGoToStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterOnGoToStmt([NotNull] VBScriptParser.OnGoToStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.onGoToStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitOnGoToStmt([NotNull] VBScriptParser.OnGoToStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.onGoSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterOnGoSubStmt([NotNull] VBScriptParser.OnGoSubStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.onGoSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitOnGoSubStmt([NotNull] VBScriptParser.OnGoSubStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.openStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterOpenStmt([NotNull] VBScriptParser.OpenStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.openStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitOpenStmt([NotNull] VBScriptParser.OpenStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.outputList"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterOutputList([NotNull] VBScriptParser.OutputListContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.outputList"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitOutputList([NotNull] VBScriptParser.OutputListContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.outputList_Expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterOutputList_Expression([NotNull] VBScriptParser.OutputList_ExpressionContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.outputList_Expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitOutputList_Expression([NotNull] VBScriptParser.OutputList_ExpressionContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.printStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterPrintStmt([NotNull] VBScriptParser.PrintStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.printStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitPrintStmt([NotNull] VBScriptParser.PrintStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.propertyGetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterPropertyGetStmt([NotNull] VBScriptParser.PropertyGetStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.propertyGetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitPropertyGetStmt([NotNull] VBScriptParser.PropertyGetStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.propertySetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterPropertySetStmt([NotNull] VBScriptParser.PropertySetStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.propertySetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitPropertySetStmt([NotNull] VBScriptParser.PropertySetStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.propertyLetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterPropertyLetStmt([NotNull] VBScriptParser.PropertyLetStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.propertyLetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitPropertyLetStmt([NotNull] VBScriptParser.PropertyLetStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.putStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterPutStmt([NotNull] VBScriptParser.PutStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.putStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitPutStmt([NotNull] VBScriptParser.PutStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.raiseEventStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterRaiseEventStmt([NotNull] VBScriptParser.RaiseEventStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.raiseEventStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitRaiseEventStmt([NotNull] VBScriptParser.RaiseEventStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.randomizeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterRandomizeStmt([NotNull] VBScriptParser.RandomizeStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.randomizeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitRandomizeStmt([NotNull] VBScriptParser.RandomizeStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.redimStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterRedimStmt([NotNull] VBScriptParser.RedimStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.redimStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitRedimStmt([NotNull] VBScriptParser.RedimStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.redimSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterRedimSubStmt([NotNull] VBScriptParser.RedimSubStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.redimSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitRedimSubStmt([NotNull] VBScriptParser.RedimSubStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.resetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterResetStmt([NotNull] VBScriptParser.ResetStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.resetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitResetStmt([NotNull] VBScriptParser.ResetStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.resumeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterResumeStmt([NotNull] VBScriptParser.ResumeStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.resumeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitResumeStmt([NotNull] VBScriptParser.ResumeStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.returnStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterReturnStmt([NotNull] VBScriptParser.ReturnStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.returnStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitReturnStmt([NotNull] VBScriptParser.ReturnStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.rmdirStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterRmdirStmt([NotNull] VBScriptParser.RmdirStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.rmdirStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitRmdirStmt([NotNull] VBScriptParser.RmdirStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.rsetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterRsetStmt([NotNull] VBScriptParser.RsetStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.rsetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitRsetStmt([NotNull] VBScriptParser.RsetStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.savepictureStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSavepictureStmt([NotNull] VBScriptParser.SavepictureStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.savepictureStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSavepictureStmt([NotNull] VBScriptParser.SavepictureStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.saveSettingStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSaveSettingStmt([NotNull] VBScriptParser.SaveSettingStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.saveSettingStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSaveSettingStmt([NotNull] VBScriptParser.SaveSettingStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.seekStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSeekStmt([NotNull] VBScriptParser.SeekStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.seekStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSeekStmt([NotNull] VBScriptParser.SeekStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.selectCaseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSelectCaseStmt([NotNull] VBScriptParser.SelectCaseStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.selectCaseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSelectCaseStmt([NotNull] VBScriptParser.SelectCaseStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.sC_Case"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSC_Case([NotNull] VBScriptParser.SC_CaseContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.sC_Case"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSC_Case([NotNull] VBScriptParser.SC_CaseContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>caseCondElse</c>
	/// labeled alternative in <see cref="VBScriptParser.sC_Cond"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCaseCondElse([NotNull] VBScriptParser.CaseCondElseContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>caseCondElse</c>
	/// labeled alternative in <see cref="VBScriptParser.sC_Cond"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCaseCondElse([NotNull] VBScriptParser.CaseCondElseContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>caseCondExpr</c>
	/// labeled alternative in <see cref="VBScriptParser.sC_Cond"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCaseCondExpr([NotNull] VBScriptParser.CaseCondExprContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>caseCondExpr</c>
	/// labeled alternative in <see cref="VBScriptParser.sC_Cond"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCaseCondExpr([NotNull] VBScriptParser.CaseCondExprContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>caseCondExprIs</c>
	/// labeled alternative in <see cref="VBScriptParser.sC_CondExpr"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCaseCondExprIs([NotNull] VBScriptParser.CaseCondExprIsContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>caseCondExprIs</c>
	/// labeled alternative in <see cref="VBScriptParser.sC_CondExpr"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCaseCondExprIs([NotNull] VBScriptParser.CaseCondExprIsContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>caseCondExprValue</c>
	/// labeled alternative in <see cref="VBScriptParser.sC_CondExpr"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCaseCondExprValue([NotNull] VBScriptParser.CaseCondExprValueContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>caseCondExprValue</c>
	/// labeled alternative in <see cref="VBScriptParser.sC_CondExpr"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCaseCondExprValue([NotNull] VBScriptParser.CaseCondExprValueContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>caseCondExprTo</c>
	/// labeled alternative in <see cref="VBScriptParser.sC_CondExpr"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCaseCondExprTo([NotNull] VBScriptParser.CaseCondExprToContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>caseCondExprTo</c>
	/// labeled alternative in <see cref="VBScriptParser.sC_CondExpr"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCaseCondExprTo([NotNull] VBScriptParser.CaseCondExprToContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.sendkeysStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSendkeysStmt([NotNull] VBScriptParser.SendkeysStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.sendkeysStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSendkeysStmt([NotNull] VBScriptParser.SendkeysStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.setattrStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSetattrStmt([NotNull] VBScriptParser.SetattrStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.setattrStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSetattrStmt([NotNull] VBScriptParser.SetattrStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.setStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSetStmt([NotNull] VBScriptParser.SetStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.setStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSetStmt([NotNull] VBScriptParser.SetStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.stopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterStopStmt([NotNull] VBScriptParser.StopStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.stopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitStopStmt([NotNull] VBScriptParser.StopStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.subStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSubStmt([NotNull] VBScriptParser.SubStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.subStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSubStmt([NotNull] VBScriptParser.SubStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.timeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterTimeStmt([NotNull] VBScriptParser.TimeStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.timeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitTimeStmt([NotNull] VBScriptParser.TimeStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.typeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterTypeStmt([NotNull] VBScriptParser.TypeStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.typeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitTypeStmt([NotNull] VBScriptParser.TypeStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.typeStmt_Element"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterTypeStmt_Element([NotNull] VBScriptParser.TypeStmt_ElementContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.typeStmt_Element"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitTypeStmt_Element([NotNull] VBScriptParser.TypeStmt_ElementContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.typeOfStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterTypeOfStmt([NotNull] VBScriptParser.TypeOfStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.typeOfStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitTypeOfStmt([NotNull] VBScriptParser.TypeOfStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.unloadStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterUnloadStmt([NotNull] VBScriptParser.UnloadStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.unloadStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitUnloadStmt([NotNull] VBScriptParser.UnloadStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.unlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterUnlockStmt([NotNull] VBScriptParser.UnlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.unlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitUnlockStmt([NotNull] VBScriptParser.UnlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsAssign</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsAssign([NotNull] VBScriptParser.VsAssignContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsAssign</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsAssign([NotNull] VBScriptParser.VsAssignContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsStruct</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsStruct([NotNull] VBScriptParser.VsStructContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsStruct</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsStruct([NotNull] VBScriptParser.VsStructContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsAddressOf</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsAddressOf([NotNull] VBScriptParser.VsAddressOfContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsAddressOf</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsAddressOf([NotNull] VBScriptParser.VsAddressOfContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsTypeOf</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsTypeOf([NotNull] VBScriptParser.VsTypeOfContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsTypeOf</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsTypeOf([NotNull] VBScriptParser.VsTypeOfContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsNew</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsNew([NotNull] VBScriptParser.VsNewContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsNew</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsNew([NotNull] VBScriptParser.VsNewContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsICS</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsICS([NotNull] VBScriptParser.VsICSContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsICS</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsICS([NotNull] VBScriptParser.VsICSContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsLiteral</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsLiteral([NotNull] VBScriptParser.VsLiteralContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsLiteral</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsLiteral([NotNull] VBScriptParser.VsLiteralContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsUnaryOperation</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsUnaryOperation([NotNull] VBScriptParser.VsUnaryOperationContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsUnaryOperation</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsUnaryOperation([NotNull] VBScriptParser.VsUnaryOperationContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsDualOperation</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsDualOperation([NotNull] VBScriptParser.VsDualOperationContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsDualOperation</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsDualOperation([NotNull] VBScriptParser.VsDualOperationContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsMid</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsMid([NotNull] VBScriptParser.VsMidContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsMid</c>
	/// labeled alternative in <see cref="VBScriptParser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsMid([NotNull] VBScriptParser.VsMidContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.variableStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVariableStmt([NotNull] VBScriptParser.VariableStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.variableStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVariableStmt([NotNull] VBScriptParser.VariableStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.variableListStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVariableListStmt([NotNull] VBScriptParser.VariableListStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.variableListStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVariableListStmt([NotNull] VBScriptParser.VariableListStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.variableSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVariableSubStmt([NotNull] VBScriptParser.VariableSubStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.variableSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVariableSubStmt([NotNull] VBScriptParser.VariableSubStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.whileWendStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterWhileWendStmt([NotNull] VBScriptParser.WhileWendStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.whileWendStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitWhileWendStmt([NotNull] VBScriptParser.WhileWendStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.widthStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterWidthStmt([NotNull] VBScriptParser.WidthStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.widthStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitWidthStmt([NotNull] VBScriptParser.WidthStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.withStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterWithStmt([NotNull] VBScriptParser.WithStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.withStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitWithStmt([NotNull] VBScriptParser.WithStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.writeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterWriteStmt([NotNull] VBScriptParser.WriteStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.writeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitWriteStmt([NotNull] VBScriptParser.WriteStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.explicitCallStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterExplicitCallStmt([NotNull] VBScriptParser.ExplicitCallStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.explicitCallStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitExplicitCallStmt([NotNull] VBScriptParser.ExplicitCallStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.eCS_ProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterECS_ProcedureCall([NotNull] VBScriptParser.ECS_ProcedureCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.eCS_ProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitECS_ProcedureCall([NotNull] VBScriptParser.ECS_ProcedureCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.eCS_MemberProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterECS_MemberProcedureCall([NotNull] VBScriptParser.ECS_MemberProcedureCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.eCS_MemberProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitECS_MemberProcedureCall([NotNull] VBScriptParser.ECS_MemberProcedureCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.implicitCallStmt_InBlock"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterImplicitCallStmt_InBlock([NotNull] VBScriptParser.ImplicitCallStmt_InBlockContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.implicitCallStmt_InBlock"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitImplicitCallStmt_InBlock([NotNull] VBScriptParser.ImplicitCallStmt_InBlockContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.noParenthesisArgs"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterNoParenthesisArgs([NotNull] VBScriptParser.NoParenthesisArgsContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.noParenthesisArgs"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitNoParenthesisArgs([NotNull] VBScriptParser.NoParenthesisArgsContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.iCS_B_ProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterICS_B_ProcedureCall([NotNull] VBScriptParser.ICS_B_ProcedureCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.iCS_B_ProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitICS_B_ProcedureCall([NotNull] VBScriptParser.ICS_B_ProcedureCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.iCS_B_MemberProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterICS_B_MemberProcedureCall([NotNull] VBScriptParser.ICS_B_MemberProcedureCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.iCS_B_MemberProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitICS_B_MemberProcedureCall([NotNull] VBScriptParser.ICS_B_MemberProcedureCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.implicitCallStmt_InStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterImplicitCallStmt_InStmt([NotNull] VBScriptParser.ImplicitCallStmt_InStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.implicitCallStmt_InStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitImplicitCallStmt_InStmt([NotNull] VBScriptParser.ImplicitCallStmt_InStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.iCS_S_VariableOrProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterICS_S_VariableOrProcedureCall([NotNull] VBScriptParser.ICS_S_VariableOrProcedureCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.iCS_S_VariableOrProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitICS_S_VariableOrProcedureCall([NotNull] VBScriptParser.ICS_S_VariableOrProcedureCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.iCS_S_ProcedureOrArrayCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterICS_S_ProcedureOrArrayCall([NotNull] VBScriptParser.ICS_S_ProcedureOrArrayCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.iCS_S_ProcedureOrArrayCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitICS_S_ProcedureOrArrayCall([NotNull] VBScriptParser.ICS_S_ProcedureOrArrayCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.iCS_S_NestedProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterICS_S_NestedProcedureCall([NotNull] VBScriptParser.ICS_S_NestedProcedureCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.iCS_S_NestedProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitICS_S_NestedProcedureCall([NotNull] VBScriptParser.ICS_S_NestedProcedureCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.iCS_S_MembersCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterICS_S_MembersCall([NotNull] VBScriptParser.ICS_S_MembersCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.iCS_S_MembersCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitICS_S_MembersCall([NotNull] VBScriptParser.ICS_S_MembersCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.iCS_S_MemberCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterICS_S_MemberCall([NotNull] VBScriptParser.ICS_S_MemberCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.iCS_S_MemberCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitICS_S_MemberCall([NotNull] VBScriptParser.ICS_S_MemberCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.iCS_S_DefaultMemberAccess"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterICS_S_DefaultMemberAccess([NotNull] VBScriptParser.ICS_S_DefaultMemberAccessContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.iCS_S_DefaultMemberAccess"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitICS_S_DefaultMemberAccess([NotNull] VBScriptParser.ICS_S_DefaultMemberAccessContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.argsCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterArgsCall([NotNull] VBScriptParser.ArgsCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.argsCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitArgsCall([NotNull] VBScriptParser.ArgsCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.argCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterArgCall([NotNull] VBScriptParser.ArgCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.argCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitArgCall([NotNull] VBScriptParser.ArgCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.defaultMemberAccess"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterDefaultMemberAccess([NotNull] VBScriptParser.DefaultMemberAccessContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.defaultMemberAccess"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitDefaultMemberAccess([NotNull] VBScriptParser.DefaultMemberAccessContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.argList"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterArgList([NotNull] VBScriptParser.ArgListContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.argList"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitArgList([NotNull] VBScriptParser.ArgListContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.arg"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterArg([NotNull] VBScriptParser.ArgContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.arg"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitArg([NotNull] VBScriptParser.ArgContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.argDefaultValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterArgDefaultValue([NotNull] VBScriptParser.ArgDefaultValueContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.argDefaultValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitArgDefaultValue([NotNull] VBScriptParser.ArgDefaultValueContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.subscripts"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSubscripts([NotNull] VBScriptParser.SubscriptsContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.subscripts"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSubscripts([NotNull] VBScriptParser.SubscriptsContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.subscript"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSubscript([NotNull] VBScriptParser.SubscriptContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.subscript"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSubscript([NotNull] VBScriptParser.SubscriptContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.ambiguousIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterAmbiguousIdentifier([NotNull] VBScriptParser.AmbiguousIdentifierContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.ambiguousIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitAmbiguousIdentifier([NotNull] VBScriptParser.AmbiguousIdentifierContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.asTypeClause"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterAsTypeClause([NotNull] VBScriptParser.AsTypeClauseContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.asTypeClause"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitAsTypeClause([NotNull] VBScriptParser.AsTypeClauseContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.baseType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterBaseType([NotNull] VBScriptParser.BaseTypeContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.baseType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitBaseType([NotNull] VBScriptParser.BaseTypeContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.certainIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCertainIdentifier([NotNull] VBScriptParser.CertainIdentifierContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.certainIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCertainIdentifier([NotNull] VBScriptParser.CertainIdentifierContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.comparisonOperator"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterComparisonOperator([NotNull] VBScriptParser.ComparisonOperatorContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.comparisonOperator"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitComparisonOperator([NotNull] VBScriptParser.ComparisonOperatorContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.complexType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterComplexType([NotNull] VBScriptParser.ComplexTypeContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.complexType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitComplexType([NotNull] VBScriptParser.ComplexTypeContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.fieldLength"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterFieldLength([NotNull] VBScriptParser.FieldLengthContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.fieldLength"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitFieldLength([NotNull] VBScriptParser.FieldLengthContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.letterrange"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLetterrange([NotNull] VBScriptParser.LetterrangeContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.letterrange"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLetterrange([NotNull] VBScriptParser.LetterrangeContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>ltColor</c>
	/// labeled alternative in <see cref="VBScriptParser.delimitedLiteral"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLtColor([NotNull] VBScriptParser.LtColorContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>ltColor</c>
	/// labeled alternative in <see cref="VBScriptParser.delimitedLiteral"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLtColor([NotNull] VBScriptParser.LtColorContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>ltOctal</c>
	/// labeled alternative in <see cref="VBScriptParser.delimitedLiteral"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLtOctal([NotNull] VBScriptParser.LtOctalContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>ltOctal</c>
	/// labeled alternative in <see cref="VBScriptParser.delimitedLiteral"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLtOctal([NotNull] VBScriptParser.LtOctalContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>ltDate</c>
	/// labeled alternative in <see cref="VBScriptParser.delimitedLiteral"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLtDate([NotNull] VBScriptParser.LtDateContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>ltDate</c>
	/// labeled alternative in <see cref="VBScriptParser.delimitedLiteral"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLtDate([NotNull] VBScriptParser.LtDateContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>ltString</c>
	/// labeled alternative in <see cref="VBScriptParser.delimitedLiteral"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLtString([NotNull] VBScriptParser.LtStringContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>ltString</c>
	/// labeled alternative in <see cref="VBScriptParser.delimitedLiteral"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLtString([NotNull] VBScriptParser.LtStringContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>ltDouble</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLtDouble([NotNull] VBScriptParser.LtDoubleContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>ltDouble</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLtDouble([NotNull] VBScriptParser.LtDoubleContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>ltDelimited</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLtDelimited([NotNull] VBScriptParser.LtDelimitedContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>ltDelimited</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLtDelimited([NotNull] VBScriptParser.LtDelimitedContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>ltFilenumber</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLtFilenumber([NotNull] VBScriptParser.LtFilenumberContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>ltFilenumber</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLtFilenumber([NotNull] VBScriptParser.LtFilenumberContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>ltInteger</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLtInteger([NotNull] VBScriptParser.LtIntegerContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>ltInteger</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLtInteger([NotNull] VBScriptParser.LtIntegerContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>ltBoolean</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLtBoolean([NotNull] VBScriptParser.LtBooleanContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>ltBoolean</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLtBoolean([NotNull] VBScriptParser.LtBooleanContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>ltNothing</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLtNothing([NotNull] VBScriptParser.LtNothingContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>ltNothing</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLtNothing([NotNull] VBScriptParser.LtNothingContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>ltNull</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLtNull([NotNull] VBScriptParser.LtNullContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>ltNull</c>
	/// labeled alternative in <see cref="VBScriptParser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLtNull([NotNull] VBScriptParser.LtNullContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.publicPrivateVisibility"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterPublicPrivateVisibility([NotNull] VBScriptParser.PublicPrivateVisibilityContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.publicPrivateVisibility"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitPublicPrivateVisibility([NotNull] VBScriptParser.PublicPrivateVisibilityContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.publicPrivateGlobalVisibility"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterPublicPrivateGlobalVisibility([NotNull] VBScriptParser.PublicPrivateGlobalVisibilityContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.publicPrivateGlobalVisibility"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitPublicPrivateGlobalVisibility([NotNull] VBScriptParser.PublicPrivateGlobalVisibilityContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.type"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterType([NotNull] VBScriptParser.TypeContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.type"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitType([NotNull] VBScriptParser.TypeContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.typeHint"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterTypeHint([NotNull] VBScriptParser.TypeHintContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.typeHint"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitTypeHint([NotNull] VBScriptParser.TypeHintContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.visibility"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVisibility([NotNull] VBScriptParser.VisibilityContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.visibility"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVisibility([NotNull] VBScriptParser.VisibilityContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VBScriptParser.ambiguousKeyword"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterAmbiguousKeyword([NotNull] VBScriptParser.AmbiguousKeywordContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VBScriptParser.ambiguousKeyword"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitAmbiguousKeyword([NotNull] VBScriptParser.AmbiguousKeywordContext context);
}
