{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:8000/", "sslPort": 0}}, "profiles": {"IIS": {"commandName": "IIS", "launchBrowser": true, "launchUrl": "https://localhost/WebApplication1", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "http://*:8000"}, "applicationUrl": "http://localhost:5003/", "httpPort": 5003}, "WSL": {"commandName": "WSL2", "distributionName": ""}}}