ABS=1
ANY=2
ARRAY=3
CBOOL=4
CBYTE=5
CCUR=6
CDATE=7
CDBL=8
CDEC=9
CINT=10
CIRCLE=11
CLNG=12
CLNGLNG=13
CLNGPTR=14
CSNG=15
CSTR=16
CURRENCY=17
CVAR=18
CVERR=19
DEBUG=20
DOEVENTS=21
EXIT=22
FIX=23
INPUTB=24
INT=25
LBOUND=26
LEN=27
LENB=28
LONGLONG=29
LONGPTR=30
MIDB=31
OPTION=32
PSET=33
SCALE=34
SGN=35
UBOUND=36
COMMA=37
COLON=38
SEMICOLON=39
EXCLAMATIONPOINT=40
DOT=41
HASH=42
AT=43
PERCENT=44
DOLLAR=45
AMPERSAND=46
ACCESS=47
ADDRESSOF=48
ALIAS=49
AND=50
ATTRIBUTE=51
APPEND=52
AS=53
BEGINPROPERTY=54
BEGIN=55
BINARY=56
BOOLEAN=57
BYVAL=58
BYREF=59
BYTE=60
CALL=61
CASE=62
CDECL=63
CLASS=64
CLOSE=65
CONST=66
DATABASE=67
DATE=68
DECLARE=69
DEFBOOL=70
DEFBYTE=71
DEFDATE=72
DEFDBL=73
DEFCUR=74
DEFINT=75
DEFLNG=76
DEFLNGLNG=77
DEFLNGPTR=78
DEFOBJ=79
DEFSNG=80
DEFSTR=81
DEFVAR=82
DIM=83
DO=84
DOUBLE=85
EACH=86
ELSE=87
ELSEIF=88
EMPTY=89
END_ENUM=90
END_FUNCTION=91
END_IF=92
ENDPROPERTY=93
END_PROPERTY=94
END_SELECT=95
END_SUB=96
END_TYPE=97
END_WITH=98
END=99
ENUM=100
EQV=101
ERASE=102
ERROR=103
EVENT=104
EXIT_DO=105
EXIT_FOR=106
EXIT_FUNCTION=107
EXIT_PROPERTY=108
EXIT_SUB=109
FALSE=110
FRIEND=111
FOR=112
FUNCTION=113
GET=114
GLOBAL=115
GOSUB=116
GOTO=117
IF=118
IMP=119
IMPLEMENTS=120
IN=121
INPUT=122
IS=123
INTEGER=124
LOCK=125
LONG=126
LOOP=127
LET=128
LIB=129
LIKE=130
LINE_INPUT=131
LOCK_READ=132
LOCK_WRITE=133
LOCK_READ_WRITE=134
LSET=135
ME=136
MID=137
MOD=138
NAME=139
NEXT=140
NEW=141
NOT=142
NOTHING=143
NULL=144
OBJECT=145
ON=146
ON_ERROR=147
ON_LOCAL_ERROR=148
OPEN=149
OPTIONAL=150
OPTION_BASE=151
OPTION_EXPLICIT=152
OPTION_COMPARE=153
OPTION_PRIVATE_MODULE=154
OR=155
OUTPUT=156
PARAMARRAY=157
PRESERVE=158
PRINT=159
PRIVATE=160
PROPERTY_GET=161
PROPERTY_LET=162
PROPERTY_SET=163
PTRSAFE=164
PUBLIC=165
PUT=166
RANDOM=167
RANDOMIZE=168
RAISEEVENT=169
READ=170
READ_WRITE=171
REDIM=172
REM=173
RESET=174
RESUME=175
RETURN=176
RSET=177
SEEK=178
SELECT=179
SET=180
SHARED=181
SINGLE=182
SPC=183
STATIC=184
STEP=185
STOP=186
STRING=187
SUB=188
TAB=189
TEXT=190
THEN=191
TO=192
TRUE=193
TYPE=194
TYPEOF=195
UNLOCK=196
UNTIL=197
VARIANT=198
VERSION=199
WEND=200
WHILE=201
WIDTH=202
WITH=203
WITHEVENTS=204
WRITE=205
XOR=206
ASSIGN=207
DIV=208
INTDIV=209
EQ=210
GEQ=211
GT=212
LEQ=213
LPAREN=214
LT=215
MINUS=216
MULT=217
NEQ=218
PLUS=219
POW=220
RPAREN=221
L_SQUARE_BRACKET=222
R_SQUARE_BRACKET=223
L_BRACE=224
R_BRACE=225
STRINGLITERAL=226
OCTLITERAL=227
HEXLITERAL=228
FLOATLITERAL=229
INTEGERLITERAL=230
DATELITERAL=231
NEWLINE=232
SINGLEQUOTE=233
UNDERSCORE=234
WS=235
GUIDLITERAL=236
IDENTIFIER=237
LINE_CONTINUATION=238
BARE_HEX_LITERAL=239
ERRORCHAR=240
','=37
':'=38
';'=39
'!'=40
'.'=41
'#'=42
'@'=43
'%'=44
'$'=45
'&'=46
':='=207
'/'=208
'\\'=209
'='=210
'>'=212
'('=214
'<'=215
'-'=216
'*'=217
'+'=219
'^'=220
')'=221
'['=222
']'=223
'{'=224
'}'=225
'\''=233
'_'=234
