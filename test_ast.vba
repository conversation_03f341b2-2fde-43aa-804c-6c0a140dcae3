Option Explicit

Public Function TestFunction(x As Integer, y As String) As String
    Dim result As String
    Dim i As Integer
    
    result = "Hello " & y
    
    For i = 1 To x
        result = result & " " & CStr(i)
    Next i
    
    If Len(result) > 20 Then
        result = Left(result, 20) & "..."
    End If
    
    TestFunction = result
End Function

Private Sub TestSubroutine()
    Dim msg As String
    Dim count As Integer
    
    count = 5
    msg = TestFunction(count, "World")
    
    Select Case count
        Case 1 To 3
            MsgBox "Small count: " & msg
        Case 4 To 6
            MsgBox "Medium count: " & msg
        Case Else
            MsgBox "Large count: " & msg
    End Select
End Sub

Public Property Get CurrentTime() As Date
    CurrentTime = Now()
End Property

Public Property Let CurrentValue(value As Integer)
    ' Property setter implementation
End Property
