using System.Collections.Generic;

namespace VbDeobf.AST
{
    /// <summary>
    /// Module node - represents a complete VBA module
    /// </summary>
    public class ModuleNode : ASTNodeBase
    {
        public override ASTNodeType NodeType => ASTNodeType.Module;
        public ModuleHeaderNode? Header { get; set; }
        public ModuleDeclarationsNode Declarations { get; set; }
        public ModuleBodyNode Body { get; set; }
        public List<AttributeNode> Attributes { get; set; } = new List<AttributeNode>();
        public string? FileName { get; set; }
        
        public ModuleNode(ModuleDeclarationsNode declarations, ModuleBodyNode body, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            Declarations = declarations;
            Body = body;
            AddChild(declarations);
            AddChild(body);
        }
        
        public void SetHeader(ModuleHeaderNode header)
        {
            Header = header;
            AddChild(header);
        }
        
        public void AddAttribute(AttributeNode attribute)
        {
            Attributes.Add(attribute);
            AddChild(attribute);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitModule(this);
    }
    
    /// <summary>
    /// Module header node - contains version and class information
    /// </summary>
    public class ModuleHeaderNode : ASTNodeBase
    {
        public override ASTNodeType NodeType => ASTNodeType.ModuleHeader;
        public string Version { get; set; } = "1.0";
        public bool IsClass { get; set; }
        
        public ModuleHeaderNode(string version = "1.0", bool isClass = false, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            Version = version;
            IsClass = isClass;
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitModuleHeader(this);
    }
    
    /// <summary>
    /// Module declarations node - contains all module-level declarations
    /// </summary>
    public class ModuleDeclarationsNode : ASTNodeBase
    {
        public override ASTNodeType NodeType => ASTNodeType.ModuleDeclarations;
        public List<DeclarationNode> Declarations { get; set; } = new List<DeclarationNode>();
        public List<ModuleOptionNode> Options { get; set; } = new List<ModuleOptionNode>();
        public List<DeclareStatementNode> DeclareStatements { get; set; } = new List<DeclareStatementNode>();
        
        public ModuleDeclarationsNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }
        
        public void AddDeclaration(DeclarationNode declaration)
        {
            Declarations.Add(declaration);
            AddChild(declaration);
        }
        
        public void AddOption(ModuleOptionNode option)
        {
            Options.Add(option);
            AddChild(option);
        }
        
        public void AddDeclareStatement(DeclareStatementNode declareStatement)
        {
            DeclareStatements.Add(declareStatement);
            AddChild(declareStatement);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitModuleDeclarations(this);
    }
    
    /// <summary>
    /// Module body node - contains all executable code
    /// </summary>
    public class ModuleBodyNode : ASTNodeBase
    {
        public override ASTNodeType NodeType => ASTNodeType.ModuleBody;
        public List<DeclarationNode> Procedures { get; set; } = new List<DeclarationNode>();
        
        public ModuleBodyNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }
        
        public void AddProcedure(DeclarationNode procedure)
        {
            Procedures.Add(procedure);
            AddChild(procedure);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitModuleBody(this);
    }
    
    /// <summary>
    /// Module option node (Option Explicit, Option Base, etc.)
    /// </summary>
    public class ModuleOptionNode : ASTNodeBase
    {
        public override ASTNodeType NodeType => ASTNodeType.Identifier; // Reusing identifier type
        public ModuleOptionType OptionType { get; set; }
        public string? Value { get; set; }
        
        public ModuleOptionNode(ModuleOptionType optionType, string? value = null, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            OptionType = optionType;
            Value = value;
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitIdentifier(new IdentifierNode($"Option{OptionType}", SourceLocation));
    }
    
    /// <summary>
    /// Declare statement node for external function declarations
    /// </summary>
    public class DeclareStatementNode : DeclarationNode
    {
        public override ASTNodeType NodeType => ASTNodeType.FunctionDeclaration; // Reusing function declaration type
        public bool IsFunction { get; set; } // true for Function, false for Sub
        public bool IsPtrSafe { get; set; }
        public string LibraryName { get; set; }
        public string? Alias { get; set; }
        public ParameterListNode Parameters { get; set; }
        public TypeNode? ReturnType { get; set; }
        public bool IsCDecl { get; set; }
        
        public DeclareStatementNode(string name, bool isFunction, string libraryName, ParameterListNode parameters, 
            TypeNode? returnType = null, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            Name = name;
            IsFunction = isFunction;
            LibraryName = libraryName;
            Parameters = parameters;
            ReturnType = returnType;
            AddChild(parameters);
            if (returnType != null) AddChild(returnType);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitFunctionDeclaration(
            new FunctionDeclarationNode(Name, Parameters, new BlockNode(), ReturnType, SourceLocation));
    }
    
    /// <summary>
    /// Module configuration reference node
    /// </summary>
    public class ModuleConfigReferenceNode : ASTNodeBase
    {
        public override ASTNodeType NodeType => ASTNodeType.Identifier; // Reusing identifier type
        public string ObjectName { get; set; }
        public string ObjectValue { get; set; }
        public string ComponentValue { get; set; }
        
        public ModuleConfigReferenceNode(string objectName, string objectValue, string componentValue, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            ObjectName = objectName;
            ObjectValue = objectValue;
            ComponentValue = componentValue;
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitIdentifier(new IdentifierNode(ObjectName, SourceLocation));
    }
    
    /// <summary>
    /// Module configuration property node
    /// </summary>
    public class ModuleConfigPropertyNode : ASTNodeBase
    {
        public override ASTNodeType NodeType => ASTNodeType.Identifier; // Reusing identifier type
        public string PropertyName { get; set; }
        public string PropertyValue { get; set; }
        
        public ModuleConfigPropertyNode(string propertyName, string propertyValue, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            PropertyName = propertyName;
            PropertyValue = propertyValue;
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitIdentifier(new IdentifierNode(PropertyName, SourceLocation));
    }
    
    /// <summary>
    /// Implements statement node
    /// </summary>
    public class ImplementsStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.CallStatement; // Reusing call statement type
        public ExpressionNode InterfaceName { get; set; }
        
        public ImplementsStatementNode(ExpressionNode interfaceName, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            InterfaceName = interfaceName;
            AddChild(interfaceName);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitCallStatement(new CallStatementNode(InterfaceName, false, SourceLocation));
    }
    
    /// <summary>
    /// DefType statement node (DefInt, DefStr, etc.)
    /// </summary>
    public class DefTypeStatementNode : StatementNode
    {
        public override ASTNodeType NodeType => ASTNodeType.CallStatement; // Reusing call statement type
        public DefTypeType DefType { get; set; }
        public List<LetterRangeNode> LetterRanges { get; set; } = new List<LetterRangeNode>();
        
        public DefTypeStatementNode(DefTypeType defType, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            DefType = defType;
        }
        
        public void AddLetterRange(LetterRangeNode letterRange)
        {
            LetterRanges.Add(letterRange);
            AddChild(letterRange);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitCallStatement(
            new CallStatementNode(new IdentifierExpressionNode($"Def{DefType}"), false, SourceLocation));
    }
    
    /// <summary>
    /// Letter range node for DefType statements
    /// </summary>
    public class LetterRangeNode : ASTNodeBase
    {
        public override ASTNodeType NodeType => ASTNodeType.Identifier; // Reusing identifier type
        public char StartLetter { get; set; }
        public char? EndLetter { get; set; }
        
        public LetterRangeNode(char startLetter, char? endLetter = null, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            StartLetter = startLetter;
            EndLetter = endLetter;
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitIdentifier(
            new IdentifierNode(EndLetter.HasValue ? $"{StartLetter}-{EndLetter}" : StartLetter.ToString(), SourceLocation));
    }
    
    /// <summary>
    /// Module option types
    /// </summary>
    public enum ModuleOptionType
    {
        Explicit,
        Base,
        Compare,
        Private
    }
    
    /// <summary>
    /// DefType types
    /// </summary>
    public enum DefTypeType
    {
        Bool,
        Byte,
        Int,
        Lng,
        LngLng,
        LngPtr,
        Cur,
        Sng,
        Dbl,
        Date,
        Str,
        Obj,
        Var
    }
}
