name: Publish

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      logLevel:
        description: 'Log level'     
        required: true
        default: 'warning'
      tags:
        description: 'Test scenario tags'  
        
jobs:
  release:
    name: Release
    strategy:
      matrix:
        kind: ['linux', 'windows', 'macOS']
        include:
          - kind: linux
            os: ubuntu-latest
            target: linux-x64
          - kind: windows
            os: windows-latest
            target: win-x64
          - kind: macOS
            os: macos-latest
            target: osx-x64
    runs-on: ${{ matrix.os }}
    steps:
      - name: Checkout
        uses: actions/checkout@v1

      - name: Setup dotnet
        uses: actions/setup-dotnet@v1
        with:
          dotnet-version: 3.1.101

      - name: Build
        shell: bash
        run: |
          tag=$(git describe --tags --abbrev=0)
          release_name="vbSparkle-$tag-${{ matrix.target }}"

          # Build everything
          dotnet publish Sources/vbSparkle.Console/ --framework netcoreapp3.1 --runtime "${{ matrix.target }}" -c Release -o "$release_name"

          # Pack files
          if [ "${{ matrix.target }}" == "win-x64" ]; then
            # Pack to zip for Windows
            7z a -tzip "${release_name}.zip" "./${release_name}/*"
          else
          tar czvf "${release_name}.tar.gz" "$release_name"
          fi

          # Delete output directory
          rm -r "$release_name"

      - name: Publish
        uses: softprops/action-gh-release@v1
        with:
          files: "vbSparkle-*"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
