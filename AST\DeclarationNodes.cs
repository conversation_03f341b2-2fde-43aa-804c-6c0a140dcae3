using System.Collections.Generic;

namespace VbDeobf.AST
{
    /// <summary>
    /// Function declaration node
    /// </summary>
    public class FunctionDeclarationNode : DeclarationNode
    {
        public override ASTNodeType NodeType => ASTNodeType.FunctionDeclaration;
        public ParameterListNode Parameters { get; set; }
        public TypeNode? ReturnType { get; set; }
        public BlockNode Body { get; set; }
        public bool IsStatic { get; set; }
        public AttributeListNode? Attributes { get; set; }
        
        public FunctionDeclarationNode(string name, ParameterListNode parameters, BlockNode body, 
            TypeNode? returnType = null, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            Name = name;
            Parameters = parameters;
            Body = body;
            ReturnType = returnType;
            AddChild(parameters);
            AddChild(body);
            if (returnType != null) AddChild(returnType);
        }
        
        public void SetAttributes(AttributeListNode attributes)
        {
            Attributes = attributes;
            AddChild(attributes);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitFunctionDeclaration(this);
    }
    
    /// <summary>
    /// Subroutine declaration node
    /// </summary>
    public class SubroutineDeclarationNode : DeclarationNode
    {
        public override ASTNodeType NodeType => ASTNodeType.SubroutineDeclaration;
        public ParameterListNode Parameters { get; set; }
        public BlockNode Body { get; set; }
        public bool IsStatic { get; set; }
        public AttributeListNode? Attributes { get; set; }
        
        public SubroutineDeclarationNode(string name, ParameterListNode parameters, BlockNode body, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            Name = name;
            Parameters = parameters;
            Body = body;
            AddChild(parameters);
            AddChild(body);
        }
        
        public void SetAttributes(AttributeListNode attributes)
        {
            Attributes = attributes;
            AddChild(attributes);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitSubroutineDeclaration(this);
    }
    
    /// <summary>
    /// Property declaration node
    /// </summary>
    public class PropertyDeclarationNode : DeclarationNode
    {
        public override ASTNodeType NodeType => ASTNodeType.PropertyDeclaration;
        public PropertyType PropertyType { get; set; }
        public ParameterListNode Parameters { get; set; }
        public TypeNode? ReturnType { get; set; }
        public BlockNode Body { get; set; }
        public AttributeListNode? Attributes { get; set; }
        
        public PropertyDeclarationNode(string name, PropertyType propertyType, ParameterListNode parameters, 
            BlockNode body, TypeNode? returnType = null, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            Name = name;
            PropertyType = propertyType;
            Parameters = parameters;
            Body = body;
            ReturnType = returnType;
            AddChild(parameters);
            AddChild(body);
            if (returnType != null) AddChild(returnType);
        }
        
        public void SetAttributes(AttributeListNode attributes)
        {
            Attributes = attributes;
            AddChild(attributes);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitPropertyDeclaration(this);
    }
    
    /// <summary>
    /// Variable declaration node
    /// </summary>
    public class VariableDeclarationNode : DeclarationNode
    {
        public override ASTNodeType NodeType => ASTNodeType.VariableDeclaration;
        public TypeNode? Type { get; set; }
        public ExpressionNode? Initializer { get; set; }
        public bool IsWithEvents { get; set; }
        public bool IsStatic { get; set; }
        public ArrayDimensionsNode? ArrayDimensions { get; set; }
        
        public VariableDeclarationNode(string name, TypeNode? type = null, ExpressionNode? initializer = null, 
            SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            Name = name;
            Type = type;
            Initializer = initializer;
            if (type != null) AddChild(type);
            if (initializer != null) AddChild(initializer);
        }
        
        public void SetArrayDimensions(ArrayDimensionsNode dimensions)
        {
            ArrayDimensions = dimensions;
            AddChild(dimensions);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitVariableDeclaration(this);
    }
    
    /// <summary>
    /// Constant declaration node
    /// </summary>
    public class ConstantDeclarationNode : DeclarationNode
    {
        public override ASTNodeType NodeType => ASTNodeType.ConstantDeclaration;
        public TypeNode? Type { get; set; }
        public ExpressionNode Value { get; set; }
        
        public ConstantDeclarationNode(string name, ExpressionNode value, TypeNode? type = null, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            Name = name;
            Value = value;
            Type = type;
            AddChild(value);
            if (type != null) AddChild(type);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitConstantDeclaration(this);
    }
    
    /// <summary>
    /// Type declaration node
    /// </summary>
    public class TypeDeclarationNode : DeclarationNode
    {
        public override ASTNodeType NodeType => ASTNodeType.TypeDeclaration;
        public List<VariableDeclarationNode> Members { get; set; } = new List<VariableDeclarationNode>();
        
        public TypeDeclarationNode(string name, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            Name = name;
        }
        
        public void AddMember(VariableDeclarationNode member)
        {
            Members.Add(member);
            AddChild(member);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitTypeDeclaration(this);
    }
    
    /// <summary>
    /// Enum declaration node
    /// </summary>
    public class EnumDeclarationNode : DeclarationNode
    {
        public override ASTNodeType NodeType => ASTNodeType.EnumDeclaration;
        public List<EnumMemberNode> Members { get; set; } = new List<EnumMemberNode>();
        
        public EnumDeclarationNode(string name, SourceLocation? sourceLocation = null) : base(sourceLocation)
        {
            Name = name;
        }
        
        public void AddMember(EnumMemberNode member)
        {
            Members.Add(member);
            AddChild(member);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitEnumDeclaration(this);
    }
    
    /// <summary>
    /// Event declaration node
    /// </summary>
    public class EventDeclarationNode : DeclarationNode
    {
        public override ASTNodeType NodeType => ASTNodeType.EventDeclaration;
        public ParameterListNode Parameters { get; set; }
        
        public EventDeclarationNode(string name, ParameterListNode parameters, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            Name = name;
            Parameters = parameters;
            AddChild(parameters);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitEventDeclaration(this);
    }
    
    /// <summary>
    /// Enum member node
    /// </summary>
    public class EnumMemberNode : ASTNodeBase
    {
        public override ASTNodeType NodeType => ASTNodeType.Identifier; // Reusing identifier type
        public string Name { get; set; }
        public ExpressionNode? Value { get; set; }
        
        public EnumMemberNode(string name, ExpressionNode? value = null, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            Name = name;
            Value = value;
            if (value != null) AddChild(value);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitIdentifier(new IdentifierNode(Name, SourceLocation));
    }
    
    /// <summary>
    /// Array dimensions node
    /// </summary>
    public class ArrayDimensionsNode : ASTNodeBase
    {
        public override ASTNodeType NodeType => ASTNodeType.ArrayType;
        public List<ArrayDimensionNode> Dimensions { get; set; } = new List<ArrayDimensionNode>();
        
        public ArrayDimensionsNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }
        
        public void AddDimension(ArrayDimensionNode dimension)
        {
            Dimensions.Add(dimension);
            AddChild(dimension);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitArrayType(new ArrayTypeNode(null, SourceLocation));
    }
    
    /// <summary>
    /// Array dimension node
    /// </summary>
    public class ArrayDimensionNode : ASTNodeBase
    {
        public override ASTNodeType NodeType => ASTNodeType.Identifier; // Reusing identifier type
        public ExpressionNode? LowerBound { get; set; }
        public ExpressionNode UpperBound { get; set; }
        
        public ArrayDimensionNode(ExpressionNode upperBound, ExpressionNode? lowerBound = null, SourceLocation? sourceLocation = null) 
            : base(sourceLocation)
        {
            UpperBound = upperBound;
            LowerBound = lowerBound;
            AddChild(upperBound);
            if (lowerBound != null) AddChild(lowerBound);
        }
        
        public override T Accept<T>(IASTVisitor<T> visitor) => visitor.VisitIdentifier(new IdentifierNode("ArrayDimension", SourceLocation));
    }
    
    /// <summary>
    /// Property types
    /// </summary>
    public enum PropertyType
    {
        Get,
        Let,
        Set
    }
}
